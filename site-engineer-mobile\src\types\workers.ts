// Worker-related types for Site Engineer Mobile Application

export interface Certification {
  id: string;
  name: string;
  issuedBy: string;
  issuedDate: Date;
  expiryDate: Date;
  certificateNumber: string;
  isValid: boolean;
}

export interface Training {
  id: string;
  name: string;
  completedDate: Date;
  expiryDate?: Date;
  provider: string;
  isValid: boolean;
}

export interface Worker {
  id: string;
  name: string;
  employeeId: string;
  trade: string;
  photo?: string;
  phone: string;
  email?: string;
  siteId: string;
  supervisorId: string; // Site engineer ID
  isActive: boolean;
  isOnSite: boolean;
  lastCheckIn?: Date;
  certifications: Certification[];
  trainings: Training[];
  hoursWorked: number;
  overtimeHours: number;
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
}

export interface WorkerSummary {
  id: string;
  name: string;
  trade: string;
  photo?: string;
  isOnSite: boolean;
  hoursWorked: number;
  overtimeHours: number;
  lastSeen: Date;
  status: 'on-site' | 'off-site' | 'on-break' | 'unavailable';
}

export interface OvertimeRequest {
  id: string;
  workerId: string;
  workerName: string;
  date: Date;
  hours: number;
  reason: string;
  description?: string;
  approvalStatus: 'pending' | 'approved' | 'rejected';
  submittedBy: string;
  submittedAt: Date;
  reviewedBy?: string;
  reviewedAt?: Date;
  reviewNotes?: string;
}

export interface WorkerAttendance {
  workerId: string;
  date: Date;
  checkInTime?: Date;
  checkOutTime?: Date;
  hoursWorked: number;
  overtimeHours: number;
  status: 'present' | 'absent' | 'late' | 'early-departure';
  notes?: string;
}

// Mock data for development
export const mockWorkers: Worker[] = [
  {
    id: 'worker-1',
    name: 'David Kamau',
    employeeId: 'EMP001',
    trade: 'Welder',
    photo: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150&h=150&fit=crop&crop=face',
    phone: '+254712345678',
    email: '<EMAIL>',
    siteId: 'site-1',
    supervisorId: 'engineer-1',
    isActive: true,
    isOnSite: true,
    lastCheckIn: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    certifications: [
      {
        id: 'cert-1',
        name: 'Welding Certificate Level 2',
        issuedBy: 'Kenya Institute of Welding',
        issuedDate: new Date('2023-01-15'),
        expiryDate: new Date('2025-01-15'),
        certificateNumber: 'WLD-2023-001',
        isValid: true
      }
    ],
    trainings: [
      {
        id: 'train-1',
        name: 'Hot Work Safety',
        completedDate: new Date('2023-06-10'),
        expiryDate: new Date('2024-06-10'),
        provider: 'Safety Training Institute',
        isValid: true
      }
    ],
    hoursWorked: 8,
    overtimeHours: 2,
    emergencyContact: {
      name: 'Mary Kamau',
      phone: '+254712345679',
      relationship: 'Spouse'
    }
  },
  {
    id: 'worker-2',
    name: 'Mary Wanjiku',
    employeeId: 'EMP002',
    trade: 'Electrician',
    photo: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150&h=150&fit=crop&crop=face',
    phone: '+254723456789',
    email: '<EMAIL>',
    siteId: 'site-1',
    supervisorId: 'engineer-1',
    isActive: true,
    isOnSite: true,
    lastCheckIn: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
    certifications: [
      {
        id: 'cert-2',
        name: 'Electrical Installation Certificate',
        issuedBy: 'Electrical Engineers Board',
        issuedDate: new Date('2022-08-20'),
        expiryDate: new Date('2025-08-20'),
        certificateNumber: 'ELC-2022-045',
        isValid: true
      }
    ],
    trainings: [
      {
        id: 'train-2',
        name: 'Electrical Safety Training',
        completedDate: new Date('2023-03-15'),
        expiryDate: new Date('2024-03-15'),
        provider: 'Electrical Safety Institute',
        isValid: true
      }
    ],
    hoursWorked: 8,
    overtimeHours: 0,
    emergencyContact: {
      name: 'John Wanjiku',
      phone: '+254723456790',
      relationship: 'Brother'
    }
  },
  {
    id: 'worker-3',
    name: 'Peter Ochieng',
    employeeId: 'EMP003',
    trade: 'Carpenter',
    photo: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150&h=150&fit=crop&crop=face',
    phone: '+254734567890',
    siteId: 'site-1',
    supervisorId: 'engineer-1',
    isActive: true,
    isOnSite: false,
    lastCheckIn: new Date(Date.now() - 18 * 60 * 60 * 1000), // 18 hours ago
    certifications: [
      {
        id: 'cert-3',
        name: 'Carpentry Trade Certificate',
        issuedBy: 'Kenya Technical Institute',
        issuedDate: new Date('2021-11-10'),
        expiryDate: new Date('2024-11-10'),
        certificateNumber: 'CRP-2021-078',
        isValid: true
      }
    ],
    trainings: [
      {
        id: 'train-3',
        name: 'Construction Safety',
        completedDate: new Date('2023-01-20'),
        expiryDate: new Date('2024-01-20'),
        provider: 'Construction Safety Board',
        isValid: true
      }
    ],
    hoursWorked: 7.5,
    overtimeHours: 1,
    emergencyContact: {
      name: 'Grace Ochieng',
      phone: '+254734567891',
      relationship: 'Wife'
    }
  },
  {
    id: 'worker-4',
    name: 'Grace Muthoni',
    employeeId: 'EMP004',
    trade: 'Mason',
    photo: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150&h=150&fit=crop&crop=face',
    phone: '+254745678901',
    siteId: 'site-1',
    supervisorId: 'engineer-1',
    isActive: true,
    isOnSite: true,
    lastCheckIn: new Date(Date.now() - 30 * 60 * 1000), // 30 minutes ago
    certifications: [
      {
        id: 'cert-4',
        name: 'Masonry Certificate',
        issuedBy: 'Building Construction Board',
        issuedDate: new Date('2022-05-15'),
        expiryDate: new Date('2025-05-15'),
        certificateNumber: 'MSN-2022-032',
        isValid: true
      }
    ],
    trainings: [
      {
        id: 'train-4',
        name: 'Working at Height',
        completedDate: new Date('2023-04-10'),
        expiryDate: new Date('2024-04-10'),
        provider: 'Height Safety Training',
        isValid: true
      }
    ],
    hoursWorked: 8,
    overtimeHours: 0,
    emergencyContact: {
      name: 'James Muthoni',
      phone: '+254745678902',
      relationship: 'Husband'
    }
  }
];

export const mockOvertimeRequests: OvertimeRequest[] = [
  {
    id: 'ot-1',
    workerId: 'worker-1',
    workerName: 'David Kamau',
    date: new Date(),
    hours: 2,
    reason: 'Critical welding work completion',
    description: 'Need to complete structural welding before concrete pour tomorrow',
    approvalStatus: 'pending',
    submittedBy: 'engineer-1',
    submittedAt: new Date(Date.now() - 2 * 60 * 60 * 1000)
  },
  {
    id: 'ot-2',
    workerId: 'worker-3',
    workerName: 'Peter Ochieng',
    date: new Date(Date.now() - 24 * 60 * 60 * 1000),
    hours: 1,
    reason: 'Formwork completion',
    approvalStatus: 'approved',
    submittedBy: 'engineer-1',
    submittedAt: new Date(Date.now() - 26 * 60 * 60 * 1000),
    reviewedBy: 'manager-1',
    reviewedAt: new Date(Date.now() - 24 * 60 * 60 * 1000),
    reviewNotes: 'Approved for critical path activity'
  }
];
