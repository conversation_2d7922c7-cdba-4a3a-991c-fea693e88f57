// Permit-related TypeScript interfaces

// Permit Status Types
export type PermitStatus =
	| "draft"
	| "pending-approval"
	| "approved"
	| "active"
	| "expired"
	| "closed"
	| "rejected"
	| "cancelled";

// Permit Priority Types
export type PermitPriority = "low" | "medium" | "high" | "critical";

// Permit Type
export interface PermitType {
	id: string;
	name: string;
	description: string;
	category: string;
	defaultValidityHours: number;
	requiredTrainings: string[];
	requiredCertifications: string[];
	riskLevel: "low" | "medium" | "high" | "critical";
	template: PermitTemplate;
	isActive: boolean;
	createdAt: Date;
	updatedAt: Date;
}

// Permit Template Structure
export interface PermitTemplate {
	id: string;
	sections: PermitTemplateSection[];
	approvalWorkflow: ApprovalStep[];
}

export interface PermitTemplateSection {
	id: string;
	title: string;
	description?: string;
	fields: PermitTemplateField[];
	order: number;
	isRequired: boolean;
}

export interface PermitTemplateField {
	id: string;
	type:
		| "text"
		| "textarea"
		| "checkbox"
		| "radio"
		| "select"
		| "date"
		| "time"
		| "signature"
		| "file";
	label: string;
	placeholder?: string;
	required: boolean;
	options?: string[];
	validation?: {
		min?: number;
		max?: number;
		pattern?: string;
		message?: string;
	};
	order: number;
}

// Approval Workflow
export interface ApprovalStep {
	id: string;
	role: string;
	title: string;
	description: string;
	order: number;
	isRequired: boolean;
	canSkip: boolean;
}

// Main Permit Interface
export interface Permit {
	id: string;
	permitNumber: string;
	permitType: PermitType;
	title: string;
	description: string;
	location: string;
	siteId: string;
	taskId?: string;

	// Timing
	requestedDate: Date;
	validFrom?: Date;
	validUntil?: Date;
	actualStartTime?: Date;
	actualEndTime?: Date;

	// Status and Priority
	status: PermitStatus;
	priority: PermitPriority;

	// People
	requestedBy: string;
	requestedByName: string;
	assignedWorkers: PermitWorker[];
	approvals: PermitApproval[];

	// Risk Assessment
	riskAssessment: RiskAssessment;
	dailyRiskAssessments: DailyRiskAssessment[];

	// Form Data
	formData: Record<string, any>;

	// Documents
	attachments: PermitAttachment[];

	// Audit Trail
	createdAt: Date;
	updatedAt: Date;
	history: PermitHistoryEntry[];
}

// Worker Assignment
export interface PermitWorker {
	workerId: string;
	workerName: string;
	workerPhoto?: string;
	primaryTrade: string;
	role: "supervisor" | "worker" | "safety-observer";
	hasRequiredTraining: boolean;
	hasRequiredCertifications: boolean;
	acknowledgedAt?: Date;
}

// Approval Record
export interface PermitApproval {
	id: string;
	stepId: string;
	approverRole: string;
	approverId: string;
	approverName: string;
	status: "pending" | "approved" | "rejected";
	approvedAt?: Date;
	comments?: string;
	signature?: string;
}

// Risk Assessment
export interface RiskAssessment {
	id: string;
	hazards: Hazard[];
	controlMeasures: ControlMeasure[];
	overallRiskLevel: "low" | "medium" | "high" | "critical";
	assessedBy: string;
	assessedAt: Date;
	reviewedBy?: string;
	reviewedAt?: Date;
}

export interface Hazard {
	id: string;
	description: string;
	category: string;
	likelihood: "rare" | "unlikely" | "possible" | "likely" | "certain";
	consequence:
		| "insignificant"
		| "minor"
		| "moderate"
		| "major"
		| "catastrophic";
	riskLevel: "low" | "medium" | "high" | "critical";
}

export interface ControlMeasure {
	id: string;
	hazardId: string;
	description: string;
	type:
		| "elimination"
		| "substitution"
		| "engineering"
		| "administrative"
		| "ppe";
	responsibility: string;
	isImplemented: boolean;
	verifiedBy?: string;
	verifiedAt?: Date;
}

// Daily Risk Assessment
export interface DailyRiskAssessment {
	id: string;
	permitId: string;
	date: Date;
	assessedBy: string;
	assessedByName: string;
	conditions: EnvironmentalCondition[];
	additionalHazards: Hazard[];
	controlMeasuresVerified: boolean;
	safeToWork: boolean;
	comments?: string;
	createdAt: Date;
}

export interface EnvironmentalCondition {
	factor: "weather" | "visibility" | "noise" | "temperature" | "wind" | "other";
	status: "acceptable" | "marginal" | "unacceptable";
	description: string;
	impact: string;
}

// Permit Attachment
export interface PermitAttachment {
	id: string;
	name: string;
	type: "document" | "image" | "video" | "other";
	url: string;
	size: number;
	uploadedBy: string;
	uploadedAt: Date;
}

// Permit History
export interface PermitHistoryEntry {
	id: string;
	action:
		| "created"
		| "updated"
		| "approved"
		| "rejected"
		| "activated"
		| "closed"
		| "cancelled"
		| "extended";
	description: string;
	performedBy: string;
	performedByName: string;
	timestamp: Date;
	details?: Record<string, any>;
}

// Permit Statistics
export interface PermitStats {
	totalPermits: number;
	activePermits: number;
	pendingApproval: number;
	expiringSoon: number;
	expired: number;
	closedToday: number;
	averageApprovalTime: number; // in hours
}

// Permit Filters
export interface PermitFilters {
	search: string;
	status: PermitStatus | "all";
	permitType: string;
	priority: PermitPriority | "all";
	assignedWorker: string;
	dateRange: {
		start?: Date;
		end?: Date;
	};
	location: string;
}

// Permit Report Data
export interface PermitReportData {
	title: string;
	description: string;
	generatedAt: Date;
	chartData: any;
	chartType: "bar" | "line" | "pie" | "doughnut";
	tableData: any[];
	columns: {
		key: string;
		label: string;
	}[];
}

// Permit Extension Request
export interface PermitExtension {
	id: string;
	permitId: string;
	requestedBy: string;
	requestedByName: string;
	currentValidUntil: Date;
	requestedValidUntil: Date;
	reason: string;
	justification: string;
	status: "pending" | "approved" | "rejected";
	approvedBy?: string;
	approvedAt?: Date;
	comments?: string;
	createdAt: Date;
}

// Permit Violation
export interface PermitViolation {
	id: string;
	permitId: string;
	violationType:
		| "expired"
		| "unauthorized-work"
		| "missing-controls"
		| "worker-non-compliance"
		| "other";
	description: string;
	severity: "low" | "medium" | "high" | "critical";
	discoveredBy: string;
	discoveredAt: Date;
	correctionRequired: string;
	correctedAt?: Date;
	correctedBy?: string;
	status: "open" | "corrected" | "closed";
}
