import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, FileText, Shield, AlertTriangle, CheckCircle} from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';

const PTWFormDemoPage: React.FC = () => {
  const navigate = useNavigate();

  const handleStartPTW = () => {
    navigate('/ptw/form/new');
  };

  const handleBack = () => {
    navigate('/');
  };

  return (
    <FloatingCard title="Permit to Work Form">
      <div className="min-h-screen bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={handleBack}
                className="flex items-center text-gray-600 hover:text-gray-900 transition-colors"
              >
                <ArrowLeft className="h-5 w-5 mr-2" />
                Back to Dashboard
              </button>
              <div className="h-6 w-px bg-gray-300" />
              <div className="flex items-center space-x-2">
                <Shield className="h-6 w-6 text-green-600" />
                <h1 className="text-2xl font-bold text-gray-900">Permit to Work (PTW) System</h1>
              </div>
            </div>
          </div>
        </div>

        {/* Main Content */}
        <div className="px-6 py-8">
          <div className="max-w-6xl mx-auto space-y-8">
            {/* Introduction */}
            <div className="bg-white rounded-lg shadow-lg p-8 text-center">
              <div className="flex justify-center mb-4">
                <Shield className="h-16 w-16 text-green-600" />
              </div>
              <h2 className="text-3xl font-bold text-gray-900 mb-4">
                Permit to Work System
              </h2>
              <p className="text-gray-600 mb-8 text-lg">
                A comprehensive 3-step digital permit system based on the General PTW sheet structure.
                Ensures safe work practices and regulatory compliance with automated date/time stamping.
              </p>
              
              <button
                onClick={handleStartPTW}
                className="inline-flex items-center px-8 py-3 bg-green-600 text-white rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-green-500 text-lg font-medium"
              >
                <FileText className="h-5 w-5 mr-2" />
                Start New Permit
              </button>
            </div>

            {/* 3-Step Process */}
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-6 text-center">3-Step Process Based on PTW Sheets</h3>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <FileText className="h-10 w-10 text-green-600" />
                  </div>
                  <h4 className="text-xl font-semibold text-gray-900 mb-2">Step 1</h4>
                  <h5 className="font-medium text-green-600 mb-3">General Work Permit (GWP) - Sheet 1</h5>
                  <p className="text-sm text-gray-600">
                    Complete permit application including work details, applicant information, work team,
                    hazard identification, PPE requirements, precautions required, isolation needs, hot work permits, and confined space entry
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-20 h-20 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <AlertTriangle className="h-10 w-10 text-yellow-600" />
                  </div>
                  <h4 className="text-xl font-semibold text-gray-900 mb-2">Step 2</h4>
                  <h5 className="font-medium text-yellow-600 mb-3">Work Area Inspection & Permit Renewal</h5>
                  <p className="text-sm text-gray-600">
                    Authorized person inspection with safety checklist, area assessment,
                    and permit renewal procedures if required
                  </p>
                </div>

                <div className="text-center">
                  <div className="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <CheckCircle className="h-10 w-10 text-blue-600" />
                  </div>
                  <h4 className="text-xl font-semibold text-gray-900 mb-2">Step 3</h4>
                  <h5 className="font-medium text-blue-600 mb-3">Authorizations & Sign-offs - Sheet 2</h5>
                  <p className="text-sm text-gray-600">
                    Permit issuer authorization, area authority approval, safety officer authorization,
                    gas test results, emergency contacts, permit extensions, work completion documentation, and area handback procedures
                  </p>
                </div>
              </div>
            </div>

            {/* Key Features */}
            <div className="bg-white rounded-lg shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 mb-6">Key Features</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="space-y-4">
                  <div className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">Multi-Sheet Workflow</h4>
                      <p className="text-sm text-gray-600">3-step process based on actual PTW sheet structure (Sheet 1, Inspection, Sheet 2)</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">Automated Timestamps</h4>
                      <p className="text-sm text-gray-600">Date and time fields are automatically populated and read-only</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">Hazard Assessment</h4>
                      <p className="text-sm text-gray-600">Comprehensive hazard identification and control measures</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">Digital Signatures</h4>
                      <p className="text-sm text-gray-600">Electronic signature capture for all approval stages</p>
                    </div>
                  </div>
                </div>
                <div className="space-y-4">
                  <div className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">Progress Tracking</h4>
                      <p className="text-sm text-gray-600">Visual step indicator showing completion progress</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">Inspection Checklist</h4>
                      <p className="text-sm text-gray-600">Built-in safety checklist for work area inspection</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">Permit Summary</h4>
                      <p className="text-sm text-gray-600">Comprehensive summary view before final submission</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">Precautions Required</h4>
                      <p className="text-sm text-gray-600">Comprehensive checklist of 22 safety precautions</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">Gas Test Results</h4>
                      <p className="text-sm text-gray-600">Atmospheric testing for O₂, LEL, H₂S, CO and other gases</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">Emergency Contacts</h4>
                      <p className="text-sm text-gray-600">Primary and secondary emergency contact information</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">Permit Extensions</h4>
                      <p className="text-sm text-gray-600">Formal process for extending permit validity</p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 mr-3" />
                    <div>
                      <h4 className="font-medium text-gray-900">Responsive Design</h4>
                      <p className="text-sm text-gray-600">Works seamlessly on desktop, tablet, and mobile devices</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Safety Notice */}
            <div className="bg-red-50 border border-red-200 rounded-lg p-6">
              <div className="flex items-start">
                <AlertTriangle className="h-6 w-6 text-red-600 mt-0.5 mr-3" />
                <div>
                  <h3 className="text-lg font-medium text-red-800 mb-2">
                    Important Safety Notice
                  </h3>
                  <div className="text-sm text-red-700 space-y-2">
                    <p>• This permit must be completed and approved before any work begins</p>
                    <p>• All identified hazards must have corresponding control measures</p>
                    <p>• Work area inspection must be conducted by authorized personnel</p>
                    <p>• Permit is valid only for the specified date, time, and location</p>
                    <p>• Any changes to work scope require a new permit application</p>
                    <p>• Emergency procedures must be communicated to all personnel</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Technical Details */}
            <div className="bg-gray-50 border border-gray-200 rounded-lg p-6">
              <h3 className="text-lg font-medium text-gray-800 mb-4">
                Technical Implementation
              </h3>
              <div className="text-sm text-gray-600 space-y-2">
                <p>• Built with React and TypeScript for type safety and reliability</p>
                <p>• 3-step form workflow matching actual PTW sheet structure</p>
                <p>• Based on General PTW sheet images from utils folder</p>
                <p>• Comprehensive form sections: work details, team info, hazards, precautions, isolation, hot work, confined space</p>
                <p>• Automated date/time stamping for audit trail compliance</p>
                <p>• Work area inspection with safety checklist and permit renewal</p>
                <p>• Complete authorization workflow with issuer, area authority, and safety officer sign-offs</p>
                <p>• Gas testing results with atmospheric monitoring capabilities</p>
                <p>• Emergency contact management and permit extension procedures</p>
                <p>• Work completion and area handback documentation</p>
                <p>• Responsive design with Tailwind CSS styling</p>
                <p>• Form data persistence across all steps</p>
                <p>• Ready for backend integration and data storage</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </FloatingCard>
  );
};

export default PTWFormDemoPage;
