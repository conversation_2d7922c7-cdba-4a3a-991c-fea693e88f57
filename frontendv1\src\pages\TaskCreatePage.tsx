import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import {
  CheckCircle,
  FileText,
  Clock,
  MapPin
} from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import { SiteInfo } from '../types';
import { TaskTemplate, TaskCategory } from '../types/tasks';

import { mockSite } from '../mock/taskData';

// Mock task templates by category
const mockTaskTemplates: Record<TaskCategory, TaskTemplate[]> = {
  'excavation': [
    {
      id: 'template-exc-001',
      companyId: 'company-1',
      category: 'excavation',
      name: 'Standard Excavation',
      description: 'General excavation work with standard safety measures',
      hazards: [],
      controlMeasures: [],
      requiredDocuments: [],
      estimatedDuration: 8,
      skillRequirements: [],
      equipmentRequirements: [],
      requiredTrainings: [],
      requiredCertifications: [],
      requiredPPE: [],
      safetyRequirements: [],
      qualityChecks: [],
      permitTypes: [],
      riskLevel: 'medium',
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'admin',
      isActive: true
    },
    {
      id: 'template-exc-002',
      companyId: 'company-1',
      category: 'excavation',
      name: 'Deep Excavation (>2m)',
      description: 'Deep excavation requiring shoring and additional safety measures',
      hazards: [],
      controlMeasures: [],
      requiredDocuments: [],
      estimatedDuration: 12,
      skillRequirements: [],
      equipmentRequirements: [],
      requiredTrainings: [],
      requiredCertifications: [],
      requiredPPE: [],
      safetyRequirements: [],
      qualityChecks: [],
      permitTypes: [],
      riskLevel: 'high',
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'admin',
      isActive: true
    }
  ],
  'concrete-work': [
    {
      id: 'template-con-001',
      companyId: 'company-1',
      category: 'concrete-work',
      name: 'Foundation Pour',
      description: 'Concrete pouring for foundation work',
      hazards: [],
      controlMeasures: [],
      requiredDocuments: [],
      estimatedDuration: 6,
      skillRequirements: [],
      equipmentRequirements: [],
      requiredTrainings: [],
      requiredCertifications: [],
      requiredPPE: [],
      safetyRequirements: [],
      qualityChecks: [],
      permitTypes: [],
      riskLevel: 'medium',
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'admin',
      isActive: true
    }
  ],
  'electrical-installation': [
    {
      id: 'template-elec-001',
      companyId: 'company-1',
      category: 'electrical-installation',
      name: 'Standard Wiring',
      description: 'Standard electrical wiring installation',
      hazards: [],
      controlMeasures: [],
      requiredDocuments: [],
      estimatedDuration: 4,
      skillRequirements: [],
      equipmentRequirements: [],
      requiredTrainings: [],
      requiredCertifications: [],
      requiredPPE: [],
      safetyRequirements: [],
      qualityChecks: [],
      permitTypes: [],
      riskLevel: 'medium',
      createdAt: new Date(),
      updatedAt: new Date(),
      createdBy: 'admin',
      isActive: true
    }
  ],
  'steel-erection': [],
  'plumbing': [],
  'hvac': [],
  'safety': [],
  'inspection': [],
  'maintenance': [],
  'other': [],
  'electrical': [],
  'construction': []
};

const TaskCreatePage: React.FC = () => {
  const { siteId } = useParams<{ siteId: string }>();
  const navigate = useNavigate();
  const [site] = useState<SiteInfo>(mockSite);
  const [selectedCategory, setSelectedCategory] = useState<TaskCategory | ''>('');
  const [selectedTemplate, setSelectedTemplate] = useState<TaskTemplate | null>(null);
  const [workDescription, setWorkDescription] = useState('');
  const [location, setLocation] = useState('');
  const [plannedStartDate, setPlannedStartDate] = useState('');
  const [plannedStartTime, setPlannedStartTime] = useState('08:00');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [taskName, setTaskName] = useState('');
  const [estimatedDuration, setEstimatedDuration] = useState(8);

  // Get tomorrow's date as minimum date
  const getTomorrowDate = () => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow.toISOString().split('T')[0];
  };

  const categories: { value: TaskCategory; label: string }[] = [
    { value: 'excavation', label: 'Excavation' },
    { value: 'concrete-work', label: 'Concrete Work' },
    { value: 'steel-erection', label: 'Steel Erection' },
    { value: 'electrical-installation', label: 'Electrical Installation' },
    { value: 'plumbing', label: 'Plumbing' },
    { value: 'hvac', label: 'HVAC' },
    { value: 'safety', label: 'Safety' },
    { value: 'inspection', label: 'Inspection' },
    { value: 'maintenance', label: 'Maintenance' },
    { value: 'other', label: 'Other' }
  ];



  const handleCategoryChange = (category: TaskCategory) => {
    setSelectedCategory(category);
    setSelectedTemplate(null);
  };

  const handleTemplateSelect = (template: TaskTemplate) => {
    setSelectedTemplate(template);
    setTaskName(template.name);
    setWorkDescription(template.description);
    setEstimatedDuration(template.estimatedDuration);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!taskName.trim() || !workDescription.trim() || !location.trim() || !plannedStartDate) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Here you would call the API to:
      // 1. Create a copy of the task template with hazards and control measures
      // 2. Create a new site task instance
      // 3. Create a task request for approval

      const taskData = {
        templateId: selectedTemplate?.id || null,
        siteId: siteId,
        category: selectedCategory,
        name: taskName.trim(),
        workDescription: workDescription.trim(),
        location: location.trim(),
        plannedStartDate: new Date(`${plannedStartDate}T${plannedStartTime}:00`),
        plannedEndDate: new Date(new Date(`${plannedStartDate}T${plannedStartTime}:00`).getTime() + estimatedDuration * 60 * 60 * 1000),
        estimatedDuration: estimatedDuration,
        status: 'opened', // Will become 'requested' when submitted for approval
        priority: 'medium', // Default priority
        createdBy: 'current-user', // Replace with actual user ID
        createdByName: 'Site Engineer', // Replace with actual user name
        hasTemplate: !!selectedTemplate // Indicates if created from template or manually
      };

      console.log('Creating task:', taskData);

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Show success toast
      const successMessage = selectedTemplate
        ? 'Task created and submitted for approval successfully!'
        : 'Custom task created and submitted for approval successfully! HSE team will add hazards and control measures during review.';

      toast.success(successMessage, {
        position: 'top-right',
        autoClose: 6000,
      });

      // Navigate back to tasks page
      navigate(`/sites/${siteId}/tasks`);

    } catch (error) {
      console.error('Error creating task:', error);
      // Show error toast
      toast.error('Failed to create task. Please try again.', {
        position: 'top-right',
        autoClose: 5000,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: site.name, path: `/sites/${siteId}/dashboard` },
    { name: 'Tasks', path: `/sites/${siteId}/tasks` },
    { name: 'Create Task', path: `/sites/${siteId}/tasks/create` },
  ];

  const availableTemplates = selectedCategory ? mockTaskTemplates[selectedCategory] : [];

  return (
    <FloatingCard title="Create New Task" breadcrumbs={breadcrumbs}>
      <div className="max-w-4xl mx-auto">


        <form onSubmit={handleSubmit} className="space-y-8">
          {/* Step 1: Select Category */}
          <div className="bg-white rounded-lg border border-gray-200 p-6">
            <div className="flex items-center space-x-2 mb-4">
              <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                <span className="text-sm font-medium text-blue-600">1</span>
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Select Task Category</h3>
            </div>

            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3">
              {categories.map((category) => (
                <button
                  key={category.value}
                  type="button"
                  onClick={() => handleCategoryChange(category.value)}
                  className={`p-4 text-left border rounded-lg transition-colors ${
                    selectedCategory === category.value
                      ? 'border-blue-500 bg-blue-50 text-blue-700'
                      : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                  }`}
                >
                  <div className="font-medium text-sm">{category.label}</div>
                  <div className="text-xs text-gray-500 mt-1">
                    {mockTaskTemplates[category.value].length} template{mockTaskTemplates[category.value].length !== 1 ? 's' : ''}
                  </div>
                </button>
              ))}
            </div>
          </div>

          {/* Step 2: Select Template */}
          {selectedCategory && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-blue-600">2</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">Select Task Template</h3>
              </div>

              {availableTemplates.length === 0 ? (
                <div className="text-center py-8">
                  <FileText className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                  <h4 className="text-lg font-medium text-gray-900 mb-2">No templates available for this category</h4>
                  <p className="text-gray-500 mb-4">You can create a custom task. The HSE team will add hazards and control measures during approval.</p>
                  <button
                    type="button"
                    onClick={() => {
                      setSelectedTemplate(null);
                      setTaskName('');
                      setWorkDescription('');
                      setEstimatedDuration(8);
                    }}
                    className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
                  >
                    Create Custom Task
                  </button>
                </div>
              ) : (
                <div className="space-y-3">
                  {availableTemplates.map((template) => (
                    <button
                      key={template.id}
                      type="button"
                      onClick={() => handleTemplateSelect(template)}
                      className={`w-full p-4 text-left border rounded-lg transition-colors ${
                        selectedTemplate?.id === template.id
                          ? 'border-blue-500 bg-blue-50'
                          : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                      }`}
                    >
                      <div className="flex justify-between items-start">
                        <div className="flex-1">
                          <h4 className="font-medium text-gray-900">{template.name}</h4>
                          <p className="text-sm text-gray-600 mt-1">{template.description}</p>
                        </div>
                        <div className="text-right ml-4">
                          <div className="flex items-center space-x-1 text-sm text-gray-500">
                            <Clock className="h-4 w-4" />
                            <span>{template.estimatedDuration}h</span>
                          </div>
                        </div>
                      </div>
                    </button>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Step 3: Task Details */}
          {(selectedTemplate || (selectedCategory && availableTemplates.length === 0)) && (
            <div className="bg-white rounded-lg border border-gray-200 p-6">
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-sm font-medium text-blue-600">3</span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900">Task Details</h3>
              </div>

              <div className="space-y-6">
                {!selectedTemplate && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Task Name *
                    </label>
                    <input
                      type="text"
                      value={taskName}
                      onChange={(e) => setTaskName(e.target.value)}
                      placeholder="Enter a descriptive name for the task..."
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>
                )}

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Work Description *
                  </label>
                  <textarea
                    value={workDescription}
                    onChange={(e) => setWorkDescription(e.target.value)}
                    placeholder="Describe the specific work to be performed..."
                    rows={4}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Location *
                  </label>
                  <div className="relative">
                    <MapPin className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <input
                      type="text"
                      value={location}
                      onChange={(e) => setLocation(e.target.value)}
                      placeholder="e.g., Building A - Level 2, Zone C"
                      className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Planned Start Date *
                    </label>
                    <input
                      type="date"
                      value={plannedStartDate}
                      onChange={(e) => setPlannedStartDate(e.target.value)}
                      min={getTomorrowDate()}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Tasks cannot be scheduled for today. Minimum start date is tomorrow.
                    </p>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Planned Start Time *
                    </label>
                    <input
                      type="time"
                      value={plannedStartTime}
                      onChange={(e) => setPlannedStartTime(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>
                  {!selectedTemplate && (
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">
                        Estimated Duration (hours) *
                      </label>
                      <input
                        type="number"
                        min="1"
                        max="24"
                        value={estimatedDuration}
                        onChange={(e) => setEstimatedDuration(parseInt(e.target.value) || 8)}
                        className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                        required
                      />
                    </div>
                  )}
                </div>

                <div className="bg-gray-50 rounded-lg p-4">
                  <h4 className="text-sm font-medium text-gray-900 mb-2">
                    {selectedTemplate ? 'Template Information' : 'Task Information'}
                  </h4>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-600">Estimated Duration:</span>
                      <span className="ml-2 font-medium">{estimatedDuration} hours</span>
                    </div>
                    <div>
                      <span className="text-gray-600">Category:</span>
                      <span className="ml-2 font-medium capitalize">{selectedCategory.replace('-', ' ')}</span>
                    </div>
                    {!selectedTemplate && (
                      <div className="md:col-span-2">
                        <span className="text-gray-600">Note:</span>
                        <span className="ml-2 text-orange-600 font-medium">
                          HSE team will add hazards and control measures during approval
                        </span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Submit Button */}
          {taskName && workDescription && location && plannedStartDate && (
            <div className="flex justify-end space-x-4">
              <button
                type="button"
                onClick={() => navigate(`/sites/${siteId}/tasks`)}
                className="px-6 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="flex items-center space-x-2 px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:bg-blue-400 disabled:cursor-not-allowed transition-colors"
              >
                {isSubmitting ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                    <span>Creating...</span>
                  </>
                ) : (
                  <>
                    <CheckCircle className="h-4 w-4" />
                    <span>Create & Request Approval</span>
                  </>
                )}
              </button>
            </div>
          )}
        </form>
      </div>
    </FloatingCard>
  );
};

export default TaskCreatePage;
