 .u-section-1 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-1 .u-sheet-1 {
  min-height: 805px;
}

.u-section-1 .u-layout-wrap-1 {
  margin-top: 40px;
  margin-bottom: -1px;
  --radius: 20px;
}

.u-section-1 .u-layout-cell-1 {
  min-height: 743px;
}

.u-section-1 .u-container-layout-1 {
  padding: 0;
}

.u-section-1 .u-group-1 {
  --radius: 20px;
  min-height: 725px;
  height: auto;
  margin-top: 0;
  margin-bottom: 0;
  box-shadow: 5px 5px 8px 0 rgba(224,229,235,0.4);
}

.u-section-1 .u-container-layout-2 {
  padding: 30px;
}

.u-section-1 .u-text-1 {
  font-size: 2.25rem;
  font-weight: 400;
  margin: 0 auto 0 1px;
}

.u-section-1 .u-text-2 {
  font-weight: 300;
  margin: 8px 31px 0 0;
}

.u-section-1 .u-form-1 {
  width: 483px;
  height: 504px;
  margin: 30px auto 0;
}

.u-section-1 .u-input-1 {
  --radius: 10px;
}

.u-section-1 .u-input-2 {
  --radius: 10px;
}

.u-section-1 .u-form-group-3 {
  margin-left: 0;
}

.u-section-1 .u-input-3 {
  --radius: 10px;
}

.u-section-1 .u-form-group-4 {
  margin-left: 0;
}

.u-section-1 .u-input-4 {
  --radius: 10px;
}

.u-section-1 .u-input-5 {
  --radius: 10px;
}

.u-section-1 .u-btn-1 {
  background-image: none;
  --radius: 10px;
}

.u-section-1 .u-image-1 {
  min-height: 391px;
  background-repeat: no-repeat;
  background-size: auto 145%;
  --radius: 30px;
  background-image: url("images/contactus.webp");
  background-position: 50% 50%;
}

.u-section-1 .u-container-layout-3 {
  padding: 0;
}

.u-section-1 .u-layout-cell-3 {
  min-height: 337px;
  --radius: 30px;
}

.u-section-1 .u-container-layout-4 {
  padding: 30px;
}

.u-section-1 .u-list-1 {
  margin: 0;
}

.u-section-1 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 274px;
  --gap: 10px;
}

.u-section-1 .u-list-item-1 {
  --radius: 20px;
}

.u-section-1 .u-container-layout-5 {
  padding: 10px 30px;
}

.u-section-1 .u-icon-1 {
  width: 40px;
  height: 40px;
  background-image: none;
  --radius: 50px;
  margin: 13px auto 0 0;
  padding: 8px;
}

.u-section-1 .u-text-3 {
  font-weight: 400;
  margin: -52px auto 0 60px;
}

.u-section-1 .u-text-4 {
  transition-duration: 0.5s;
  margin: 5px 20px 0 59px;
}

.u-section-1 .u-list-item-2 {
  --radius: 20px;
}

.u-section-1 .u-container-layout-6 {
  padding: 10px 30px;
}

.u-section-1 .u-icon-2 {
  width: 40px;
  height: 40px;
  background-image: none;
  --radius: 50px;
  margin: 13px auto 0 0;
  padding: 8px;
}

.u-section-1 .u-text-5 {
  margin: -52px auto 0 60px;
}

.u-section-1 .u-text-6 {
  transition-duration: 0.5s;
  margin: 5px 20px 0 59px;
}

.u-section-1 .u-list-item-3 {
  --radius: 20px;
}

.u-section-1 .u-container-layout-7 {
  padding: 10px 30px;
}

.u-section-1 .u-icon-3 {
  width: 40px;
  height: 40px;
  background-image: none;
  --radius: 50px;
  margin: 13px auto 0 0;
  padding: 8px;
}

.u-section-1 .u-text-7 {
  font-weight: 400;
  margin: -52px auto 0 60px;
}

.u-section-1 .u-text-8 {
  transition-duration: 0.5s;
  margin: 5px 20px 0 59px;
}

@media (max-width: 1199px) {
  .u-section-1 .u-sheet-1 {
    min-height: 738px;
  }

  .u-section-1 .u-layout-wrap-1 {
    margin-bottom: 20px;
  }

  .u-section-1 .u-layout-cell-1 {
    --radius: 20px;
    min-height: 750px;
  }

  .u-section-1 .u-group-1 {
    min-height: 730px;
    height: auto;
  }

  .u-section-1 .u-container-layout-2 {
    padding-bottom: 27px;
  }

  .u-section-1 .u-text-2 {
    margin-right: 0;
  }

  .u-section-1 .u-form-1 {
    margin-right: initial;
    margin-left: initial;
    width: auto;
  }

  .u-section-1 .u-image-1 {
    min-height: 326px;
  }

  .u-section-1 .u-layout-cell-3 {
    min-height: 348px;
  }

  .u-section-1 .u-list-1 {
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-1 .u-repeater-1 {
    grid-template-columns: 100%;
    grid-gap: 10px;
  }

  .u-section-1 .u-list-item-1 {
    background-position: 50% 50%;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .u-section-1 .u-text-4 {
    margin-right: 0;
    margin-left: 60px;
  }

  .u-section-1 .u-list-item-2 {
    background-position: 50% 50%;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .u-section-1 .u-text-5 {
    font-weight: 400;
  }

  .u-section-1 .u-text-6 {
    margin-right: 0;
    margin-left: 60px;
  }

  .u-section-1 .u-list-item-3 {
    background-position: 50% 50%;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .u-section-1 .u-text-8 {
    margin-right: 0;
    margin-left: 60px;
  }
}

@media (max-width: 991px) {
  .u-section-1 .u-sheet-1 {
    min-height: 404px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 724px;
  }

  .u-section-1 .u-image-1 {
    min-height: 324px;
  }

  .u-section-1 .u-layout-cell-3 {
    min-height: 424px;
  }
}

@media (max-width: 767px) {
  .u-section-1 .u-sheet-1 {
    min-height: 612px;
  }

  .u-section-1 .u-layout-wrap-1 {
    position: relative;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-1 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-image-1 {
    min-height: 449px;
  }

  .u-section-1 .u-layout-cell-3 {
    min-height: 352px;
  }

  .u-section-1 .u-container-layout-5 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-container-layout-6 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-container-layout-7 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-1 .u-sheet-1 {
    min-height: 468px;
  }

  .u-section-1 .u-image-1 {
    min-height: 283px;
  }

  .u-section-1 .u-container-layout-4 {
    padding-left: 20px;
    padding-right: 20px;
  }

  .u-section-1 .u-icon-1 {
    margin-top: 7px;
    margin-left: 7px;
  }

  .u-section-1 .u-text-3 {
    margin-top: -47px;
  }

  .u-section-1 .u-icon-2 {
    margin-top: 7px;
    margin-left: 7px;
  }

  .u-section-1 .u-text-5 {
    margin-top: -47px;
  }

  .u-section-1 .u-icon-3 {
    margin-top: 7px;
    margin-left: 7px;
  }

  .u-section-1 .u-text-7 {
    margin-top: -47px;
  }
}

.u-section-1 .u-text-4,
.u-section-1 .u-text-4:before,
.u-section-1 .u-text-4 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-1 :hover > .u-container-layout .u-text-4 {
  color: #84277f !important;
}

.u-section-1 .hover > .u-container-layout .u-text-4 {
  color: #84277f !important;
}

.u-section-1 .u-text-6,
.u-section-1 .u-text-6:before,
.u-section-1 .u-text-6 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-1 :hover > .u-container-layout .u-text-6 {
  color: #84277f !important;
}

.u-section-1 .hover > .u-container-layout .u-text-6 {
  color: #84277f !important;
}

.u-section-1 .u-text-8,
.u-section-1 .u-text-8:before,
.u-section-1 .u-text-8 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-1 :hover > .u-container-layout .u-text-8 {
  color: #84277f !important;
}

.u-section-1 .hover > .u-container-layout .u-text-8 {
  color: #84277f !important;
}