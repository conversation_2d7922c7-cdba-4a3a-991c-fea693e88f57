using System;
using System.Collections.Generic;
using Shared.Interfaces;
using Shared.Enums;

namespace Shared.GraphQL.Models
{
    public class Incident : IAuditableEntity, ISoftDeletable
    {
        public int Id { get; set; }
        public string Title { get; set; }
        public string Description { get; set; }
        public DateTime IncidentDate { get; set; }
        public string Location { get; set; }
        public IncidentStatus Status { get; set; }
        public string? ReportedBy { get; set; }
        public string? InvestigatedBy { get; set; }
        public string? Resolution { get; set; }
        public DateTime? ResolvedDate { get; set; }

        // Navigation Properties - Many-to-many with Workers
        public ICollection<Worker> Workers { get; set; } = new List<Worker>();

        // Audit Fields
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; }
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; }

        // Soft Delete Fields
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }
    }
}
