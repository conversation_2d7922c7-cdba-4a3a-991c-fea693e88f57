import React, { useState, useEffect } from "react";
import { useParams, useLocation } from "react-router-dom";
import {
	LayoutDashboard,
	ClipboardCheck,
	History,
	FileText,
} from "lucide-react";
import FloatingCard from "../components/layout/FloatingCard";
import TabContainer, { Tab } from "../components/data/shared/TabContainer";
import PermitsDashboard from "../components/permits/PermitsDashboard";
import ActivePermits from "../components/permits/ActivePermits";
import AllPermits from "../components/permits/AllPermits";
import RequestedPermits from "../components/permits/RequestedPermits";
import PermitHistory from "../components/permits/PermitHistory";
import PermitReports from "../components/permits/PermitReports";
import { SiteInfo } from "../types";

// Mock site data - replace with actual API call
const mockSite: SiteInfo = {
	id: "site-1",
	name: "Downtown Construction Site",
	location: "Nairobi, Kenya",
	projectManager: "<PERSON>",
	healthStatus: "green",
	workersOnSite: 45,
	activePermits: 8,
	openIncidents: 0,
	timeline: "Jan 2025 - Dec 2026",
	currentPhase: "Construction",
	progressPercentage: 65,
	tenantId: '',
	status: 'active',
	createdAt: new Date()
};

const PermitsPage: React.FC = () => {
	const { siteId } = useParams<{ siteId: string }>();
	const location = useLocation();
	const [site] = useState<SiteInfo>(mockSite);
	const [activeTab, setActiveTab] = useState("dashboard");

	const validTabs = ["dashboard", "active-permits", "all-permits", "history", "reports"];

	// Handle URL hash navigation - this effect runs whenever the location changes
	useEffect(() => {
		const hash = location.hash.replace("#", "");
		if (hash && validTabs.includes(hash)) {
			setActiveTab(hash);
		} else if (!hash) {
			setActiveTab("dashboard");
		}
	}, [location.hash]);

	// Also listen for direct hash changes (for browser back/forward)
	useEffect(() => {
		const handleHashChange = () => {
			const newHash = window.location.hash.replace("#", "");
			if (newHash && validTabs.includes(newHash)) {
				setActiveTab(newHash);
			} else if (!newHash) {
				setActiveTab("dashboard");
			}
		};

		window.addEventListener("hashchange", handleHashChange);
		return () => window.removeEventListener("hashchange", handleHashChange);
	}, []);

	const handleNavigateToTab = (tabId: string) => {
		setActiveTab(tabId);
		// Update URL hash without triggering a page reload
		window.history.replaceState(null, "", `#${tabId}`);
	};

	const breadcrumbs = [
		{ name: "Dashboard", path: "/" },
		{ name: site.name, path: `/sites/${siteId}/dashboard` },
		{ name: "Permits", path: `/sites/${siteId}/permits` },
	];

	const tabs: Tab[] = [
		{
			id: "dashboard",
			label: "Dashboard",
			icon: <LayoutDashboard className="h-4 w-4" />,
			content: (
				<PermitsDashboard
					siteId={siteId || ""}
					onNavigateToTab={handleNavigateToTab}
				/>
			),
		},
		{
			id: "active-permits",
			label: "Active Permits",
			icon: <ClipboardCheck className="h-4 w-4" />,
			content: <ActivePermits siteId={siteId || ""} />,
		},
		{
			id: "all-permits",
			label: "All Permits",
			icon: <FileText className="h-4 w-4" />,
			content: <AllPermits siteId={siteId || ""} />,
		},
		{
			id: "requested-permits",
			label: "Requested Permits",
			icon: <ClipboardCheck className="h-4 w-4" />,
			content: <RequestedPermits siteId={siteId || ""} />,
		},
		{
			id: "history",
			label: "Permit History",
			icon: <History className="h-4 w-4" />,
			content: <PermitHistory siteId={siteId || ""} />,
		},
		{
			id: "reports",
			label: "Reports",
			icon: <FileText className="h-4 w-4" />,
			content: <PermitReports siteId={siteId || ""} />,
		},
	];

	return (
		<FloatingCard title="Permits" breadcrumbs={breadcrumbs}>
			<TabContainer
				tabs={tabs}
				activeTab={activeTab}
				onTabChange={handleNavigateToTab}
			/>
		</FloatingCard>
	);
};

export default PermitsPage;
