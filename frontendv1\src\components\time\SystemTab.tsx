import React, { useState } from 'react';
import { Wifi, WifiOff, <PERSON><PERSON><PERSON><PERSON>gle, <PERSON>tings, RefreshCw, Plus, Activity, UserPlus } from 'lucide-react';
import TerminalStatusPanel from './TerminalStatusPanel';
import FaceRegistrationModal from './FaceRegistrationModal';
import TerminalConfigModal from './TerminalConfigModal';
import { TerminalStatus, TerminalConfiguration, FaceRegistrationResult } from '../../types/time';

interface SystemTabProps {
	siteId: string;
}

// Mock data for terminals
const mockTerminals: TerminalStatus[] = [
	{
		id: "term1",
		name: "Main Gate Terminal",
		location: "Site Entrance",
		status: "online",
		lastSync: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
		totalCheckInsToday: 28,
		ipAddress: "*************",
	},
	{
		id: "term2",
		name: "Warehouse Terminal",
		location: "Equipment Storage",
		status: "online",
		lastSync: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
		totalCheckInsToday: 15,
		ipAddress: "*************",
	},
	{
		id: "term3",
		name: "Office Terminal",
		location: "Site Office",
		status: "offline",
		lastSync: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
		totalCheckInsToday: 0,
		ipAddress: "*************",
	},
];

const SystemTab: React.FC<SystemTabProps> = ({ siteId: _siteId }) => {
  const [terminals, _setTerminals] = useState<TerminalStatus[]>(mockTerminals);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [isFaceModalOpen, setIsFaceModalOpen] = useState(false);
  const [isTerminalModalOpen, setIsTerminalModalOpen] = useState(false);
  const [selectedTerminal, setSelectedTerminal] = useState<TerminalConfiguration | null>(null);

	const handleRefreshTerminals = async () => {
		setIsRefreshing(true);
		// Simulate API call
		setTimeout(() => {
			setIsRefreshing(false);
			// In real app, this would fetch fresh terminal data
			console.log("Refreshing terminal status...");
		}, 2000);
	};

  const handleAddTerminal = () => {
    setSelectedTerminal(null);
    setIsTerminalModalOpen(true);
  };

  const handleConfigureTerminal = (terminalId: string) => {
    // Convert TerminalStatus to TerminalConfiguration for editing
    const terminal = terminals.find(t => t.id === terminalId);
    if (terminal) {
      const config: TerminalConfiguration = {
        id: terminal.id,
        name: terminal.name,
        location: terminal.location,
        ipAddress: terminal.ipAddress || '',
        port: 80,
        username: 'admin',
        password: '••••••••',
        deviceType: 'access-control',
        isActive: terminal.status !== 'offline'
      };
      setSelectedTerminal(config);
      setIsTerminalModalOpen(true);
    }
  };

  const handleRegisterFace = () => {
    setIsFaceModalOpen(true);
  };

  const handleTerminalSave = (config: TerminalConfiguration) => {
    // In real app, this would save to the backend
    console.log('Saving terminal config:', config);
    alert(`Terminal ${config.name} configuration saved successfully!`);
  };

  const handleFaceRegistrationComplete = (result: FaceRegistrationResult) => {
    if (result.success) {
      console.log('Face registration completed:', result);
    }
  };

	const getStatusIcon = (status: TerminalStatus["status"]) => {
		switch (status) {
			case "online":
				return <Wifi className="h-5 w-5 text-green-500" />;
			case "offline":
				return <WifiOff className="h-5 w-5 text-gray-400" />;
			case "error":
				return <AlertTriangle className="h-5 w-5 text-red-500" />;
			default:
				return <WifiOff className="h-5 w-5 text-gray-400" />;
		}
	};

	const getStatusBadge = (status: TerminalStatus["status"]) => {
		const statusConfig = {
			online: {
				className: "bg-green-100 text-green-800",
				label: "Online",
			},
			offline: {
				className: "bg-gray-100 text-gray-800",
				label: "Offline",
			},
			error: {
				className: "bg-red-100 text-red-800",
				label: "Error",
			},
		};

		const config = statusConfig[status];
		return (
			<span
				className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${config.className}`}
			>
				{config.label}
			</span>
		);
	};

	const formatLastSync = (lastSync?: string) => {
		if (!lastSync) return "Never";

		const syncDate = new Date(lastSync);
		const now = new Date();
		const diffMinutes = Math.floor(
			(now.getTime() - syncDate.getTime()) / (1000 * 60),
		);

		if (diffMinutes < 1) return "Just now";
		if (diffMinutes < 60) return `${diffMinutes}m ago`;
		if (diffMinutes < 1440) return `${Math.floor(diffMinutes / 60)}h ago`;
		return syncDate.toLocaleDateString();
	};

	const onlineTerminals = terminals.filter((t) => t.status === "online").length;
	const totalTerminals = terminals.length;
	const totalCheckInsToday = terminals.reduce(
		(sum, t) => sum + t.totalCheckInsToday,
		0,
	);

  return (
    <div className="space-y-6">
      {/* Header with Actions */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Hikvision Terminal Management</h2>
          <p className="text-sm text-gray-600 mt-1">
            Manage and monitor face recognition terminals across the site
          </p>
        </div>
        <div className="flex space-x-3">
          <button
            onClick={handleRefreshTerminals}
            disabled={isRefreshing}
            className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors disabled:opacity-50"
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            {isRefreshing ? 'Refreshing...' : 'Refresh'}
          </button>
          <button
            onClick={handleRegisterFace}
            className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors"
          >
            <UserPlus className="h-4 w-4 mr-2" />
            Register Face
          </button>
          <button
            onClick={handleAddTerminal}
            className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 transition-colors"
          >
            <Plus className="h-4 w-4 mr-2" />
            Add Terminal
          </button>
        </div>
      </div>

			{/* System Overview */}
			<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
				<div className="bg-white p-6 rounded-lg border border-gray-200">
					<div className="flex items-center">
						<div className="flex-shrink-0">
							<Activity className="h-8 w-8 text-blue-600" />
						</div>
						<div className="ml-4">
							<p className="text-sm font-medium text-gray-500">
								Total Terminals
							</p>
							<p className="text-2xl font-semibold text-gray-900">
								{totalTerminals}
							</p>
						</div>
					</div>
				</div>

				<div className="bg-white p-6 rounded-lg border border-gray-200">
					<div className="flex items-center">
						<div className="flex-shrink-0">
							<Wifi className="h-8 w-8 text-green-600" />
						</div>
						<div className="ml-4">
							<p className="text-sm font-medium text-gray-500">
								Online Terminals
							</p>
							<p className="text-2xl font-semibold text-gray-900">
								{onlineTerminals}/{totalTerminals}
							</p>
						</div>
					</div>
				</div>

				<div className="bg-white p-6 rounded-lg border border-gray-200">
					<div className="flex items-center">
						<div className="flex-shrink-0">
							<Activity className="h-8 w-8 text-purple-600" />
						</div>
						<div className="ml-4">
							<p className="text-sm font-medium text-gray-500">
								Today's Check-ins
							</p>
							<p className="text-2xl font-semibold text-gray-900">
								{totalCheckInsToday}
							</p>
						</div>
					</div>
				</div>
			</div>

			{/* Terminal Status Panel */}
			<TerminalStatusPanel
				terminals={terminals}
				totalCheckInsToday={totalCheckInsToday}
			/>

      {/* Detailed Terminal List */}
      <div className="bg-white rounded-lg border border-gray-200 overflow-hidden">
        <div className="px-6 py-4 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">Terminal Details</h3>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Terminal
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  IP Address
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Last Sync
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Check-ins Today
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {terminals.map((terminal) => (
                <tr key={terminal.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0">
                        {getStatusIcon(terminal.status)}
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {terminal.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {terminal.location}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    {getStatusBadge(terminal.status)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {terminal.ipAddress || '-'}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {formatLastSync(terminal.lastSync)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {terminal.totalCheckInsToday}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      onClick={() => handleConfigureTerminal(terminal.id)}
                      className="text-green-600 hover:text-green-900 transition-colors"
                      title="Configure terminal"
                    >
                      <Settings className="h-4 w-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Face Registration Modal */}
      <FaceRegistrationModal
        isOpen={isFaceModalOpen}
        onClose={() => setIsFaceModalOpen(false)}
        onRegistrationComplete={handleFaceRegistrationComplete}
      />

      {/* Terminal Configuration Modal */}
      <TerminalConfigModal
        isOpen={isTerminalModalOpen}
        onClose={() => {
          setIsTerminalModalOpen(false);
          setSelectedTerminal(null);
        }}
        terminal={selectedTerminal}
        onSave={handleTerminalSave}
      />
    </div>
  );
};

export default SystemTab;
