import React, { useState } from "react";
import {
	Search,
	Download,
	Eye,
	Calendar,
	CheckCircle,
	XCircle,
	AlertTriangle,
} from "lucide-react";

interface InspectionHistoryProps {
	siteId: string;
}

interface CompletedInspection {
	id: string;
	templateName: string;
	target: string;
	inspector: string;
	completedAt: string;
	status: "passed" | "failed" | "passed-with-issues";
	criticalIssues: number;
	totalItems: number;
	passedItems: number;
	failedItems: number;
	duration: string;
}

const InspectionHistory: React.FC<InspectionHistoryProps> = ({ siteId: _siteId }) => {
	const [searchTerm, setSearchTerm] = useState("");
	const [filterStatus, setFilterStatus] = useState<string>("all");
	const [filterDateRange, setFilterDateRange] = useState<string>("all");

	// Mock data - replace with actual API call
	const completedInspections: CompletedInspection[] = [
		{
			id: "1",
			templateName: "Daily Site Safety Inspection",
			target: "Main Construction Area",
			inspector: "<PERSON>",
			completedAt: "2024-01-15 14:30",
			status: "passed",
			criticalIssues: 0,
			totalItems: 20,
			passedItems: 20,
			failedItems: 0,
			duration: "45m",
		},
		{
			id: "2",
			templateName: "Equipment Pre-Use Check",
			target: "Excavator CAT-001",
			inspector: "Mary Wanjiku",
			completedAt: "2024-01-15 11:15",
			status: "failed",
			criticalIssues: 2,
			totalItems: 12,
			passedItems: 8,
			failedItems: 4,
			duration: "30m",
		},
		{
			id: "3",
			templateName: "Scaffold Safety Check",
			target: "Building A - Level 3",
			inspector: "Peter Kiprotich",
			completedAt: "2024-01-15 09:45",
			status: "passed-with-issues",
			criticalIssues: 0,
			totalItems: 15,
			passedItems: 13,
			failedItems: 2,
			duration: "35m",
		},
		{
			id: "4",
			templateName: "Fire Safety Inspection",
			target: "Site Office Building",
			inspector: "Sarah Njeri",
			completedAt: "2024-01-14 16:20",
			status: "passed",
			criticalIssues: 0,
			totalItems: 18,
			passedItems: 18,
			failedItems: 0,
			duration: "40m",
		},
		{
			id: "5",
			templateName: "PPE Compliance Check",
			target: "Site Entrance Gate",
			inspector: "David Ochieng",
			completedAt: "2024-01-14 08:30",
			status: "failed",
			criticalIssues: 1,
			totalItems: 10,
			passedItems: 7,
			failedItems: 3,
			duration: "25m",
		},
	];

	const getStatusIcon = (status: string) => {
		switch (status) {
			case "passed":
				return <CheckCircle className="h-4 w-4 text-green-500" />;
			case "failed":
				return <XCircle className="h-4 w-4 text-red-500" />;
			case "passed-with-issues":
				return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
			default:
				return <CheckCircle className="h-4 w-4 text-gray-500" />;
		}
	};

	const getStatusColor = (status: string) => {
		switch (status) {
			case "passed":
				return "text-green-700 bg-green-50";
			case "failed":
				return "text-red-700 bg-red-50";
			case "passed-with-issues":
				return "text-yellow-700 bg-yellow-50";
			default:
				return "text-gray-700 bg-gray-50";
		}
	};

	const getStatusText = (status: string) => {
		switch (status) {
			case "passed":
				return "Passed";
			case "failed":
				return "Failed";
			case "passed-with-issues":
				return "Passed with Issues";
			default:
				return "Unknown";
		}
	};

	const formatDateTime = (dateTimeString: string) => {
		const dateTime = new Date(dateTimeString);
		return {
			date: dateTime.toLocaleDateString("en-US", {
				month: "short",
				day: "numeric",
				year: "numeric",
			}),
			time: dateTime.toLocaleTimeString("en-US", {
				hour: "2-digit",
				minute: "2-digit",
				hour12: false,
			}),
		};
	};

	const filteredInspections = completedInspections.filter((inspection) => {
		const matchesStatus =
			filterStatus === "all" || inspection.status === filterStatus;
		const matchesSearch =
			inspection.templateName
				.toLowerCase()
				.includes(searchTerm.toLowerCase()) ||
			inspection.target.toLowerCase().includes(searchTerm.toLowerCase()) ||
			inspection.inspector.toLowerCase().includes(searchTerm.toLowerCase());

		// Date range filtering logic would go here
		const matchesDateRange = true; // Simplified for now

		return matchesStatus && matchesSearch && matchesDateRange;
	});

	const handleExportReport = () => {
		console.log("Exporting inspection history report...");
	};

	const handleViewReport = (inspectionId: string) => {
		console.log("Viewing detailed report for inspection:", inspectionId);
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
				<div>
					<h2 className="text-xl font-semibold text-gray-900">
						Inspection History
					</h2>
					<p className="text-sm text-gray-600 mt-1">
						Complete record of all inspections ({completedInspections.length}{" "}
						total)
					</p>
				</div>
				<button
					onClick={handleExportReport}
					className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"
				>
					<Download className="h-4 w-4 mr-2" />
					Export Report
				</button>
			</div>

			{/* Filters and Search */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
				<div className="md:col-span-2">
					<div className="relative">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
						<input
							type="text"
							placeholder="Search inspections..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
						/>
					</div>
				</div>
				<select
					value={filterStatus}
					onChange={(e) => setFilterStatus(e.target.value)}
					className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
				>
					<option value="all">All Status</option>
					<option value="passed">Passed</option>
					<option value="failed">Failed</option>
					<option value="passed-with-issues">Passed with Issues</option>
				</select>
				<select
					value={filterDateRange}
					onChange={(e) => setFilterDateRange(e.target.value)}
					className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
				>
					<option value="all">All Time</option>
					<option value="today">Today</option>
					<option value="week">This Week</option>
					<option value="month">This Month</option>
					<option value="quarter">This Quarter</option>
				</select>
			</div>

			{/* Summary Stats */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
				<div className="bg-white p-4 rounded-lg border border-gray-200">
					<div className="flex items-center">
						<CheckCircle className="h-8 w-8 text-green-500" />
						<div className="ml-3">
							<div className="text-lg font-semibold text-gray-900">
								{
									completedInspections.filter((i) => i.status === "passed")
										.length
								}
							</div>
							<div className="text-sm text-gray-600">Passed</div>
						</div>
					</div>
				</div>
				<div className="bg-white p-4 rounded-lg border border-gray-200">
					<div className="flex items-center">
						<XCircle className="h-8 w-8 text-red-500" />
						<div className="ml-3">
							<div className="text-lg font-semibold text-gray-900">
								{
									completedInspections.filter((i) => i.status === "failed")
										.length
								}
							</div>
							<div className="text-sm text-gray-600">Failed</div>
						</div>
					</div>
				</div>
				<div className="bg-white p-4 rounded-lg border border-gray-200">
					<div className="flex items-center">
						<AlertTriangle className="h-8 w-8 text-yellow-500" />
						<div className="ml-3">
							<div className="text-lg font-semibold text-gray-900">
								{
									completedInspections.filter(
										(i) => i.status === "passed-with-issues",
									).length
								}
							</div>
							<div className="text-sm text-gray-600">With Issues</div>
						</div>
					</div>
				</div>
				<div className="bg-white p-4 rounded-lg border border-gray-200">
					<div className="flex items-center">
						<Calendar className="h-8 w-8 text-blue-500" />
						<div className="ml-3">
							<div className="text-lg font-semibold text-gray-900">
								{Math.round(
									(completedInspections.filter((i) => i.status === "passed")
										.length /
										completedInspections.length) *
										100,
								)}
								%
							</div>
							<div className="text-sm text-gray-600">Pass Rate</div>
						</div>
					</div>
				</div>
			</div>

			{/* Inspections Table */}
			<div className="bg-white rounded-lg border border-gray-200 shadow-sm">
				<div className="overflow-x-auto">
					<table className="min-w-full divide-y divide-gray-200">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Inspection
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Target
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Inspector
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Completed
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Status
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Results
								</th>
								<th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
									Actions
								</th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{filteredInspections.map((inspection) => {
								const { date, time } = formatDateTime(inspection.completedAt);
								return (
									<tr key={inspection.id} className="hover:bg-gray-50">
										<td className="px-6 py-4 whitespace-nowrap">
											<div className="text-sm font-medium text-gray-900">
												{inspection.templateName}
											</div>
											<div className="text-xs text-gray-500">
												Duration: {inspection.duration}
											</div>
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
											{inspection.target}
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
											{inspection.inspector}
										</td>
										<td className="px-6 py-4 whitespace-nowrap">
											<div className="text-sm text-gray-900">{date}</div>
											<div className="text-xs text-gray-500">{time}</div>
										</td>
										<td className="px-6 py-4 whitespace-nowrap">
											<div className="flex items-center">
												{getStatusIcon(inspection.status)}
												<span
													className={`ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(inspection.status)}`}
												>
													{getStatusText(inspection.status)}
												</span>
											</div>
											{inspection.criticalIssues > 0 && (
												<div className="text-xs text-red-600 mt-1">
													{inspection.criticalIssues} critical issue
													{inspection.criticalIssues > 1 ? "s" : ""}
												</div>
											)}
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
											<div className="text-green-600">
												{inspection.passedItems} passed
											</div>
											{inspection.failedItems > 0 && (
												<div className="text-red-600">
													{inspection.failedItems} failed
												</div>
											)}
										</td>
										<td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
											<button
												onClick={() => handleViewReport(inspection.id)}
												className="text-green-600 hover:text-green-900 inline-flex items-center"
											>
												<Eye className="h-4 w-4 mr-1" />
												View
											</button>
										</td>
									</tr>
								);
							})}
						</tbody>
					</table>
				</div>
			</div>

			{filteredInspections.length === 0 && (
				<div className="text-center py-12">
					<Calendar className="mx-auto h-12 w-12 text-gray-400" />
					<h3 className="mt-2 text-sm font-medium text-gray-900">
						No inspections found
					</h3>
					<p className="mt-1 text-sm text-gray-500">
						{searchTerm || filterStatus !== "all"
							? "Try adjusting your search or filter criteria."
							: "No completed inspections to display."}
					</p>
				</div>
			)}
		</div>
	);
};

export default InspectionHistory;
