import React, { useState, useEffect } from 'react';
import { useParams, useLocation } from 'react-router-dom';
import {
  LayoutDashboard,
  ClipboardList,
  History,
  Clock
} from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import TabContainer, { Tab } from '../components/data/shared/TabContainer';
import TasksDashboard from '../components/tasks/TasksDashboard';
import TaskRequestsList from '../components/tasks/TaskRequestsList';
import ActiveTasksList from '../components/tasks/ActiveTasksList';
import TaskHistoryList from '../components/tasks/TaskHistoryList';

import { SiteInfo } from '../types';
import { mockSite } from '../mock/taskData';

const TasksPage: React.FC = () => {
	const { siteId } = useParams<{ siteId: string }>();
	const location = useLocation();
	const [site] = useState<SiteInfo>(mockSite);
	const [activeTab, setActiveTab] = useState("dashboard");

  const validTabs = ['dashboard', 'requests', 'active', 'history'];

	// Handle URL path and hash navigation - this effect runs whenever the location changes
	useEffect(() => {
		const hash = location.hash.replace("#", "");

		if (hash && validTabs.includes(hash)) {
			setActiveTab(hash);
		} else if (!hash && location.pathname.endsWith('/tasks')) {
			setActiveTab("dashboard");
		}
	}, [location.pathname, location.hash]);

	// Also listen for direct hash changes (for browser back/forward)
	useEffect(() => {
		const handleHashChange = () => {
			const newHash = window.location.hash.replace("#", "");
			if (newHash && validTabs.includes(newHash)) {
				setActiveTab(newHash);
			} else if (!newHash) {
				setActiveTab("dashboard");
			}
		};

		window.addEventListener("hashchange", handleHashChange);
		return () => window.removeEventListener("hashchange", handleHashChange);
	}, []);

	const handleNavigateToTab = (tabId: string) => {
		setActiveTab(tabId);
		// Use hash navigation for tabs
		window.history.replaceState(null, "", `/sites/${siteId}/tasks#${tabId}`);
	};

  const breadcrumbs = [
    { name: 'Dashboard', path: '/' },
    { name: site.name, path: `/sites/${siteId}/dashboard` },
    { name: 'Task Management', path: `/sites/${siteId}/tasks` },
  ];

  const tabs: Tab[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: <LayoutDashboard className="h-4 w-4" />,
      content: <TasksDashboard siteId={siteId || ''} onNavigateToTab={handleNavigateToTab} />
    },
    {
      id: 'requests',
      label: 'Requests',
      icon: <Clock className="h-4 w-4" />,
      content: <TaskRequestsList siteId={siteId || ''} />
    },
    {
      id: 'active',
      label: 'Active',
      icon: <ClipboardList className="h-4 w-4" />,
      content: <ActiveTasksList siteId={siteId || ''} />
    },
    {
      id: 'history',
      label: 'History',
      icon: <History className="h-4 w-4" />,
      content: <TaskHistoryList siteId={siteId || ''} />
    }
  ];

  return (
    <FloatingCard title="Task Management" breadcrumbs={breadcrumbs}>
      <TabContainer
        tabs={tabs}
        activeTab={activeTab}
        onTabChange={handleNavigateToTab}
      />
    </FloatingCard>
  );
};

export default TasksPage;
