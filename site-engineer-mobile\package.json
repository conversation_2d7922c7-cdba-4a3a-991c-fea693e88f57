{"name": "site-engineer-mobile", "private": true, "version": "1.0.0", "type": "module", "description": "Mobile-first application for site engineers to manage workers, tasks, permits, and reports", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"axios": "^1.9.0", "lucide-react": "^0.515.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "workbox-webpack-plugin": "^7.3.0", "workbox-window": "^7.3.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/node": "^24.0.1", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "autoprefixer": "^10.4.21", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "postcss": "^8.5.5", "tailwindcss": "^3.4.0", "typescript": "~5.8.3", "typescript-eslint": "^8.30.1", "vite": "^6.3.5"}}