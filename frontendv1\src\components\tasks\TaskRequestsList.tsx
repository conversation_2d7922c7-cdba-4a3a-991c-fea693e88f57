import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Search,
  Clock,
  Calendar,
  MapPin,
  User
} from 'lucide-react';
import { Task } from '../../types/tasks';
import TaskPriorityBadge from './shared/TaskPriorityBadge';

interface TaskRequestsListProps {
  siteId: string;
}

const TaskRequestsList: React.FC<TaskRequestsListProps> = ({ siteId }) => {
  const navigate = useNavigate();
  const [requestedTasks, setRequestedTasks] = useState<Task[]>([]);
  const [filteredTasks, setFilteredTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');

  useEffect(() => {
    fetchRequestedTasks();
  }, [siteId]);

  useEffect(() => {
    filterTasks();
  }, [requestedTasks, searchTerm, priorityFilter]);

  const filterTasks = () => {
    let filtered = requestedTasks.filter(task =>
      task.status === 'permit-pending' || task.status === 'requested'
    );

    if (searchTerm) {
      filtered = filtered.filter(task =>
        task.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.category.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    if (priorityFilter !== 'all') {
      filtered = filtered.filter(task => task.priority === priorityFilter);
    }

    setFilteredTasks(filtered);
  };

  const fetchRequestedTasks = async () => {
    // Mock data - replace with actual API call
    const mockRequestedTasks: Task[] = [
      {
        id: "task-1",
        taskNumber: "TSK-2024-101",
        title: "Foundation Excavation",
        description: "Excavate foundation area for main building",
        category: "excavation",
        location: "Main Building - Foundation Area",
        siteId: siteId,
        plannedStartDate: new Date('2024-01-22T08:00:00'),
        plannedEndDate: new Date('2024-01-22T16:00:00'),
        estimatedDuration: 8,
        status: "permit-pending",
        priority: "critical",
        progressPercentage: 0,
        createdBy: "engineer-1",
        createdByName: "Site Engineer",
        assignedSupervisor: "supervisor-1",
        assignedSupervisorName: "John Smith",
        assignedWorkers: [],
        dependencies: [],
        requiresPermit: true,
        riskLevel: "high",
        safetyRequirements: [],
        requiredPPE: [],
        requiredTrainings: [],
        requiredCertifications: [],
        ramsDocuments: [],
        attachments: [],
        qualityChecks: [],
        complianceRequirements: [],
        createdAt: new Date('2024-01-20T10:00:00'),
        updatedAt: new Date('2024-01-20T10:00:00'),
        history: [],
        tags: ["excavation", "foundation", "main-building"],
        customFields: {}
      },
      {
        id: "task-2",
        taskNumber: "TSK-2024-102",
        title: "Excavation - Trench Installation",
        description: "Excavate trench for utility installation in Zone A",
        category: "excavation",
        location: "Zone A - North Wall",
        siteId: siteId,
        plannedStartDate: new Date('2024-01-23T07:00:00'),
        plannedEndDate: new Date('2024-01-23T15:00:00'),
        estimatedDuration: 8,
        status: "permit-pending",
        priority: "high",
        progressPercentage: 0,
        createdBy: "engineer-2",
        createdByName: "Construction Engineer",
        assignedSupervisor: "supervisor-2",
        assignedSupervisorName: "Mike Johnson",
        assignedWorkers: [],
        dependencies: [],
        requiresPermit: false,
        riskLevel: "high",
        safetyRequirements: [],
        requiredPPE: [],
        requiredTrainings: [],
        requiredCertifications: [],
        ramsDocuments: [],
        attachments: [],
        qualityChecks: [],
        complianceRequirements: [],
        createdAt: new Date('2024-01-21T14:00:00'),
        updatedAt: new Date('2024-01-21T14:00:00'),
        history: [],
        tags: ["excavation", "utilities", "zone-a"],
        customFields: {}
      }
    ];

    setRequestedTasks(mockRequestedTasks);
    setLoading(false);
  };

  const handleTaskClick = (taskId: string) => {
    navigate(`/sites/${siteId}/tasks/request/${taskId}`);
  };

  const getRequestAge = (createdAt: Date) => {
    const now = new Date();
    const diffMs = now.getTime() - createdAt.getTime();
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
    const diffDays = Math.floor(diffHours / 24);

    if (diffDays > 0) {
      return `${diffDays} day${diffDays > 1 ? 's' : ''} ago`;
    } else if (diffHours > 0) {
      return `${diffHours} hour${diffHours > 1 ? 's' : ''} ago`;
    } else {
      return 'Just now';
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="text-gray-500">Loading task requests...</div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold text-gray-900">Task Requests</h2>
          <p className="text-sm text-gray-600">
            Tasks pending approval from HSE team
          </p>
        </div>
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-500">
            {filteredTasks.length} request{filteredTasks.length !== 1 ? 's' : ''}
          </span>
        </div>
      </div>

      {/* Filters */}
      <div className="bg-gray-50 rounded-lg p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Search
            </label>
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search tasks..."
                className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              />
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Priority
            </label>
            <select
              value={priorityFilter}
              onChange={(e) => setPriorityFilter(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="all">All Priorities</option>
              <option value="critical">Critical</option>
              <option value="high">High</option>
              <option value="medium">Medium</option>
              <option value="low">Low</option>
            </select>
          </div>
        </div>
      </div>

      {/* Task Requests List */}
      <div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
        {filteredTasks.length === 0 ? (
          <div className="p-12 text-center">
            <Clock className="h-12 w-12 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">No task requests found</h3>
            <p className="text-gray-500">
              {searchTerm || priorityFilter !== 'all'
                ? 'Try adjusting your filters to see more results.'
                : 'All task requests have been processed.'
              }
            </p>
          </div>
        ) : (
          <div className="divide-y divide-gray-200">
            {filteredTasks.map((task) => (
              <div
                key={task.id}
                className="p-6 hover:bg-gray-50 cursor-pointer transition-colors"
                onClick={() => handleTaskClick(task.id)}
              >
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h3 className="text-lg font-medium text-gray-900 hover:text-blue-600">
                        {task.title}
                      </h3>
                      <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800 font-medium">
                        PENDING APPROVAL
                      </span>
                      <TaskPriorityBadge priority={task.priority} size="sm" />
                    </div>

                    <p className="text-sm text-gray-600 mb-3">{task.description}</p>

                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
                      <div className="flex items-center space-x-2">
                        <MapPin className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-600">{task.location}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <User className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-600">Requested by {task.createdByName}</span>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Calendar className="h-4 w-4 text-gray-400" />
                        <span className="text-gray-600">
                          Planned: {task.plannedStartDate.toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="text-right ml-4">
                    <div className="text-sm text-gray-500 mb-1">
                      {getRequestAge(task.createdAt)}
                    </div>
                    <div className="text-xs text-gray-400">
                      {task.taskNumber}
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
};

export default TaskRequestsList;
