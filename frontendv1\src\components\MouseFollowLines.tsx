import React, { useEffect, useRef } from 'react';

interface MouseFollowLinesProps {
  className?: string;
}

const MouseFollowLines: React.FC<MouseFollowLinesProps> = ({ className = '' }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const mouseRef = useRef({ x: 0, y: 0 });
  const linesRef = useRef<Array<{ x: number; y: number; targetX: number; targetY: number; opacity: number }>>([]);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Initialize lines
    const initLines = () => {
      linesRef.current = [];
      for (let i = 0; i < 8; i++) {
        linesRef.current.push({
          x: Math.random() * canvas.width,
          y: Math.random() * canvas.height,
          targetX: 0,
          targetY: 0,
          opacity: Math.random() * 0.3 + 0.1,
        });
      }
    };

    initLines();

    // Mouse move handler
    const handleMouseMove = (e: MouseEvent) => {
      mouseRef.current.x = e.clientX;
      mouseRef.current.y = e.clientY;
    };

    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      // Update and draw lines
      linesRef.current.forEach((line, index) => {
        // Calculate target position based on mouse with some offset
        const angle = (index / linesRef.current.length) * Math.PI * 2;
        const distance = 100 + index * 20;
        line.targetX = mouseRef.current.x + Math.cos(angle) * distance;
        line.targetY = mouseRef.current.y + Math.sin(angle) * distance;

        // Smooth movement towards target
        line.x += (line.targetX - line.x) * 0.05;
        line.y += (line.targetY - line.y) * 0.05;

        // Create gradient for the line
        const gradient = ctx.createLinearGradient(
          mouseRef.current.x,
          mouseRef.current.y,
          line.x,
          line.y
        );
        gradient.addColorStop(0, `rgba(147, 51, 234, ${line.opacity})`); // purple
        gradient.addColorStop(0.5, `rgba(236, 72, 153, ${line.opacity})`); // pink
        gradient.addColorStop(1, `rgba(249, 115, 22, ${line.opacity})`); // orange

        // Draw line
        ctx.beginPath();
        ctx.moveTo(mouseRef.current.x, mouseRef.current.y);
        ctx.lineTo(line.x, line.y);
        ctx.strokeStyle = gradient;
        ctx.lineWidth = 2;
        ctx.stroke();

        // Draw small circle at line end
        ctx.beginPath();
        ctx.arc(line.x, line.y, 3, 0, Math.PI * 2);
        ctx.fillStyle = gradient;
        ctx.fill();
      });

      requestAnimationFrame(animate);
    };

    // Start animation
    document.addEventListener('mousemove', handleMouseMove);
    animate();

    // Cleanup
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      document.removeEventListener('mousemove', handleMouseMove);
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className={`fixed inset-0 pointer-events-none z-10 ${className}`}
      style={{ mixBlendMode: 'multiply' }}
    />
  );
};

export default MouseFollowLines;
