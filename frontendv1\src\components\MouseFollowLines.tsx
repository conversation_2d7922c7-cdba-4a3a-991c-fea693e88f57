import React, { useEffect, useRef } from 'react';

interface GraphPaperLinesProps {
  className?: string;
}

interface GridLine {
  x: number;
  y: number;
  type: 'horizontal' | 'vertical';
  opacity: number;
  createdAt: number;
  length: number;
}

const GraphPaperLines: React.FC<GraphPaperLinesProps> = ({ className = '' }) => {
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const mouseRef = useRef({ x: 0, y: 0 });
  const gridLinesRef = useRef<GridLine[]>([]);
  const lastMouseMoveRef = useRef(0);

  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;

    const ctx = canvas.getContext('2d');
    if (!ctx) return;

    const GRID_SIZE = 20;
    const FADE_DURATION = 2000; // 2 seconds
    const LINE_SPAWN_DELAY = 100; // milliseconds

    // Set canvas size
    const resizeCanvas = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    resizeCanvas();
    window.addEventListener('resize', resizeCanvas);

    // Mouse move handler
    const handleMouseMove = (e: MouseEvent) => {
      const now = Date.now();
      mouseRef.current.x = e.clientX;
      mouseRef.current.y = e.clientY;

      // Throttle line creation
      if (now - lastMouseMoveRef.current > LINE_SPAWN_DELAY) {
        lastMouseMoveRef.current = now;

        // Create grid lines around mouse position
        const mouseGridX = Math.round(mouseRef.current.x / GRID_SIZE) * GRID_SIZE;
        const mouseGridY = Math.round(mouseRef.current.y / GRID_SIZE) * GRID_SIZE;

        // Add horizontal lines
        for (let i = -2; i <= 2; i++) {
          const y = mouseGridY + (i * GRID_SIZE);
          if (y >= 0 && y <= canvas.height) {
            gridLinesRef.current.push({
              x: mouseGridX - GRID_SIZE * 3,
              y: y,
              type: 'horizontal',
              opacity: 0.6,
              createdAt: now,
              length: GRID_SIZE * 6
            });
          }
        }

        // Add vertical lines
        for (let i = -2; i <= 2; i++) {
          const x = mouseGridX + (i * GRID_SIZE);
          if (x >= 0 && x <= canvas.width) {
            gridLinesRef.current.push({
              x: x,
              y: mouseGridY - GRID_SIZE * 3,
              type: 'vertical',
              opacity: 0.6,
              createdAt: now,
              length: GRID_SIZE * 6
            });
          }
        }

        // Limit number of lines to prevent memory issues
        if (gridLinesRef.current.length > 200) {
          gridLinesRef.current = gridLinesRef.current.slice(-100);
        }
      }
    };

    // Animation loop
    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      const now = Date.now();

      // Update and draw grid lines
      gridLinesRef.current = gridLinesRef.current.filter(line => {
        const age = now - line.createdAt;

        // Calculate opacity based on age
        if (age > FADE_DURATION) {
          return false; // Remove old lines
        }

        const fadeProgress = age / FADE_DURATION;
        line.opacity = 0.6 * (1 - fadeProgress);

        // Create gradient
        const gradient = line.type === 'horizontal'
          ? ctx.createLinearGradient(line.x, line.y, line.x + line.length, line.y)
          : ctx.createLinearGradient(line.x, line.y, line.x, line.y + line.length);

        gradient.addColorStop(0, `rgba(249, 115, 22, 0)`); // transparent orange
        gradient.addColorStop(0.5, `rgba(249, 115, 22, ${line.opacity})`); // orange
        gradient.addColorStop(1, `rgba(249, 115, 22, 0)`); // transparent orange

        // Draw line
        ctx.beginPath();
        ctx.strokeStyle = gradient;
        ctx.lineWidth = 1;

        if (line.type === 'horizontal') {
          ctx.moveTo(line.x, line.y);
          ctx.lineTo(line.x + line.length, line.y);
        } else {
          ctx.moveTo(line.x, line.y);
          ctx.lineTo(line.x, line.y + line.length);
        }

        ctx.stroke();
        return true;
      });

      requestAnimationFrame(animate);
    };

    // Start animation
    document.addEventListener('mousemove', handleMouseMove);
    animate();

    // Cleanup
    return () => {
      window.removeEventListener('resize', resizeCanvas);
      document.removeEventListener('mousemove', handleMouseMove);
    };
  }, []);

  return (
    <canvas
      ref={canvasRef}
      className={`fixed inset-0 pointer-events-none z-10 ${className}`}
      style={{ mixBlendMode: 'multiply' }}
    />
  );
};

export default GraphPaperLines;
