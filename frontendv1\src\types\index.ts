// Tenant (Organization) - Root level entity
export interface Tenant {
  id: string;
  name: string;
  subdomain: string;
  subscriptionPlan: 'basic' | 'professional' | 'enterprise';
  maxSites: number;
  status: 'active' | 'inactive' | 'suspended';
  createdAt: string;
  updatedAt?: string;
}

// Site (Project) - Container for work, not resource owner
export interface SiteInfo {
  id: string;
  tenantId: string; // Belongs to tenant
  name: string;
  healthStatus: 'green' | 'amber' | 'red';
  workersOnSite: number;
  activePermits: number;
  openIncidents: number;
  projectManager: string;
  location: string;
  timeline?: string;
  currentPhase?: string;
  progressPercentage?: number;
  startDate?: Date;
  endDate?: Date;
  status: 'planning' | 'active' | 'on-hold' | 'completed' | 'cancelled';
  createdAt: Date;
  updatedAt?: Date;
  phase?: string;
  progress?: number;
  totalWorkers?: number;
  activeWorkers?: number;
  workforce?: number;
  safetyScore?: number;
}

export interface KPI {
	title: string;
	value: string | number;
	change?: number;
	icon?: React.ReactNode;
}

export interface MenuItem {
	name: string;
	icon: React.ReactNode;
	path: string;
	submenu?: {
		title: string;
		items: {
			name: string;
			path: string;
		}[];
	};
}

export interface SiteMenuItem {
	name: string;
	icon: React.ReactNode;
	path: string;
	submenu?: {
		title: string;
		items: {
			name: string;
			path: string;
			action?: "add" | "view" | "manage";
		}[];
	};
}

export interface NavigationContext {
	isCompanyLevel: boolean;
	isSiteLevel: boolean;
	siteId?: string;
	siteName?: string;
}

// Tenant-level master data entities
export interface Trade {
  id: number;
  tenantId: string; // Belongs to tenant
  name: string;
  description?: string;
  requiredCertifications?: string[];
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
}

export interface Skill {
  id: number;
  tenantId: string; // Belongs to tenant
  name: string;
  description?: string;
  certificationRequired: boolean;
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
}

export enum TrainingStatus {
  Scheduled = 'Scheduled',
  InProgress = 'InProgress',
  Completed = 'Completed',
  Cancelled = 'Cancelled',
  Expired = 'Expired',
  Renewed = 'Renewed'
}

export interface WorkerTrainingHistory {
  id: number;
  workerId: number;
  trainingId: number;
  completionDate: string;
  expiryDate?: string;
  score?: number;
  notes?: string;
  status: TrainingStatus;
  worker?: Worker;
  training?: Training;
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
}

export interface WorkerAttendance {
  id: number;
  workerId: number;
  worker?: Worker;
  checkInTime: string;
  checkOutTime?: string;
  status: string;
  notes?: string;
  isVerifiedByHikvision: boolean;
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
}

export interface ToolboxSession {
  id: number;
  sessionTime: string; // DateTime from backend
  topic: string;
  conductor: string;
  photoUrl?: string; // Made optional to match backend
  notes?: string;
  attendances: ToolboxAttendance[];
  // Audit fields to match backend IAuditableEntity
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
}

export interface ToolboxAttendance {
  id: number;
  toolboxSessionId: number;
  workerId: number;
  toolboxSession?: ToolboxSession;
  worker?: Worker;
  wasPresent: boolean;
  notes?: string;
  // Audit fields to match backend IAuditableEntity
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
}

// Updated Worker interface to match backend
export interface Worker {
  // Core identification
  id: number;                           // Changed from string
  tenantId: string;                     // Belongs to tenant (organization)
  name: string;

  // Personal information (added from backend)
  company: string;                      // Added
  nationalId: string;
  phoneNumber: string;                  // Renamed from phone
  email?: string;
  dateOfBirth?: string;                 // Added (ISO date string)
  gender: string;                       // Added

  // Work-related information (added from backend)
  manHours: number;                     // Added
  photoUrl?: string;                    // Renamed from photo
  inductionDate?: string;               // Added (ISO date string)
  medicalCheckDate?: string;            // Added (ISO date string)
  rating: number;                       // Added (0-5 scale)
  hireDate?: string;                    // Added
  status: 'active' | 'inactive' | 'terminated'; // Added

  // Navigation Properties (updated to match backend)
  trades: Trade[];                      // Changed from string[] to Trade[]
  skills: Skill[];                      // Added
  trainings: Training[];
  trainingHistory: WorkerTrainingHistory[]; // Added
  certifications: WorkerCertification[]; // Added
  siteAssignments: WorkerSiteAssignment[]; // Site assignments via junction table

  // Computed Properties (from backend)
  age?: number;                         // Added
  trainingsCompleted: number;           // Added

  // Audit Fields (added from backend)
  createdAt: string;                    // Added
  createdBy: string;                    // Added
  updatedAt?: string;                   // Added
  updatedBy?: string;                   // Added
}

// Junction table interfaces for resource assignments
export interface WorkerSiteAssignment {
  id: number;
  workerId: number;
  siteId: string;
  role: string;
  startDate: string;
  endDate?: string;
  status: 'active' | 'inactive' | 'completed';
  hourlyRate?: number;
  notes?: string;
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
  // Navigation properties
  worker?: Worker;
  site?: SiteInfo;
}

export interface WorkerTrade {
  id: number;
  workerId: number;
  tradeId: number;
  certifiedDate: string;
  expiryDate?: string;
  level: 'apprentice' | 'journeyman' | 'master';
  certificationNumber?: string;
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
  // Navigation properties
  worker?: Worker;
  trade?: Trade;
}

export interface WorkerSkill {
  id: number;
  workerId: number;
  skillId: number;
  acquiredDate: string;
  expiryDate?: string;
  proficiencyLevel: 'beginner' | 'intermediate' | 'advanced' | 'expert';
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
  // Navigation properties
  worker?: Worker;
  skill?: Skill;
}

export interface WorkerCertification {
	id: string;
	name: string;
	issueDate: string;
	expiryDate: string;
	status: "valid" | "expiring" | "expired";
	documentUrl?: string;
}

// Updated Training interface to match backend
export interface Training {
  // Core identification
  id: number;                           // Changed from string
  tenantId: string;                     // Belongs to tenant
  name: string;
  description?: string;

  // Training schedule (added from backend)
  startDate?: string;                   // Added (ISO date string)
  endDate?: string;                     // Added (ISO date string)
  duration?: string;                    // Added (e.g., "2h 30m")
  validityPeriodMonths?: number;        // Added
  trainingType?: string;                // Added
  trainer?: string;                     // Added
  frequency?: string;                   // Added
  status: TrainingStatus;               // Added enum

  // Navigation Properties
  workers: Worker[];                    // Added
  trainingHistory: WorkerTrainingHistory[]; // Added

  // Audit Fields
  createdAt: string;
  createdBy: string;
  updatedAt?: string;
  updatedBy?: string;
}

export interface WorkerTraining {
	id: string;
	name: string;
	completionDate: string;
	expiryDate: string;
	status: "valid" | "expiring" | "expired";
	documentUrl?: string;
}

export interface TimeLog {
  id: string;
  workerId: string;
  workerName: string;
  workerPhoto?: string;
  workerTrade: string;
  date: string;
  clockIn?: string;
  clockOut?: string;
  breakDuration?: number; // in minutes
  totalHours?: number;
  overtime?: number;
  status: 'on-site' | 'late' | 'absent' | 'off-site';
  toolboxTalkAttended: boolean;
  isManuallyEdited?: boolean;
  editReason?: string;
  editedBy?: string;
  editedAt?: string;
  isVerifiedByHikvision?: boolean; // Face recognition verification
  hikvisionPersonId?: string; // Hikvision person ID for face recognition
  terminalId?: string; // Terminal used for check-in/out
}

export interface Incident {
	id: string;
	date: string;
	type: string;
	severity: "low" | "medium" | "high" | "critical";
	description: string;
	location: string;
	reportedBy: string;
	status: "reported" | "investigating" | "resolved" | "closed";
	involvedWorkers: string[];
}

// Export document types
export * from './documents';

// Export weather types
export * from './weather';
