/**
 * Modal Scrolling Fix
 * This script adds additional fixes for modal scrolling issues
 */

document.addEventListener('DOMContentLoaded', function() {
  // Get modal elements
  const modal = document.getElementById('project-modal');
  const modalInner = modal ? modal.querySelector('.modal-inner') : null;
  const closeBtn = modal ? modal.querySelector('.close-modal') : null;
  
  if (!modal || !modalInner || !closeBtn) {
    console.error('Modal elements not found');
    return;
  }
  
  // Ensure close button works
  closeBtn.addEventListener('click', function(event) {
    console.log('Close button clicked from modal-fix.js');
    event.preventDefault();
    event.stopPropagation();
    
    // Hide modal directly
    modal.style.display = 'none';
    
    // Remove modal-open class from body
    document.body.classList.remove('modal-open');
    
    // Restore scroll position
    const scrollY = modal.dataset.scrollY ? parseInt(modal.dataset.scrollY) : 0;
    const scrollX = modal.dataset.scrollX ? parseInt(modal.dataset.scrollX) : 0;
    window.scrollTo(scrollX, scrollY);
  });
  
  // Add additional touchpad/wheel event handling
  modalInner.addEventListener('wheel', function(event) {
    // Don't prevent default - let the browser handle scrolling naturally
    console.log('Wheel event on modal inner from modal-fix.js');
  }, { passive: true });
  
  // Add touch event handling
  modalInner.addEventListener('touchstart', function() {
    console.log('Touch start on modal inner from modal-fix.js');
  }, { passive: true });
  
  modalInner.addEventListener('touchmove', function() {
    console.log('Touch move on modal inner from modal-fix.js');
  }, { passive: true });
  
  // Ensure modal inner is scrollable
  function ensureScrollable() {
    if (modal.style.display === 'block') {
      // Force scrollable state
      modalInner.style.overflowY = 'scroll';
      
      // Check if content is taller than container
      const isScrollable = modalInner.scrollHeight > modalInner.clientHeight;
      console.log('Modal should be scrollable:', isScrollable,
        'scrollHeight:', modalInner.scrollHeight,
        'clientHeight:', modalInner.clientHeight
      );
      
      // If not scrollable but should be, add some padding to force scrolling
      if (!isScrollable && modalInner.scrollHeight > 300) {
        modalInner.style.paddingBottom = '100px';
      }
    }
  }
  
  // Run on load and resize
  window.addEventListener('resize', ensureScrollable);
  
  // Override modal display method
  const originalDisplay = modal.style.display;
  Object.defineProperty(modal.style, 'display', {
    set: function(value) {
      this.cssText = this.cssText.replace(/display:.*?;/, '') + 'display: ' + value + ';';
      if (value === 'block') {
        setTimeout(ensureScrollable, 10);
      }
    },
    get: function() {
      return this.cssText.match(/display:\s*(.*?)\s*;/) ? 
        this.cssText.match(/display:\s*(.*?)\s*;/)[1] : originalDisplay;
    }
  });
});
