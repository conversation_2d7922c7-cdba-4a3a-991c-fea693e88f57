import { useState, useEffect } from "react";
import { useParams, useLocation } from "react-router-dom";
import {
	BarChart3,
  FileText } from "lucide-react";
import FloatingCard from "../components/layout/FloatingCard";
import TabContainer, { Tab } from "../components/data/shared/TabContainer";
import WeatherDashboard from "../components/weather/WeatherDashboard";
import LoggedWeatherData from "../components/weather/LoggedWeatherData";

const WeatherPage = () => {
	const { siteId } = useParams<{ siteId: string }>();
	const location = useLocation();
	const [activeTab, setActiveTab] = useState("dashboard");

	// Handle hash navigation
	useEffect(() => {
		const hash = location.hash.replace("#", "");
		if (
			hash &&
			[
				"dashboard",
				"logged-data",
			].includes(hash)
		) {
			setActiveTab(hash);
		}
	}, [location.hash]);

	// Update URL hash when tab changes
	const handleTabChange = (tabId: string) => {
		setActiveTab(tabId);
		window.location.hash = tabId;
	};

	const tabs: Tab[] = [
		{
			id: "dashboard",
			label: "Weather Dashboard",
			icon: <BarChart3 className="h-4 w-4" />,
			content: (
				<WeatherDashboard
					siteId={siteId!}
					onNavigateToTab={handleTabChange}
				/>
			) },
		{
			id: "logged-data",
			label: "Logged Weather Data",
			icon: <FileText className="h-4 w-4" />,
			content: <LoggedWeatherData siteId={siteId!} /> },
	];

	const breadcrumbs = [
		{ name: "Sites", path: "/" },
		{ name: "Site Dashboard", path: `/sites/${siteId}/dashboard` },
		{ name: "Weather", path: `/sites/${siteId}/weather` },
	];

	return (
		<FloatingCard title="Weather Management" breadcrumbs={breadcrumbs}>
			<TabContainer
				tabs={tabs}
				activeTab={activeTab}
				onTabChange={handleTabChange}
			/>
		</FloatingCard>
	);
};

export default WeatherPage;
