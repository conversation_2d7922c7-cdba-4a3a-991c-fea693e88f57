import React from 'react';
import { ArrowLeft } from 'lucide-react';
import FloatingCard from '../layout/FloatingCard';

interface PermitFormDisplayProps {
  permitType: 'excavation' | 'confined-space' | 'general-work' | 'hot-work' | 'work-at-height';
  permitData: any;
  onBack: () => void;
}

const PermitFormDisplay: React.FC<PermitFormDisplayProps> = ({ permitType, permitData, onBack }) => {
  const getPermitConfig = () => {
    switch (permitType) {
      case 'excavation':
        return {
          title: 'Excavation Permit to Work',
          headerTitle: 'EXCAVATION PERMIT TO WORK',
          headerColor: 'orange',
          description: 'This permit is valid for one day ONLY. Tick as appropriate.',
          serialNumber: 'EXC-2024-001'
        };
      case 'confined-space':
        return {
          title: 'Confined Space Entry Permit',
          headerTitle: 'CONFINED SPACE ENTRY PERMIT',
          headerColor: 'blue',
          description: 'This permit is valid for the specified work period only.',
          serialNumber: 'CSE-2024-002'
        };
      case 'general-work':
        return {
          title: 'General Permit to Work',
          headerTitle: 'GENERAL PERMIT TO WORK',
          headerColor: 'green',
          description: 'This permit is valid for 7 days.',
          serialNumber: 'GWP-2024-003'
        };
      case 'hot-work':
        return {
          title: 'Hot Work Permit',
          headerTitle: 'HOT WORK PERMIT',
          headerColor: 'red',
          description: 'This permit is valid for one day ONLY.',
          serialNumber: 'HWP-2024-004'
        };
      case 'work-at-height':
        return {
          title: 'Work at Height Permit',
          headerTitle: 'WORK AT HEIGHT PERMIT',
          headerColor: 'purple',
          description: 'This permit is valid for the specified work period only.',
          serialNumber: 'WAH-2024-005'
        };
      default:
        return {
          title: 'Permit to Work',
          headerTitle: 'PERMIT TO WORK',
          headerColor: 'gray',
          description: 'This permit is valid for the specified work period only.',
          serialNumber: 'PTW-2024-001'
        };
    }
  };

  const config = getPermitConfig();

  const getHeaderColorClasses = (color: string) => {
    switch (color) {
      case 'orange':
        return 'bg-orange-50 border-orange-200 text-orange-800';
      case 'blue':
        return 'bg-blue-50 border-blue-200 text-blue-800';
      case 'green':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'red':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'purple':
        return 'bg-purple-50 border-purple-200 text-purple-800';
      default:
        return 'bg-gray-50 border-gray-200 text-gray-800';
    }
  };





  return (
    <FloatingCard title={config.title}>
      <div className="h-full bg-gray-50">
        {/* Header */}
        <div className="bg-white border-b border-gray-200 px-4 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={onBack}
                className="inline-flex items-center px-3 py-2 border border-gray-300 rounded-md text-sm leading-4 font-medium text-gray-700 bg-white hover:bg-gray-50"
              >
                <ArrowLeft className="h-4 w-4 mr-2" />
                Back
              </button>
              <div>
                <h1 className="text-xl font-bold text-gray-900">{config.title}</h1>
                <p className="text-xs text-gray-500">Permit Display - Read Only</p>
              </div>
            </div>
          </div>
        </div>

        {/* Form Content */}
        <div className="w-full p-4">
          <div className="space-y-4">
            {/* Header Section */}
            <div className={`border rounded-lg p-4 ${getHeaderColorClasses(config.headerColor)}`}>
              <div className="text-center">
                <div className="flex items-center justify-center gap-4 mb-2">
                  <h2 className="text-lg font-bold">{config.headerTitle}</h2>
                  <span className="text-xs text-gray-500 font-light">Serial No: {config.serialNumber}</span>
                </div>
                <div className="text-xs text-red-600 font-medium">
                  {config.description}
                </div>
              </div>
            </div>

            {/* Basic Information */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Basic Information</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Project Name</label>
                  <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm text-gray-700">
                    {permitData.projectName || 'Building Maintenance Project'}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Location</label>
                  <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm text-gray-700">
                    {permitData.location || 'Main Building - Level 2'}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Start Date & Time</label>
                  <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm text-gray-700">
                    {permitData.startDateTime || new Date().toLocaleString()}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">End Date & Time</label>
                  <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm text-gray-700">
                    {permitData.endDateTime || new Date(Date.now() + 24 * 60 * 60 * 1000).toLocaleString()}
                  </div>
                </div>
              </div>
            </div>

            {/* Work Description */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Work Description</h3>
              <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm text-gray-700 min-h-[100px]">
                {permitData.workDescription || `${config.title} activities including safety inspections, equipment setup, and completion of required work tasks according to established safety protocols and procedures.`}
              </div>
            </div>

            {/* Hazards and Precautions */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Hazards and Precautions</h3>
              <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm text-gray-700 min-h-[100px]">
                {permitData.hazards || 'Standard safety hazards identified and appropriate precautions implemented. All personnel briefed on safety requirements and emergency procedures.'}
              </div>
            </div>

            {/* Permit Issue Section */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Permit Issue</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Issued By</label>
                  <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm text-gray-700">
                    {permitData.issuedBy || 'John Mwangi'}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Issue Date & Time</label>
                  <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm text-gray-700">
                    {permitData.issueDateTime || new Date().toLocaleString()}
                  </div>
                </div>
              </div>
            </div>

            {/* Permit Return Section */}
            <div className="bg-white border border-gray-200 rounded-lg p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Permit Return</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Returned By</label>
                  <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm text-gray-700">
                    {permitData.returnedBy || 'Sarah Njeri'}
                  </div>
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">Return Date & Time</label>
                  <div className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-sm text-gray-700">
                    {permitData.returnDateTime || new Date(Date.now() + 8 * 60 * 60 * 1000).toLocaleString()}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </FloatingCard>
  );
};

export default PermitFormDisplay;
