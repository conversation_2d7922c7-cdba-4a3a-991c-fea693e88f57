 .u-section-1 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-1 .u-sheet-1 {
  min-height: 295px;
}

.u-section-1 .u-text-1 {
  margin: 20px 0 0;
}

.u-section-1 .u-icon-1 {
  font-size: 1.1112em;
}

.u-section-1 .u-btn-1 {
  border-style: solid;
  padding: 0;
}

.u-section-1 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: -24px;
}

.u-section-1 .u-layout-cell-1 {
  min-height: 213px;
}

.u-section-1 .u-container-layout-1 {
  padding: 0;
}

.u-section-1 .u-text-2 {
  margin: -4px 356px 0 0;
}

.u-section-1 .u-text-3 {
  margin: 20px 16px 0 0;
}

.u-section-1 .u-layout-cell-2 {
  min-height: 213px;
}

.u-section-1 .u-container-layout-2 {
  padding: 30px;
}

.u-section-1 .u-text-4 {
  font-weight: 400;
  margin: 15px 0 0;
}

@media (max-width: 1199px) {
  .u-section-1 .u-sheet-1 {
    min-height: 311px;
  }

  .u-section-1 .u-layout-wrap-1 {
    position: relative;
    margin-bottom: -101px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 178px;
  }

  .u-section-1 .u-text-2 {
    margin-right: 256px;
  }

  .u-section-1 .u-text-3 {
    margin-right: 0;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 178px;
    --top-left-radius: 200px;
    --radius: 20px;
    background-position: 50% 50%;
  }
}

@media (max-width: 991px) {
  .u-section-1 .u-sheet-1 {
    min-height: 369px;
  }

  .u-section-1 .u-layout-wrap-1 {
    margin-bottom: -177px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-1 .u-text-2 {
    margin-right: 146px;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 267px;
  }
}

@media (max-width: 767px) {
  .u-section-1 .u-sheet-1 {
    min-height: 390px;
  }

  .u-section-1 .u-layout-wrap-1 {
    margin-bottom: -222px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 134px;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 178px;
  }

  .u-section-1 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-text-4 {
    margin-top: 0;
  }
}

@media (max-width: 575px) {
  .u-section-1 .u-sheet-1 {
    min-height: 465px;
  }

  .u-section-1 .u-layout-wrap-1 {
    margin-bottom: -200px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 154px;
  }

  .u-section-1 .u-text-2 {
    margin-right: 0;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 233px;
  }
} .u-section-2 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-2 .u-sheet-1 {
  min-height: 520px;
}

.u-section-2 .u-layout-wrap-1 {
  margin-top: 60px;
  margin-bottom: -60px;
  position: relative;
}

.u-section-2 .u-layout-cell-1 {
  min-height: 210px;
  background-repeat: no-repeat;
  background-size: cover;
  --radius: 10px;
}

.u-section-2 .u-image-1 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: url("../images/precon1.webp");
  background-size: cover;
}

.u-section-2 .u-container-layout-1 {
  padding: 30px;
}

.u-section-2 .u-layout-cell-2 {
  min-height: 210px;
  background-repeat: no-repeat;
  background-size: cover;
  --radius: 10px;
}

.u-section-2 .u-image-2 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: url("../images/hselaxmi5.webp");
  background-size: cover;
}

.u-section-2 .u-container-layout-2 {
  padding: 30px;
}

.u-section-2 .u-layout-cell-3 {
  min-height: 210px;
  background-repeat: no-repeat;
  background-size: cover;
  --radius: 10px;
}

.u-section-2 .u-image-3 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: url("../images/laxmi1.webp");
  background-size: cover;
}

.u-section-2 .u-container-layout-3 {
  padding: 30px;
}

.u-section-2 .u-layout-cell-4 {
  min-height: 210px;
  background-repeat: no-repeat;
  background-size: cover;
  --radius: 10px;
}

.u-section-2 .u-image-4 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: url("../images/hselaxmi4.webp");
  background-size: cover;
}

.u-section-2 .u-container-layout-4 {
  padding: 30px;
}

.u-section-2 .u-layout-cell-5 {
  min-height: 420px;
  --radius: 20px;
}

.u-section-2 .u-container-layout-5 {
  padding: 30px;
}

.u-section-2 .u-text-1 {
  font-size: 1.5rem;
  margin: 0 20px 0 0;
}

.u-section-2 .u-text-2 {
  font-weight: 500;
  margin: 20px 20px 0 0;
}

.u-section-2 .u-text-3 {
  margin: 20px 20px 0 0;
}

.u-section-2 .u-btn-1 {
  border-style: solid;
  padding: 0;
}

.u-section-2 .u-icon-1 {
  font-size: 1.1112em;
}

@media (max-width: 1199px) {
  .u-section-2 .u-sheet-1 {
    min-height: 410px;
  }

  .u-section-2 .u-layout-wrap-1 {
    margin-top: 20px;
  }

  .u-section-2 .u-layout-cell-1 {
    min-height: 175px;
    background-position: 50% 50%;
  }

  .u-section-2 .u-layout-cell-2 {
    min-height: 175px;
    background-position: 50% 50%;
  }

  .u-section-2 .u-layout-cell-3 {
    min-height: 175px;
    background-position: 50% 50%;
  }

  .u-section-2 .u-layout-cell-4 {
    min-height: 175px;
    background-position: 50% 50%;
  }

  .u-section-2 .u-layout-cell-5 {
    min-height: 350px;
  }

  .u-section-2 .u-text-1 {
    margin-right: 0;
  }

  .u-section-2 .u-text-2 {
    margin-right: 0;
  }

  .u-section-2 .u-text-3 {
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-2 .u-sheet-1 {
    min-height: 871px;
  }

  .u-section-2 .u-layout-wrap-1 {
    margin-bottom: -501px;
  }

  .u-section-2 .u-layout-cell-1 {
    min-height: 273px;
  }

  .u-section-2 .u-layout-cell-2 {
    min-height: 273px;
  }

  .u-section-2 .u-layout-cell-3 {
    min-height: 273px;
  }

  .u-section-2 .u-layout-cell-4 {
    min-height: 273px;
  }

  .u-section-2 .u-layout-cell-5 {
    min-height: 100px;
  }
}

@media (max-width: 767px) {
  .u-section-2 .u-sheet-1 {
    min-height: 1849px;
  }

  .u-section-2 .u-layout-wrap-1 {
    margin-bottom: -762px;
  }

  .u-section-2 .u-layout-cell-1 {
    min-height: 378px;
  }

  .u-section-2 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-2 .u-layout-cell-2 {
    min-height: 378px;
  }

  .u-section-2 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-2 .u-layout-cell-3 {
    min-height: 378px;
  }

  .u-section-2 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-2 .u-layout-cell-4 {
    min-height: 378px;
  }

  .u-section-2 .u-container-layout-4 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-2 .u-sheet-1 {
    min-height: 1320px;
  }

  .u-section-2 .u-layout-wrap-1 {
    margin-bottom: -852px;
  }

  .u-section-2 .u-layout-cell-1 {
    min-height: 238px;
  }

  .u-section-2 .u-layout-cell-2 {
    min-height: 238px;
  }

  .u-section-2 .u-layout-cell-3 {
    min-height: 238px;
  }

  .u-section-2 .u-layout-cell-4 {
    min-height: 238px;
  }

  .u-section-2 .u-container-layout-5 {
    padding: 20px;
  }
}