/* Hero Section Styles */
.hero {
    position: relative;
    width: 100%;
    height: 80vh; /* 80% of viewport height for desktop */
    min-height: 600px; /* Minimum height of 600px */
    overflow: hidden;
    display: flex;
    color: #ffffff;
    font-family: 'Figtree', sans-serif;
    z-index: 1; /* Set hero section to low z-index */
}

.hero__background-slides {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 1; /* Low z-index within hero context */
}

.hero__background-slide {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-size: cover;
    background-position: center;
    opacity: 0;
    transition: opacity 0.8s ease-in-out;
    z-index: 1; /* Low z-index within hero context */
}

.hero__background-slide--active {
    opacity: 1;
    z-index: 2; /* Low z-index within hero context */
}

.hero__overlay-container {
    position: relative;
    z-index: 3; /* Low z-index within hero context */
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-end; /* Push content to bottom */
    padding: 0; /* Remove padding to allow full-width lines */
    box-sizing: border-box;
}

.hero__main-content-area {
    width: 100%;
    height: 50%; /* Take up the lower half of the hero */
    position: relative;
    padding: 2rem;
    box-sizing: border-box;
}

/* Responsive height adjustments */
@media (max-width: 1200px) {
    .hero__main-content-area {
        font-size: 1.2rem;
    }
}

/* Full-width horizontal line at the vertical center */
.hero__main-content-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: #fcf7fb; /* Updated to new specified color */
    z-index: 4; /* Relative to hero context */
}

/* Full-height vertical line at the center */
.hero__main-content-area::after {
    content: '';
    position: absolute;
    top: 0;
    bottom: 0;
    left: 50%;
    width: 1px;
    background: #fcf7fb; /* Updated to new specified color */
    transform: translateX(-50%);
    z-index: 4; /* Relative to hero context */
}

.hero__content-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    width: 100%;
    height: 100%;
    position: relative;
    z-index: 5; /* Relative to hero context */
}

.hero__content-left-column {
    padding-right: 1.5rem; /* Space before vertical line */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: flex-start;
    text-align: left;
}

.hero__content-right-column {
    padding-left: 1.5rem; /* Space after vertical line */
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
}

.hero__title {
    font-size: clamp(1.2rem, 3vw, 2rem);
    font-weight: 700;
    margin-bottom: clamp(1rem, 2vw, 1.5rem);
    line-height: 1.3;
    color: #ffffff;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero__subtitle {
    font-size: clamp(0.9rem, 2vw, 1.2rem);
    font-weight: 400;
    margin-bottom: clamp(1.2rem, 2.5vw, 1.8rem);
    line-height: 1.5;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.4);
}

.hero__services-list {
    font-size: clamp(0.9rem, 2vw, 1.2rem);
    font-weight: 400;
    line-height: 1.6;
    color: #ffffff;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.4);
    margin-bottom: clamp(0.8rem, 1.5vw, 1.2rem);
}

.hero__services-list strong {
    font-weight: 700;
}

.hero__service-box {
    background-color: rgba(133, 38, 127, 0.25);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    padding: clamp(1rem, 2vw, 1.5rem);
    display: flex;
    align-items: center;
    gap: clamp(1rem, 2vw, 1.5rem);
    min-height: 100px;
    transition: all 0.3s ease;
    text-decoration: none;
    color: inherit;
    margin-bottom: clamp(0.8rem, 1.5vw, 1.2rem);
}

.hero__service-box--clickable {
    cursor: pointer;
}

.hero__service-box--clickable:hover {
    background-color: rgba(133, 38, 127, 0.85); /* Slightly more opaque on hover */
    transform: translateY(-2px); /* Subtle lift effect */
    box-shadow: 0 4px 12px rgba(133, 38, 127, 0.3); /* Add shadow on hover */
}

.hero__service-box--clickable:active {
    transform: translateY(0); /* Reset on click */
}

/* Responsive service box adjustments */
@media (max-width: 1200px) {
    .hero__service-box {
        padding: 1.2rem;
        gap: 1.2rem;
        min-height: 90px;
        flex-direction: row; /* Maintain horizontal layout */
    }
}

.hero__service-box-text-content {
    flex-grow: 1;
}

.hero__service-name {
    font-size: clamp(1rem, 2.5vw, 1.5rem);
    font-weight: 500;
    color: #ffffff;
    margin: 0;
    line-height: 1.4;
    text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.4);
}

.hero__service-box-image-figure {
    margin: 0;
    flex-shrink: 0;
}

.hero__service-image {
    width: 150px;
    height: auto;
    max-height: 70px; /* To ensure image doesn't get too tall if service name is short */
    object-fit: cover;
    border-radius: 8px;
    background-color: #f0f0f0;
    transition: all 0.3s ease; /* Smooth transitions */
}

/* Responsive service image adjustments */
@media (max-width: 1200px) {
    .hero__service-image {
        width: 130px;
        max-height: 60px;
    }
}

.hero__controls {
    position: absolute;
    bottom: 2rem; /* Increased bottom spacing from section end */
    left: 75%; /* Center of right column (50% + 25% = 75%) */
    transform: translateX(-50%); /* Perfect centering under service box */
    z-index: 6; /* Relative to hero context */
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.75rem; /* Reduced gap for inline layout */
    width: auto;
    box-sizing: border-box;
    margin-top: 10px; /* Gap between service box and controls */
}

.hero__control-button {
    background-color: rgba(255, 255, 255, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    width: 44px; /* Slightly larger for desktop */
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 0;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.hero__control-button:hover {
    background-color: #ffffff;
    transform: scale(1.1);
}

.hero__control-button svg {
    width: 20px; /* Slightly larger for desktop */
    height: 20px;
    fill: #684278;
}

.hero__dots-navigation {
    display: flex;
    gap: 0.4rem; /* Reduced gap for inline layout */
}

.hero__dot {
    width: 12px; /* Slightly larger for desktop */
    height: 12px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    border: none;
    padding: 0;
    cursor: pointer;
    transition: background-color 0.3s ease, transform 0.2s ease;
}

.hero__dot:hover {
    transform: scale(1.2);
}

.hero__dot--active {
    background-color: #85267f; /* Updated to match service box color */
}

.hero--visually-hidden {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
}

/* Responsive Adjustments */
@media (max-width: 992px) { /* Tablet */
    .hero {
        height: 65vh; /* Adjusted responsive height for tablet */
    }
    .hero__overlay-container {
        justify-content: flex-end; /* Move content to bottom on smaller screens */
    }
    .hero__main-content-area {
        height: auto; /* Allow content to size naturally */
        padding: 1rem 1.5rem 1.5rem 1.5rem;
    }
    /* Hide the cross lines on tablet and mobile */
    .hero__main-content-area::before,
    .hero__main-content-area::after {
        display: none;
    }
    .hero__content-grid {
        grid-template-columns: 1fr; /* Stack columns */
        gap: 0; /* No gap needed when stacked, handled by column padding */
    }
    .hero__content-left-column {
        border-right: none; /* Remove vertical line */
        padding-right: 0;
        padding-bottom: 1.5rem; /* Space between stacked introduction and services */
    }
    .hero__content-right-column {
        padding-left: 0;
    }
    .hero__content-left, .hero__content-right {
        text-align: center;
    }
    .hero__service-box {
        flex-direction: column;
        text-align: center;
        padding: 1rem;
    }
    .hero__service-name {
        font-size: clamp(0.95rem, 2.2vw, 1.3rem);
    }
    .hero__service-box {
        flex-direction: row; /* Maintain horizontal layout on tablet */
        gap: 1rem;
        padding: 1rem;
        min-height: 80px;
    }
    .hero__service-box-text-content {
        flex: 1; /* Take available space */
    }
    .hero__service-image {
        width: 120px;
        max-height: 60px;
    }
    .hero__controls {
        position: absolute;
        bottom: 1.5rem; /* Increased bottom spacing for tablet */
        left: 50%; /* Center of full width since layout is now single column */
        transform: translateX(-50%); /* Perfect centering under full-width content */
        justify-content: center;
        width: auto;
        gap: 0.6rem; /* Slightly reduced gap for tablet */
    }
    .hero__control-button {
        width: 40px; /* Slightly smaller for tablet */
        height: 40px;
    }
    .hero__control-button svg {
        width: 18px;
        height: 18px;
    }
    .hero__dot {
        width: 10px;
        height: 10px;
    }
    .hero__title {
        margin-bottom: clamp(0.8rem, 1.8vw, 1.3rem);
        line-height: 1.35;
    }
    .hero__subtitle {
        margin-bottom: clamp(1rem, 2.2vw, 1.5rem);
        line-height: 1.45;
    }
    .hero__services-list {
        line-height: 1.5;
        margin-bottom: clamp(0.7rem, 1.3vw, 1rem);
    }
    .hero__service-name {
        line-height: 1.35;
    }
    .hero__service-box {
        padding: clamp(0.8rem, 1.8vw, 1.3rem);
        gap: clamp(0.8rem, 1.8vw, 1.3rem);
        margin-bottom: clamp(0.7rem, 1.3vw, 1rem);
    }
}

@media (max-width: 768px) { /* Mobile Landscape / Large Mobile */
    .hero {
        height: 70vh; /* Adjusted responsive height for mobile landscape */
    }
    .hero__main-content-area {
        padding: 0.5rem 1rem 1rem 1rem; /* Reduced top padding to create more space for controls */
    }
    .hero__title {
        margin-bottom: clamp(0.7rem, 1.6vw, 1.1rem);
        line-height: 1.4;
    }
    .hero__subtitle {
        margin-bottom: clamp(0.9rem, 2vw, 1.3rem);
        line-height: 1.5;
    }
    .hero__services-list {
        line-height: 1.45;
        margin-bottom: clamp(0.6rem, 1.2vw, 0.9rem);
    }
    .hero__service-name {
        line-height: 1.3;
    }
    .hero__service-box {
        padding: clamp(0.7rem, 1.6vw, 1.1rem);
        gap: clamp(0.7rem, 1.6vw, 1.1rem);
        margin-bottom: clamp(0.6rem, 1.2vw, 0.9rem);
    }
    .hero__service-image {
        width: 100px;
        max-height: 50px;
    }
    .hero__controls {
        position: absolute;
        bottom: 1.25rem; /* Increased bottom spacing for mobile landscape */
        left: 50%; /* Center of full width since layout is single column */
        transform: translateX(-50%); /* Perfect centering under full-width content */
        gap: 0.5rem; /* Smaller gap for mobile */
    }
    .hero__control-button {
        width: 36px;
        height: 36px;
    }
    .hero__control-button svg {
        width: 16px;
        height: 16px;
    }
    .hero__dot {
        width: 8px;
        height: 8px;
    }
}

@media (max-width: 480px) { /* Mobile Portrait */
    .hero {
        height: 60vh; /* Adjusted responsive height for mobile portrait */
    }
    .hero__main-content-area {
        padding: 0.25rem 1rem 1rem 1rem; /* Further reduced top padding for mobile portrait */
    }
    .hero__content-left-column {
        padding-bottom: 1rem;
    }
    .hero__service-box {
        gap: 0.5rem;
    }
    .hero__controls {
        position: absolute;
        bottom: 1rem; /* Maintain 1rem for mobile portrait to maximize space */
        left: 50%; /* Center of full width since layout is single column */
        transform: translateX(-50%); /* Perfect centering under full-width content */
        gap: 0.4rem; /* Even smaller gap for mobile portrait */
    }
    .hero__control-button {
        width: 32px; /* Smaller for mobile portrait */
        height: 32px;
    }
    .hero__control-button svg {
        width: 14px;
        height: 14px;
    }
    .hero__dot {
        width: 7px;
        height: 7px;
    }
    .hero__service-box {
        flex-direction: row; /* Maintain horizontal layout even on mobile portrait */
        gap: 0.6rem;
        padding: 0.6rem;
        min-height: 60px;
    }
    .hero__service-box-text-content {
        flex: 1; /* Take available space */
    }
    .hero__service-image {
        width: 80px;
        max-height: 40px;
    }
    .hero__title {
        margin-bottom: clamp(0.6rem, 1.4vw, 1rem);
        line-height: 1.45;
    }
    .hero__subtitle {
        margin-bottom: clamp(0.8rem, 1.8vw, 1.2rem);
        line-height: 1.5;
    }
    .hero__services-list {
        line-height: 1.4;
        margin-bottom: clamp(0.5rem, 1.1vw, 0.8rem);
    }
    .hero__service-name {
        line-height: 1.35;
    }
    .hero__service-box {
        padding: clamp(0.6rem, 1.4vw, 1rem);
        gap: clamp(0.6rem, 1.4vw, 1rem);
        margin-bottom: clamp(0.5rem, 1.1vw, 0.8rem);
    }
}
