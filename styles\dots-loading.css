/* Dots Loading Animation
   Based on the dot-pulse animation from three-dots library
   https://github.com/nzbin/three-dots
*/

:root {
  --dots-color: #84277F; /* Primary color */
  --dots-size: 10px;
  --dots-spacing: 24px;
}

/* Loading Container Styles */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 0;
}

/* Dots Loading Animation */
.dots-loading {
  position: relative;
  left: -9999px;
  width: var(--dots-size);
  height: var(--dots-size);
  border-radius: calc(var(--dots-size) / 2);
  background-color: var(--dots-color);
  color: var(--dots-color);
  box-shadow: 9999px 0 0 -5px;
  animation: dots-loading 1.5s infinite linear;
  animation-delay: 0.25s;
  /* Removed margin-bottom since we no longer have text below */
}

.dots-loading::before,
.dots-loading::after {
  content: "";
  display: inline-block;
  position: absolute;
  top: 0;
  width: var(--dots-size);
  height: var(--dots-size);
  border-radius: calc(var(--dots-size) / 2);
  background-color: var(--dots-color);
  color: var(--dots-color);
}

.dots-loading::before {
  box-shadow: 9984px 0 0 -5px;
  animation: dots-loading-before 1.5s infinite linear;
  animation-delay: 0s;
}

.dots-loading::after {
  box-shadow: 10014px 0 0 -5px;
  animation: dots-loading-after 1.5s infinite linear;
  animation-delay: 0.5s;
}

@keyframes dots-loading-before {
  0% {
    box-shadow: 9984px 0 0 -5px;
  }
  30% {
    box-shadow: 9984px 0 0 2px;
  }
  60%, 100% {
    box-shadow: 9984px 0 0 -5px;
  }
}

@keyframes dots-loading {
  0% {
    box-shadow: 9999px 0 0 -5px;
  }
  30% {
    box-shadow: 9999px 0 0 2px;
  }
  60%, 100% {
    box-shadow: 9999px 0 0 -5px;
  }
}

@keyframes dots-loading-after {
  0% {
    box-shadow: 10014px 0 0 -5px;
  }
  30% {
    box-shadow: 10014px 0 0 2px;
  }
  60%, 100% {
    box-shadow: 10014px 0 0 -5px;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  :root {
    --dots-size: 8px;
    --dots-spacing: 20px;
  }
}
