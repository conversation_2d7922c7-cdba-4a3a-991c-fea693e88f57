import { gql } from '@apollo/client';

// Worker Mutations
export const CREATE_WORKER = gql`
  mutation CreateWorker(
    $name: String!
    $company: String!
    $nationalId: String!
    $gender: String!
    $phoneNumber: String!
    $dateOfBirth: Date
    $trainingIds: [Int!]
    $tradeIds: [Int!]
    $skillIds: [Int!]
    $mpesaNumber: String
    $email: String
    $inductionDate: DateTime
    $medicalCheckDate: DateTime
  ) {
    createWorker(
      name: $name
      company: $company
      nationalId: $nationalId
      gender: $gender
      phoneNumber: $phoneNumber
      dateOfBirth: $dateOfBirth
      trainingIds: $trainingIds
      tradeIds: $tradeIds
      skillIds: $skillIds
      mpesaNumber: $mpesaNumber
      email: $email
      inductionDate: $inductionDate
      medicalCheckDate: $medicalCheckDate
    ) {
      id
      name
      company
      nationalId
      phoneNumber
      email
      dateOfBirth
      gender
      mpesaNumber
      inductionDate
      medicalCheckDate
      photoUrl
      age
      trainingsCompleted
      manHours
      rating
      trades {
        id
        name
      }
      skills {
        id
        name
      }
      trainings {
        id
        name
      }
      createdAt
      createdBy
      updatedAt
      updatedBy
    }
  }
`;

export const UPDATE_WORKER = gql`
  mutation UpdateWorker(
    $id: Int!
    $name: String
    $company: String
    $dateOfBirth: Date
    $trainingIds: [Int!]
    $tradeIds: [Int!]
    $skillIds: [Int!]
    $manHours: Int
    $rating: Float
    $gender: String
    $phoneNumber: String
    $mpesaNumber: String
    $email: String
    $inductionDate: DateTime
    $medicalCheckDate: DateTime
  ) {
    updateWorker(
      id: $id
      name: $name
      company: $company
      dateOfBirth: $dateOfBirth
      trainingIds: $trainingIds
      tradeIds: $tradeIds
      skillIds: $skillIds
      manHours: $manHours
      rating: $rating
      gender: $gender
      phoneNumber: $phoneNumber
      mpesaNumber: $mpesaNumber
      email: $email
      inductionDate: $inductionDate
      medicalCheckDate: $medicalCheckDate
    ) {
      id
      name
      company
      nationalId
      phoneNumber
      email
      dateOfBirth
      gender
      mpesaNumber
      inductionDate
      medicalCheckDate
      photoUrl
      age
      trainingsCompleted
      manHours
      rating
      trades {
        id
        name
      }
      skills {
        id
        name
      }
      trainings {
        id
        name
      }
      updatedAt
      updatedBy
    }
  }
`;

export const DELETE_WORKER = gql`
  mutation DeleteWorker($id: Int!) {
    deleteWorker(id: $id)
  }
`;

export const UPLOAD_WORKER_PHOTO = gql`
  mutation UploadWorkerPhoto($workerId: Int!, $photo: Upload!) {
    uploadWorkerPhoto(workerId: $workerId, photo: $photo) {
      photoUrl
      hikvisionRegistered
      message
    }
  }
`;

export const DELETE_WORKER_PHOTO = gql`
  mutation DeleteWorkerPhoto($workerId: Int!) {
    deleteWorkerPhoto(workerId: $workerId) {
      success
      message
    }
  }
`;

// Training Mutations
export const CREATE_TRAINING = gql`
  mutation CreateTraining($input: CreateTrainingInput!) {
    createTraining(input: $input) {
      id
      name
      description
      startDate
      endDate
      duration
      validityPeriodMonths
      trainingType
      trainer
      frequency
      status
      createdAt
      createdBy
    }
  }
`;

export const UPDATE_TRAINING = gql`
  mutation UpdateTraining($id: Int!, $input: UpdateTrainingInput!) {
    updateTraining(id: $id, input: $input) {
      id
      name
      description
      startDate
      endDate
      duration
      validityPeriodMonths
      trainingType
      trainer
      frequency
      status
      updatedAt
      updatedBy
    }
  }
`;

export const RECORD_TRAINING_COMPLETION = gql`
  mutation RecordTrainingCompletion($input: TrainingCompletionInput!) {
    recordTrainingCompletion(input: $input) {
      id
      workerId
      trainingId
      completionDate
      expiryDate
      score
      notes
      status
      createdAt
      createdBy
    }
  }
`;

export const BULK_ASSIGN_TRAINING = gql`
  mutation BulkAssignTraining($trainingId: Int!, $workerIds: [Int!]!, $scheduledDate: String) {
    bulkAssignTraining(trainingId: $trainingId, workerIds: $workerIds, scheduledDate: $scheduledDate) {
      success
      assignedCount
      message
    }
  }
`;

export const RENEW_TRAINING = gql`
  mutation RenewTraining($workerId: Int!, $trainingHistoryId: Int!, $input: TrainingRenewalInput!) {
    renewTraining(workerId: $workerId, trainingHistoryId: $trainingHistoryId, input: $input) {
      id
      workerId
      trainingId
      completionDate
      expiryDate
      score
      notes
      status
      createdAt
      createdBy
    }
  }
`;

// Toolbox Session Mutations
export const CREATE_TOOLBOX_SESSION = gql`
  mutation CreateToolboxSession($input: CreateToolboxSessionInput!) {
    createToolboxSession(input: $input) {
      id
      sessionTime
      topic
      conductor
      photoUrl
      notes
      attendances {
        id
        workerId
        wasPresent
        notes
      }
      createdAt
      createdBy
    }
  }
`;

export const UPDATE_TOOLBOX_SESSION = gql`
  mutation UpdateToolboxSession($id: Int!, $input: UpdateToolboxSessionInput!) {
    updateToolboxSession(id: $id, input: $input) {
      id
      sessionTime
      topic
      conductor
      photoUrl
      notes
      attendances {
        id
        workerId
        wasPresent
        notes
      }
      updatedAt
      updatedBy
    }
  }
`;

export const DELETE_TOOLBOX_SESSION = gql`
  mutation DeleteToolboxSession($id: Int!) {
    deleteToolboxSession(id: $id) {
      success
      message
    }
  }
`;

export const UPDATE_TOOLBOX_ATTENDANCE = gql`
  mutation UpdateToolboxAttendance($sessionId: Int!, $attendances: [ToolboxAttendanceInput!]!) {
    updateToolboxAttendance(sessionId: $sessionId, attendances: $attendances) {
      success
      updatedCount
      message
    }
  }
`;

// Worker Attendance Mutations
export const RECORD_WORKER_ATTENDANCE = gql`
  mutation RecordWorkerAttendance($input: WorkerAttendanceInput!) {
    recordWorkerAttendance(input: $input) {
      id
      workerId
      checkInTime
      checkOutTime
      status
      notes
      isVerifiedByHikvision
      createdAt
      createdBy
    }
  }
`;

export const UPDATE_WORKER_ATTENDANCE = gql`
  mutation UpdateWorkerAttendance($id: Int!, $input: UpdateWorkerAttendanceInput!) {
    updateWorkerAttendance(id: $id, input: $input) {
      id
      workerId
      checkInTime
      checkOutTime
      status
      notes
      isVerifiedByHikvision
      updatedAt
      updatedBy
    }
  }
`;

// Trade and Skill Mutations
export const CREATE_TRADE = gql`
  mutation CreateTrade($input: CreateTradeInput!) {
    createTrade(input: $input) {
      id
      name
      description
      createdAt
      createdBy
    }
  }
`;

export const UPDATE_TRADE = gql`
  mutation UpdateTrade($id: Int!, $input: UpdateTradeInput!) {
    updateTrade(id: $id, input: $input) {
      id
      name
      description
      updatedAt
      updatedBy
    }
  }
`;

export const CREATE_SKILL = gql`
  mutation CreateSkill($input: CreateSkillInput!) {
    createSkill(input: $input) {
      id
      name
      description
      createdAt
      createdBy
    }
  }
`;

export const UPDATE_SKILL = gql`
  mutation UpdateSkill($id: Int!, $input: UpdateSkillInput!) {
    updateSkill(id: $id, input: $input) {
      id
      name
      description
      updatedAt
      updatedBy
    }
  }
`;

// Hikvision Integration Mutations
export const SYNC_HIKVISION_ATTENDANCE = gql`
  mutation SyncHikvisionAttendance($siteId: String!, $date: String!) {
    syncHikvisionAttendance(siteId: $siteId, date: $date) {
      success
      syncedCount
      message
      attendanceRecords {
        workerId
        checkInTime
        checkOutTime
        isVerified
      }
    }
  }
`;

export const REGISTER_WORKER_FACE = gql`
  mutation RegisterWorkerFace($workerId: Int!, $photo: Upload!) {
    registerWorkerFace(workerId: $workerId, photo: $photo) {
      success
      hikvisionPersonId
      message
    }
  }
`;

export const UNREGISTER_WORKER_FACE = gql`
  mutation UnregisterWorkerFace($workerId: Int!) {
    unregisterWorkerFace(workerId: $workerId) {
      success
      message
    }
  }
`;
