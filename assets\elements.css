 .u-section-1 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-1 .u-sheet-1 {
  min-height: 1003px;
}

.u-section-1 .u-text-1 {
  letter-spacing: 2px;
  font-weight: 400;
  margin: 20px 807px 0 0;
}

.u-section-1 .u-list-1 {
  margin-bottom: 20px;
  margin-top: 20px;
  width: 100%;
}

.u-section-1 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 900px;
  grid-gap: 10px;
}

.u-section-1 .u-container-layout-1 {
  padding: 0;
}

.u-section-1 .u-group-1 {
  --radius: 20px;
  min-height: 459px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
}

.u-section-1 .u-container-layout-2 {
  padding: 30px;
}

.u-section-1 .u-text-2 {
  font-weight: 700;
  margin: 50px auto 0;
}

.u-section-1 .u-icon-1 {
  width: 64px;
  height: 64px;
  margin: 30px auto 0;
}

.u-section-1 .u-text-3 {
  font-weight: 700;
  margin: 30px auto 0;
}

.u-section-1 .u-icon-2 {
  width: 40px;
  height: 40px;
  transition-duration: 0.5s;
  margin: 81px 0 0 auto;
  padding: 0;
}

.u-section-1 .u-container-layout-3 {
  padding: 30px;
}

.u-section-1 .u-text-4 {
  margin: 0;
}

.u-section-1 .u-container-layout-4 {
  padding: 0;
}

.u-section-1 .u-group-2 {
  --radius: 20px;
  min-height: 459px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
}

.u-section-1 .u-container-layout-5 {
  padding: 30px;
}

.u-section-1 .u-text-5 {
  font-weight: 700;
  margin: 50px auto 0;
}

.u-section-1 .u-icon-3 {
  width: 64px;
  height: 64px;
  margin: 30px auto 0;
}

.u-section-1 .u-text-6 {
  font-weight: 700;
  margin: 30px auto 0;
}

.u-section-1 .u-icon-4 {
  width: 40px;
  height: 40px;
  transition-duration: 0.5s;
  margin: 81px 0 0 auto;
  padding: 0;
}

.u-section-1 .u-container-layout-6 {
  padding: 30px;
}

.u-section-1 .u-text-7 {
  margin: 0;
}

.u-section-1 .u-container-layout-7 {
  padding: 0;
}

.u-section-1 .u-group-3 {
  --radius: 20px;
  min-height: 459px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
}

.u-section-1 .u-container-layout-8 {
  padding: 30px;
}

.u-section-1 .u-text-8 {
  font-weight: 700;
  margin: 50px auto 0;
}

.u-section-1 .u-icon-5 {
  width: 64px;
  height: 64px;
  margin: 30px auto 0;
}

.u-section-1 .u-text-9 {
  font-weight: 700;
  margin: 30px auto 0;
}

.u-section-1 .u-icon-6 {
  width: 40px;
  height: 40px;
  transition-duration: 0.5s;
  margin: 81px 0 0 auto;
  padding: 0;
}

.u-section-1 .u-container-layout-9 {
  padding: 30px;
}

.u-section-1 .u-text-10 {
  margin: 0;
}

.u-section-1 .u-container-layout-10 {
  padding: 0;
}

.u-section-1 .u-group-4 {
  --radius: 20px;
  min-height: 459px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
}

.u-section-1 .u-container-layout-11 {
  padding: 30px;
}

.u-section-1 .u-text-11 {
  font-weight: 700;
  margin: 50px auto 0;
}

.u-section-1 .u-icon-7 {
  width: 64px;
  height: 64px;
  margin: 30px auto 0;
}

.u-section-1 .u-text-12 {
  font-weight: 700;
  margin: 30px auto 0;
}

.u-section-1 .u-icon-8 {
  width: 40px;
  height: 40px;
  transition-duration: 0.5s;
  margin: 81px 0 0 auto;
  padding: 0;
}

.u-section-1 .u-container-layout-12 {
  padding: 30px;
}

.u-section-1 .u-text-13 {
  margin: 0;
}

.u-section-1 .u-container-layout-13 {
  padding: 0;
}

.u-section-1 .u-group-5 {
  --radius: 20px;
  min-height: 459px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
}

.u-section-1 .u-container-layout-14 {
  padding: 30px;
}

.u-section-1 .u-text-14 {
  font-weight: 700;
  margin: 50px auto 0;
}

.u-section-1 .u-icon-9 {
  width: 64px;
  height: 64px;
  margin: 30px auto 0;
}

.u-section-1 .u-text-15 {
  font-weight: 700;
  margin: 30px auto 0;
}

.u-section-1 .u-icon-10 {
  width: 40px;
  height: 40px;
  transition-duration: 0.5s;
  margin: 81px 0 0 auto;
  padding: 0;
}

.u-section-1 .u-container-layout-15 {
  padding: 30px;
}

.u-section-1 .u-text-16 {
  margin: 0;
}

@media (max-width: 1199px) {
  .u-section-1 .u-text-1 {
    margin-right: 607px;
  }

  .u-section-1 .u-repeater-1 {
    min-height: 1100px;
  }

  .u-section-1 .u-group-1 {
    min-height: 545px;
    height: auto;
  }

  .u-section-1 .u-icon-2 {
    margin-top: 183px;
  }

  .u-section-1 .u-group-2 {
    min-height: 545px;
    height: auto;
  }

  .u-section-1 .u-icon-4 {
    margin-top: 183px;
  }

  .u-section-1 .u-group-3 {
    min-height: 545px;
    height: auto;
  }

  .u-section-1 .u-icon-6 {
    margin-top: 183px;
  }

  .u-section-1 .u-text-10 {
    margin-bottom: -7px;
  }

  .u-section-1 .u-group-4 {
    min-height: 545px;
    height: auto;
  }

  .u-section-1 .u-icon-8 {
    margin-top: 183px;
  }

  .u-section-1 .u-group-5 {
    min-height: 545px;
    height: auto;
  }

  .u-section-1 .u-icon-10 {
    margin-top: 183px;
  }
}

@media (max-width: 991px) {
  .u-section-1 .u-sheet-1 {
    min-height: 1485px;
  }

  .u-section-1 .u-text-1 {
    width: auto;
    margin-right: 0;
  }

  .u-section-1 .u-list-1 {
    margin-bottom: 5px;
  }

  .u-section-1 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5px);
    grid-template-columns: repeat(2, calc(50% - 5px));
    min-height: 1380px;
  }

  .u-section-1 .u-group-1 {
    min-height: 454px;
  }

  .u-section-1 .u-text-2 {
    margin-top: 79px;
  }

  .u-section-1 .u-icon-2 {
    margin-top: 88px;
    margin-right: 20px;
  }

  .u-section-1 .u-group-2 {
    min-height: 454px;
  }

  .u-section-1 .u-text-5 {
    margin-top: 79px;
  }

  .u-section-1 .u-icon-4 {
    margin-top: 88px;
    margin-right: 20px;
  }

  .u-section-1 .u-group-3 {
    min-height: 454px;
  }

  .u-section-1 .u-text-8 {
    margin-top: 79px;
  }

  .u-section-1 .u-icon-6 {
    margin-top: 88px;
    margin-right: 20px;
  }

  .u-section-1 .u-group-4 {
    min-height: 454px;
  }

  .u-section-1 .u-text-11 {
    margin-top: 79px;
  }

  .u-section-1 .u-icon-8 {
    margin-top: 88px;
    margin-right: 20px;
  }

  .u-section-1 .u-group-5 {
    min-height: 454px;
  }

  .u-section-1 .u-text-14 {
    margin-top: 79px;
  }

  .u-section-1 .u-icon-10 {
    margin-top: 88px;
    margin-right: 20px;
  }
}

@media (max-width: 767px) {
  .u-section-1 .u-sheet-1 {
    min-height: 1500px;
  }

  .u-section-1 .u-list-1 {
    margin-bottom: 20px;
  }

  .u-section-1 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-1 .u-group-1 {
    min-height: 347px;
  }

  .u-section-1 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-text-2 {
    margin-top: 40px;
  }

  .u-section-1 .u-icon-2 {
    margin-top: 24px;
  }

  .u-section-1 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-group-2 {
    min-height: 347px;
  }

  .u-section-1 .u-container-layout-5 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-text-5 {
    margin-top: 40px;
  }

  .u-section-1 .u-icon-4 {
    margin-top: 24px;
  }

  .u-section-1 .u-container-layout-6 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-group-3 {
    min-height: 347px;
  }

  .u-section-1 .u-container-layout-8 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-text-8 {
    margin-top: 40px;
  }

  .u-section-1 .u-icon-6 {
    margin-top: 24px;
  }

  .u-section-1 .u-container-layout-9 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-group-4 {
    min-height: 347px;
  }

  .u-section-1 .u-container-layout-11 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-text-11 {
    margin-top: 40px;
  }

  .u-section-1 .u-icon-8 {
    margin-top: 24px;
  }

  .u-section-1 .u-container-layout-12 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-group-5 {
    min-height: 347px;
  }

  .u-section-1 .u-container-layout-14 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-text-14 {
    margin-top: 40px;
  }

  .u-section-1 .u-icon-10 {
    margin-top: 24px;
  }

  .u-section-1 .u-container-layout-15 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-1 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}

.u-section-1 .u-icon-2,
.u-section-1 .u-icon-2:before,
.u-section-1 .u-icon-2 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-1 .u-icon-2.u-icon-2.u-icon-2:hover {
  transform: translateX(10px) translateY(0px) !important;
}

.u-section-1 .u-icon-2.u-icon-2.u-icon-2.hover {
  transform: translateX(10px) translateY(0px) !important;
}

.u-section-1 .u-icon-4,
.u-section-1 .u-icon-4:before,
.u-section-1 .u-icon-4 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-1 .u-icon-4.u-icon-4.u-icon-4:hover {
  transform: translateX(10px) translateY(0px) !important;
}

.u-section-1 .u-icon-4.u-icon-4.u-icon-4.hover {
  transform: translateX(10px) translateY(0px) !important;
}

.u-section-1 .u-icon-6,
.u-section-1 .u-icon-6:before,
.u-section-1 .u-icon-6 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-1 .u-icon-6.u-icon-6.u-icon-6:hover {
  transform: translateX(10px) translateY(0px) !important;
}

.u-section-1 .u-icon-6.u-icon-6.u-icon-6.hover {
  transform: translateX(10px) translateY(0px) !important;
}

.u-section-1 .u-icon-8,
.u-section-1 .u-icon-8:before,
.u-section-1 .u-icon-8 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-1 .u-icon-8.u-icon-8.u-icon-8:hover {
  transform: translateX(10px) translateY(0px) !important;
}

.u-section-1 .u-icon-8.u-icon-8.u-icon-8.hover {
  transform: translateX(10px) translateY(0px) !important;
}

.u-section-1 .u-icon-10,
.u-section-1 .u-icon-10:before,
.u-section-1 .u-icon-10 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-1 .u-icon-10.u-icon-10.u-icon-10:hover {
  transform: translateX(10px) translateY(0px) !important;
}

.u-section-1 .u-icon-10.u-icon-10.u-icon-10.hover {
  transform: translateX(10px) translateY(0px) !important;
} .u-section-2 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-2 .u-sheet-1 {
  min-height: 717px;
}

.u-section-2 .u-text-1 {
  font-weight: 300;
  margin: 20px 967px 0 30px;
}

.u-section-2 .u-text-2 {
  font-weight: 400;
  font-size: 2.25rem;
  margin: 20px auto 0 30px;
}

.u-section-2 .u-btn-1 {
  margin: -23px 0 0 auto;
  padding: 0;
}

.u-section-2 .u-text-3 {
  margin: 20px auto 0 30px;
}

.u-section-2 .u-slider-1 {
  min-height: 531px;
  margin-top: 20px;
  margin-bottom: -311px;
  height: auto;
}

.u-section-2 .u-carousel-indicators-1 {
  position: absolute;
  bottom: 10px;
  width: auto;
  height: auto;
}

.u-section-2 .u-container-layout-1 {
  padding: 20px 40px;
}

.u-section-2 .u-list-1 {
  width: 1107px;
  margin: 0 auto;
}

.u-section-2 .u-repeater-1 {
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 491px;
  grid-auto-columns: calc(50% - 5px);
  grid-gap: 10px;
}

.u-section-2 .u-container-layout-2 {
  padding: 8px 10px;
}

.u-section-2 .u-image-1 {
  height: 352px;
  --radius: 5px;
  margin-top: 2px;
  margin-bottom: 0;
}

.u-section-2 .u-text-4 {
  font-weight: 400;
  margin: 20px 40px 0 0;
}

.u-section-2 .u-text-5 {
  margin: 21px auto 0 0;
}

.u-section-2 .u-container-layout-3 {
  padding: 8px 10px;
}

.u-section-2 .u-image-2 {
  height: 352px;
  --radius: 5px;
  margin-top: 2px;
  margin-bottom: 0;
}

.u-section-2 .u-text-6 {
  font-weight: 400;
  margin: 20px 40px 0 0;
}

.u-section-2 .u-text-7 {
  margin: 21px auto 0 0;
}

.u-section-2 .u-container-layout-4 {
  padding: 20px 40px;
}

.u-section-2 .u-list-2 {
  width: 1107px;
  margin: 0 auto;
}

.u-section-2 .u-repeater-2 {
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 491px;
  grid-auto-columns: calc(50% - 5px);
  grid-gap: 10px;
}

.u-section-2 .u-container-layout-5 {
  padding: 8px 10px;
}

.u-section-2 .u-image-3 {
  height: 352px;
  --radius: 5px;
  margin-top: 2px;
  margin-bottom: 0;
}

.u-section-2 .u-text-8 {
  font-weight: 400;
  margin: 20px 40px 0 0;
}

.u-section-2 .u-text-9 {
  margin: 21px auto 0 0;
}

.u-section-2 .u-container-layout-6 {
  padding: 8px 10px;
}

.u-section-2 .u-image-4 {
  height: 352px;
  --radius: 5px;
  margin-top: 2px;
  margin-bottom: 0;
}

.u-section-2 .u-text-10 {
  font-weight: 400;
  margin: 20px 40px 0 0;
}

.u-section-2 .u-text-11 {
  margin: 21px auto 0 0;
}

.u-section-2 .u-carousel-control-1 {
  width: 50px;
  height: 50px;
}

.u-section-2 .u-carousel-control-2 {
  width: 50px;
  height: 50px;
}

@media (max-width: 1199px) {
  .u-section-2 .u-sheet-1 {
    min-height: 636px;
  }

  .u-section-2 .u-text-1 {
    margin-right: 797px;
    margin-left: 0;
  }

  .u-section-2 .u-text-2 {
    margin-left: 0;
  }

  .u-section-2 .u-text-3 {
    margin-left: 0;
  }

  .u-section-2 .u-slider-1 {
    min-height: 449px;
    margin-bottom: -107px;
  }

  .u-section-2 .u-list-1 {
    width: 920px;
  }

  .u-section-2 .u-repeater-1 {
    min-height: 407px;
  }

  .u-section-2 .u-image-1 {
    height: 290px;
    transition-duration: 0.5s;
    transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  }

  .u-section-2 .u-text-4 {
    margin-right: 0;
  }

  .u-section-2 .u-image-2 {
    height: 290px;
    transition-duration: 0.5s;
    transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  }

  .u-section-2 .u-text-6 {
    margin-right: 0;
  }

  .u-section-2 .u-list-2 {
    width: 920px;
  }

  .u-section-2 .u-repeater-2 {
    min-height: 407px;
  }

  .u-section-2 .u-image-3 {
    height: 290px;
    transition-duration: 0.5s;
    transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  }

  .u-section-2 .u-text-8 {
    margin-right: 0;
  }

  .u-section-2 .u-image-4 {
    height: 290px;
    transition-duration: 0.5s;
    transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  }

  .u-section-2 .u-text-10 {
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-2 .u-text-1 {
    margin-right: 577px;
  }

  .u-section-2 .u-slider-1 {
    margin-top: 5px;
    margin-bottom: 5px;
  }

  .u-section-2 .u-container-layout-1 {
    padding-left: 30px;
    padding-right: 30px;
  }

  .u-section-2 .u-list-1 {
    width: 720px;
  }

  .u-section-2 .u-repeater-1 {
    grid-template-columns: 100%;
    grid-auto-columns: calc(100% - 0px);
  }

  .u-section-2 .u-image-1 {
    height: 461px;
  }

  .u-section-2 .u-image-2 {
    height: 461px;
  }

  .u-section-2 .u-container-layout-4 {
    padding-left: 30px;
    padding-right: 30px;
  }

  .u-section-2 .u-list-2 {
    width: 720px;
  }

  .u-section-2 .u-repeater-2 {
    grid-template-columns: 100%;
    grid-auto-columns: calc(100% - 0px);
  }

  .u-section-2 .u-image-3 {
    height: 461px;
  }

  .u-section-2 .u-image-4 {
    height: 461px;
  }
}

@media (max-width: 767px) {
  .u-section-2 .u-sheet-1 {
    min-height: 999px;
  }

  .u-section-2 .u-text-1 {
    margin-right: 337px;
  }

  .u-section-2 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-2 .u-list-1 {
    width: 520px;
  }

  .u-section-2 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-2 .u-image-1 {
    height: 329px;
  }

  .u-section-2 .u-image-2 {
    height: 329px;
  }

  .u-section-2 .u-container-layout-4 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-2 .u-list-2 {
    width: 520px;
  }

  .u-section-2 .u-repeater-2 {
    grid-auto-columns: 100%;
  }

  .u-section-2 .u-image-3 {
    height: 329px;
  }

  .u-section-2 .u-image-4 {
    height: 329px;
  }
}

@media (max-width: 575px) {
  .u-section-2 .u-sheet-1 {
    min-height: 926px;
  }

  .u-section-2 .u-text-1 {
    margin-right: 137px;
  }

  .u-section-2 .u-slider-1 {
    margin-bottom: -691px;
  }

  .u-section-2 .u-list-1 {
    width: 320px;
  }

  .u-section-2 .u-image-1 {
    height: 197px;
  }

  .u-section-2 .u-image-2 {
    height: 197px;
  }

  .u-section-2 .u-list-2 {
    width: 320px;
  }

  .u-section-2 .u-image-3 {
    height: 197px;
  }

  .u-section-2 .u-image-4 {
    height: 197px;
  }
}

.u-block-ac2f-21:not([data-block-selected]):not([data-cell-selected]),
.u-block-ac2f-21:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-ac2f-21:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-ac2f-21:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-21:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-21:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-ac2f-21:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-21:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-21:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-ac2f-25:not([data-block-selected]):not([data-cell-selected]),
.u-block-ac2f-25:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-ac2f-25:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-ac2f-25:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-25:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-25:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-ac2f-25:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-25:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-25:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-ac2f-43:not([data-block-selected]):not([data-cell-selected]),
.u-block-ac2f-43:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-ac2f-43:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-ac2f-43:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-43:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-43:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-ac2f-43:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-43:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-43:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-ac2f-96:not([data-block-selected]):not([data-cell-selected]),
.u-block-ac2f-96:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-ac2f-96:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-ac2f-96:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-96:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-96:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-ac2f-96:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-96:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-96:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-ac2f-102:not([data-block-selected]):not([data-cell-selected]),
.u-block-ac2f-102:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-ac2f-102:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-ac2f-102:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-102:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-102:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-ac2f-102:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-102:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-102:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-ac2f-108:not([data-block-selected]):not([data-cell-selected]),
.u-block-ac2f-108:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-ac2f-108:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-ac2f-108:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-108:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-108:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-ac2f-108:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-108:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-108:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-ac2f-114:not([data-block-selected]):not([data-cell-selected]),
.u-block-ac2f-114:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-ac2f-114:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-ac2f-114:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-114:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-114:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-ac2f-114:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-114:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-114:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-ac2f-133:not([data-block-selected]):not([data-cell-selected]),
.u-block-ac2f-133:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-ac2f-133:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-ac2f-133:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-133:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-133:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-ac2f-133:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-133:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-133:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-ac2f-138:not([data-block-selected]):not([data-cell-selected]),
.u-block-ac2f-138:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-ac2f-138:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-ac2f-138:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-138:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-138:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-ac2f-138:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-138:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-138:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-ac2f-143:not([data-block-selected]):not([data-cell-selected]),
.u-block-ac2f-143:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-ac2f-143:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-ac2f-143:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-143:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-143:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-ac2f-143:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-143:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-143:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-ac2f-148:not([data-block-selected]):not([data-cell-selected]),
.u-block-ac2f-148:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-ac2f-148:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-ac2f-148:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-148:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-148:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-ac2f-148:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-148:not([data-block-selected]):not([data-cell-selected]).u-block-ac2f-148:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-section-2 .u-image-1:not([data-block-selected]):not([data-cell-selected]),
.u-section-2 .u-image-1:not([data-block-selected]):not([data-cell-selected]):before,
.u-section-2 .u-image-1:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-container-layout:hover > .u-section-2 .u-image-1:not([data-block-selected]):not([data-cell-selected]) {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-container-layout.hover > .u-section-2 .u-image-1:not([data-block-selected]):not([data-cell-selected]) {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-2 .u-image-2:not([data-block-selected]):not([data-cell-selected]),
.u-section-2 .u-image-2:not([data-block-selected]):not([data-cell-selected]):before,
.u-section-2 .u-image-2:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-container-layout:hover > .u-section-2 .u-image-2:not([data-block-selected]):not([data-cell-selected]) {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-container-layout.hover > .u-section-2 .u-image-2:not([data-block-selected]):not([data-cell-selected]) {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
} .u-section-3 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-3 .u-sheet-1 {
  min-height: 837px;
}

.u-section-3 .u-shape-1 {
  --radius: 20px;
  height: 767px;
  margin-top: 50px;
  margin-bottom: 0;
}

.u-section-3 .u-group-1 {
  --radius: 30px;
  width: 489px;
  min-height: 60px;
  height: auto;
  margin: -797px auto 0;
}

.u-section-3 .u-container-layout-1 {
  padding-left: 30px;
  padding-right: 30px;
}

.u-section-3 .u-text-1 {
  font-weight: 600;
  font-size: 1.5rem;
  margin: 13px 37px 0;
}

.u-section-3 .u-list-1 {
  width: 1150px;
  margin: 30px auto 49px;
}

.u-section-3 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 678px;
  grid-gap: 10px;
}

.u-section-3 .u-list-item-1 {
  --radius: 20px;
  transition-duration: 0.5s;
}

.u-section-3 .u-container-layout-2 {
  padding: 10px 10px 0;
}

.u-section-3 .u-text-2 {
  font-weight: 400;
  margin: 0 auto 0 0;
}

.u-section-3 .u-icon-2 {
  font-size: 2.5em;
}

.u-section-3 .u-text-3 {
  margin-left: 0;
  margin-right: auto;
  margin-bottom: 0;
}

.u-section-3 .u-btn-1 {
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-3 .u-list-item-2 {
  --radius: 20px;
  transition-duration: 0.5s;
}

.u-section-3 .u-container-layout-3 {
  padding: 10px 10px 30px;
}

.u-section-3 .u-text-4 {
  font-weight: 400;
  margin: 0 auto 0 0;
}

.u-section-3 .u-icon-4 {
  font-size: 2.5em;
}

.u-section-3 .u-text-5 {
  margin-left: 0;
  margin-right: auto;
  margin-bottom: 0;
}

.u-section-3 .u-btn-2 {
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-3 .u-list-item-3 {
  --radius: 20px;
  transition-duration: 0.5s;
}

.u-section-3 .u-container-layout-4 {
  padding: 10px 10px 0;
}

.u-section-3 .u-text-6 {
  font-weight: 400;
  margin: 0 auto 0 0;
}

.u-section-3 .u-icon-6 {
  font-size: 2.5em;
}

.u-section-3 .u-text-7 {
  margin-left: 0;
  margin-right: auto;
  margin-bottom: 0;
}

.u-section-3 .u-btn-3 {
  margin: 20px auto 0 0;
  padding: 0;
}

@media (max-width: 1199px) {
  .u-section-3 .u-sheet-1 {
    min-height: 888px;
  }

  .u-section-3 .u-shape-1 {
    height: 818px;
  }

  .u-section-3 .u-group-1 {
    min-height: 56px;
    margin-top: -848px;
    height: auto;
  }

  .u-section-3 .u-text-1 {
    width: auto;
    margin-top: 11px;
  }

  .u-section-3 .u-list-1 {
    width: 960px;
    margin-top: 20px;
    margin-bottom: -12px;
  }

  .u-section-3 .u-repeater-1 {
    min-height: 754px;
  }
}

@media (max-width: 991px) {
  .u-section-3 .u-sheet-1 {
    min-height: 1423px;
  }

  .u-section-3 .u-shape-1 {
    height: 1353px;
  }

  .u-section-3 .u-group-1 {
    width: 454px;
    min-height: 60px;
    margin-top: -1383px;
  }

  .u-section-3 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-3 .u-text-1 {
    margin-top: 13px;
    margin-left: 40px;
    margin-right: 40px;
  }

  .u-section-3 .u-list-1 {
    width: 740px;
    margin-bottom: -468px;
  }

  .u-section-3 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5px);
    grid-template-columns: repeat(2, calc(50% - 5px));
    min-height: 1274px;
  }
}

@media (max-width: 767px) {
  .u-section-3 .u-sheet-1 {
    min-height: 1414px;
  }

  .u-section-3 .u-shape-1 {
    height: 1344px;
  }

  .u-section-3 .u-group-1 {
    width: 385px;
    min-height: 53px;
    margin-top: -1374px;
  }

  .u-section-3 .u-text-1 {
    margin-top: 12px;
    margin-left: 24px;
    margin-right: 24px;
    font-size: 1.25rem;
  }

  .u-section-3 .u-list-1 {
    width: 500px;
    margin-bottom: -36px;
  }

  .u-section-3 .u-repeater-1 {
    grid-auto-columns: calc(100% + 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 575px) {
  .u-section-3 .u-sheet-1 {
    min-height: 2048px;
  }

  .u-section-3 .u-shape-1 {
    height: 1986px;
    margin-top: 42px;
  }

  .u-section-3 .u-group-1 {
    min-height: 55px;
    width: 250px;
    margin-top: -2008px;
  }

  .u-section-3 .u-container-layout-1 {
    padding-left: 0;
    padding-right: 0;
  }

  .u-section-3 .u-text-1 {
    font-size: 1rem;
    margin-top: 0;
    margin-left: 19px;
    margin-right: 19px;
  }

  .u-section-3 .u-list-1 {
    width: 300px;
    margin-bottom: 47px;
  }

  .u-section-3 .u-repeater-1 {
    grid-auto-columns: 100%;
    min-height: 1906px;
  }

  .u-section-3 .u-container-layout-2 {
    padding-bottom: 24px;
  }

  .u-section-3 .u-container-layout-4 {
    padding-bottom: 24px;
  }
}

.u-section-3 .u-list-item-3,
.u-section-3 .u-list-item-3:before,
.u-section-3 .u-list-item-3 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 .u-list-item-3.u-list-item-3.u-list-item-3:hover {
  border-color: #e8c7e5 !important;
}

.u-section-3 .u-list-item-3.u-list-item-3.u-list-item-3.hover {
  border-color: #e8c7e5 !important;
}

.u-section-3 .u-list-item-1,
.u-section-3 .u-list-item-1:before,
.u-section-3 .u-list-item-1 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 .u-list-item-1.u-list-item-1.u-list-item-1:hover {
  border-color: #e8c7e5 !important;
}

.u-section-3 .u-list-item-1.u-list-item-1.u-list-item-1.hover {
  border-color: #e8c7e5 !important;
}

.u-section-3 .u-list-item-2,
.u-section-3 .u-list-item-2:before,
.u-section-3 .u-list-item-2 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 .u-list-item-2.u-list-item-2.u-list-item-2:hover {
  border-color: #e8c7e5 !important;
}

.u-section-3 .u-list-item-2.u-list-item-2.u-list-item-2.hover {
  border-color: #e8c7e5 !important;
} .u-section-4 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-4 .u-sheet-1 {
  min-height: 414px;
}

.u-section-4 .u-text-1 {
  margin: 20px 900px 0 0;
}

.u-section-4 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: -109px;
}

.u-section-4 .u-layout-cell-1 {
  min-height: 345px;
}

.u-section-4 .u-container-layout-1 {
  padding: 0;
}

.u-section-4 .u-text-2 {
  margin: 0 20px 0 0;
}

.u-section-4 .u-text-3 {
  font-weight: 400;
  margin: 20px 96px 0 0;
}

.u-section-4 .u-btn-1 {
  margin: 20px 96px 0 0;
  padding: 0;
}

.u-section-4 .u-image-1 {
  --radius: 20px;
  background-position: 50% 50%, 50% 50%;
  background-repeat: no-repeat, no-repeat;
  background-image: linear-gradient(to bottom, rgba(132, 39, 127, 0.6), rgba(132, 39, 127, 0.6)), url('../images/site11.webp');
  background-size: cover, cover;
  min-height: 345px;
}

.u-section-4 .u-container-layout-2 {
  padding: 30px;
}

.u-section-4 .u-list-1 {
  margin: 12px 0 0;
}

.u-section-4 .u-repeater-1 {
  grid-auto-columns: calc(50% - 5px);
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 246px;
  grid-gap: 10px;
}

.u-section-4 .u-list-item-1 {
  --radius: 20px;
}

.u-section-4 .u-container-layout-3 {
  padding: 10px;
}

.u-section-4 .u-btn-2 {
  margin: 0 0 0 9px;
  padding: 0;
}

.u-section-4 .u-list-item-2 {
  --radius: 20px;
}

.u-section-4 .u-container-layout-4 {
  padding: 10px;
}

.u-section-4 .u-btn-3 {
  margin: 0 0 0 9px;
  padding: 0;
}

.u-section-4 .u-list-item-3 {
  --radius: 20px;
}

.u-section-4 .u-container-layout-5 {
  padding: 10px;
}

.u-section-4 .u-btn-4 {
  margin: 0 0 0 9px;
  padding: 0;
}

.u-section-4 .u-list-item-4 {
  --radius: 20px;
}

.u-section-4 .u-container-layout-6 {
  padding: 10px;
}

.u-section-4 .u-btn-5 {
  margin: 0 0 0 9px;
  padding: 0;
}

.u-section-4 .u-list-item-5 {
  --radius: 20px;
}

.u-section-4 .u-container-layout-7 {
  padding: 10px;
}

.u-section-4 .u-btn-6 {
  margin: 0 0 0 9px;
  padding: 0;
}

.u-section-4 .u-list-item-6 {
  --radius: 20px;
}

.u-section-4 .u-container-layout-8 {
  padding: 10px;
}

.u-section-4 .u-btn-7 {
  margin: 0 0 0 9px;
  padding: 0;
}

.u-section-4 .u-list-item-7 {
  --radius: 20px;
}

.u-section-4 .u-container-layout-9 {
  padding: 10px;
}

.u-section-4 .u-btn-8 {
  margin: 0 0 0 9px;
  padding: 0;
}

@media (max-width: 1199px) {
  .u-section-4 .u-sheet-1 {
    min-height: 331px;
  }

  .u-section-4 .u-text-1 {
    margin-right: 700px;
  }

  .u-section-4 .u-layout-wrap-1 {
    margin-top: 509px;
    position: relative;
  }

  .u-section-4 .u-layout-cell-1 {
    background-position: 50% 50%, 50% 50%;
    background-repeat: no-repeat, no-repeat;
    background-size: cover, cover;
    --radius: 20px;
    min-height: 288px;
  }

  .u-section-4 .u-text-2 {
    margin-right: 0;
  }

  .u-section-4 .u-text-3 {
    margin-right: 0;
    margin-left: -20px;
  }

  .u-section-4 .u-btn-1 {
    margin-right: 0;
  }

  .u-section-4 .u-image-1 {
    min-height: 288px;
  }

  .u-section-4 .u-list-1 {
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-4 .u-repeater-1 {
    min-height: 204px;
  }

  .u-section-4 .u-btn-2 {
    margin-left: 0;
  }

  .u-section-4 .u-btn-3 {
    margin-left: 0;
  }

  .u-section-4 .u-btn-4 {
    margin-left: 0;
  }

  .u-section-4 .u-btn-5 {
    margin-left: 0;
  }

  .u-section-4 .u-btn-6 {
    margin-left: 0;
  }

  .u-section-4 .u-btn-7 {
    margin-left: 0;
  }

  .u-section-4 .u-btn-8 {
    margin-left: 0;
  }
}

@media (max-width: 991px) {
  .u-section-4 .u-sheet-1 {
    min-height: 183px;
  }

  .u-section-4 .u-text-1 {
    margin-right: 480px;
  }

  .u-section-4 .u-layout-wrap-1 {
    margin-top: 542px;
  }

  .u-section-4 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-4 .u-image-1 {
    min-height: 225px;
  }

  .u-section-4 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 767px) {
  .u-section-4 .u-text-1 {
    margin-right: 240px;
  }

  .u-section-4 .u-layout-wrap-1 {
    margin-top: 1280px;
  }

  .u-section-4 .u-image-1 {
    min-height: 312px;
  }

  .u-section-4 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}

@media (max-width: 575px) {
  .u-section-4 .u-text-1 {
    margin-right: 40px;
  }

  .u-section-4 .u-layout-wrap-1 {
    margin-top: 1040px;
  }

  .u-section-4 .u-image-1 {
    min-height: 196px;
  }
} .u-section-5 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-5 .u-sheet-1 {
  min-height: 464px;
}

.u-section-5 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: -18px;
}

.u-section-5 .u-layout-cell-1 {
  min-height: 195px;
}

.u-section-5 .u-container-layout-1 {
  padding: 0;
}

.u-section-5 .u-text-1 {
  margin: 0 900px 0 0;
}

.u-section-5 .u-text-2 {
  margin: 10px 234px 0 0;
}

.u-section-5 .u-text-3 {
  font-weight: 400;
  margin: 20px 234px 0 0;
}

.u-section-5 .u-layout-cell-2 {
  min-height: 194px;
  --radius: 20px;
}

.u-section-5 .u-container-layout-2 {
  padding: 30px;
}

.u-section-5 .u-text-4 {
  margin: 0;
}

.u-section-5 .u-btn-1 {
  margin: 20px 0 0;
  padding: 0;
}

.u-section-5 .u-layout-cell-3 {
  min-height: 194px;
  --radius: 20px;
}

.u-section-5 .u-container-layout-3 {
  padding: 0;
}

.u-section-5 .u-list-1 {
  margin: 0 auto 0 20px;
}

.u-section-5 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 182px;
  grid-gap: 10px;
}

.u-section-5 .u-list-item-1 {
  --radius: 20px;
}

.u-section-5 .u-container-layout-4 {
  padding: 10px;
}

.u-section-5 .u-btn-2 {
  margin: 0 0 0 9px;
  padding: 0;
}

.u-section-5 .u-list-item-2 {
  --radius: 20px;
}

.u-section-5 .u-container-layout-5 {
  padding: 10px;
}

.u-section-5 .u-btn-3 {
  margin: 0 0 0 9px;
  padding: 0;
}

.u-section-5 .u-list-item-3 {
  --radius: 20px;
}

.u-section-5 .u-container-layout-6 {
  padding: 10px;
}

.u-section-5 .u-btn-4 {
  margin: 0 0 0 9px;
  padding: 0;
}

.u-section-5 .u-list-item-4 {
  --radius: 20px;
}

.u-section-5 .u-container-layout-7 {
  padding: 10px;
}

.u-section-5 .u-btn-5 {
  margin: 0 0 0 9px;
  padding: 0;
}

.u-section-5 .u-list-item-5 {
  --radius: 20px;
}

.u-section-5 .u-container-layout-8 {
  padding: 10px;
}

.u-section-5 .u-btn-6 {
  margin: 0 0 0 9px;
  padding: 0;
}

.u-section-5 .u-list-item-6 {
  --radius: 20px;
}

.u-section-5 .u-container-layout-9 {
  padding: 10px;
}

.u-section-5 .u-btn-7 {
  margin: 0 0 0 9px;
  padding: 0;
}

.u-section-5 .u-list-item-7 {
  --radius: 20px;
}

.u-section-5 .u-container-layout-10 {
  padding: 10px;
}

.u-section-5 .u-btn-8 {
  margin: 0 0 0 9px;
  padding: 0;
}

@media (max-width: 1199px) {
  .u-section-5 .u-sheet-1 {
    min-height: 484px;
  }

  .u-section-5 .u-layout-wrap-1 {
    position: relative;
  }

  .u-section-5 .u-layout-cell-1 {
    min-height: 163px;
  }

  .u-section-5 .u-text-1 {
    margin-right: 700px;
  }

  .u-section-5 .u-text-2 {
    margin-right: 34px;
  }

  .u-section-5 .u-text-3 {
    margin-right: 34px;
  }

  .u-section-5 .u-layout-cell-2 {
    min-height: 161px;
  }

  .u-section-5 .u-layout-cell-3 {
    min-height: 161px;
  }

  .u-section-5 .u-list-1 {
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-5 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 160px;
  }

  .u-section-5 .u-btn-2 {
    margin-left: 0;
  }

  .u-section-5 .u-btn-3 {
    margin-left: 0;
  }

  .u-section-5 .u-btn-4 {
    margin-left: 0;
  }

  .u-section-5 .u-btn-5 {
    margin-left: 0;
  }

  .u-section-5 .u-btn-6 {
    margin-left: 0;
  }

  .u-section-5 .u-btn-7 {
    margin-left: 0;
  }

  .u-section-5 .u-btn-8 {
    margin-left: 0;
  }
}

@media (max-width: 991px) {
  .u-section-5 .u-sheet-1 {
    min-height: 359px;
  }

  .u-section-5 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-5 .u-text-1 {
    margin-right: 480px;
  }

  .u-section-5 .u-text-2 {
    margin-right: 0;
  }

  .u-section-5 .u-text-3 {
    margin-right: 0;
  }

  .u-section-5 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-5 .u-layout-cell-3 {
    min-height: 100px;
  }

  .u-section-5 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 251px;
  }
}

@media (max-width: 767px) {
  .u-section-5 .u-text-1 {
    margin-right: 240px;
  }

  .u-section-5 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-5 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 575px) {
  .u-section-5 .u-text-1 {
    margin-right: 40px;
  }

  .u-section-5 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
} .u-section-6 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-6 .u-sheet-1 {
  min-height: 625px;
}

.u-section-6 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: 10px;
}

.u-section-6 .u-layout-cell-1 {
  min-height: 627px;
}

.u-section-6 .u-container-layout-1 {
  padding: 0;
}

.u-section-6 .u-group-1 {
  --radius: 20px;
  min-height: 491px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
}

.u-section-6 .u-container-layout-2 {
  padding: 30px;
}

.u-section-6 .u-text-1 {
  font-size: 1.875rem;
  font-weight: 600;
  margin: 0;
}

.u-section-6 .u-text-2 {
  margin: 20px 0 0;
}

.u-section-6 .u-btn-1 {
  margin: 34px auto 0 20px;
  padding: 0;
}

.u-section-6 .u-layout-cell-2 {
  min-height: 615px;
}

.u-section-6 .u-container-layout-3 {
  padding: 0;
}

.u-section-6 .u-group-2 {
  width: 800px;
  min-height: 607px;
  height: auto;
  margin: 0 -7px 0 auto;
}

.u-section-6 .u-container-layout-4 {
  padding: 30px 30px 0;
}

.u-section-6 .u-text-3 {
  font-weight: 700;
  margin: 0 auto 0 0;
}

.u-section-6 .u-text-4 {
  font-weight: 500;
  margin: 20px auto 0 0;
}

.u-section-6 .u-text-5 {
  margin: 10px 10px 0 0;
}

.u-section-6 .u-text-6 {
  font-weight: 500;
  margin: 20px auto 0 0;
}

.u-section-6 .u-text-7 {
  margin: 10px 10px 0 0;
}

.u-section-6 .u-image-1 {
  width: 500px;
  height: 256px;
  --radius: 20px;
  margin: 30px auto 0 0;
}

@media (max-width: 1199px) {
  .u-section-6 .u-sheet-1 {
    min-height: 526px;
  }

  .u-section-6 .u-layout-cell-1 {
    min-height: 523px;
  }

  .u-section-6 .u-group-1 {
    --radius: 30px;
    height: auto;
  }

  .u-section-6 .u-text-1 {
    font-size: 2.25rem;
  }

  .u-section-6 .u-btn-1 {
    margin-left: 0;
  }

  .u-section-6 .u-layout-cell-2 {
    min-height: 513px;
  }

  .u-section-6 .u-group-2 {
    width: 660px;
    margin-right: 0;
    height: auto;
  }
}

@media (max-width: 991px) {
  .u-section-6 .u-sheet-1 {
    min-height: 1233px;
  }

  .u-section-6 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-6 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-6 .u-group-2 {
    width: 513px;
  }

  .u-section-6 .u-text-5 {
    margin-right: 0;
  }

  .u-section-6 .u-text-7 {
    margin-right: 0;
  }
}

@media (max-width: 767px) {
  .u-section-6 .u-sheet-1 {
    min-height: 1792px;
  }

  .u-section-6 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-6 .u-container-layout-4 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-6 .u-sheet-1 {
    min-height: 1464px;
  }

  .u-section-6 .u-group-2 {
    width: 340px;
  }

  .u-section-6 .u-image-1 {
    width: 320px;
    height: 164px;
  }
} .u-section-7 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-7 .u-sheet-1 {
  min-height: 332px;
}

.u-section-7 .u-text-1 {
  margin: 20px 782px 0 0;
}

.u-section-7 .u-layout-wrap-1 {
  margin-top: 10px;
  margin-bottom: 20px;
}

.u-section-7 .u-layout-cell-1 {
  min-height: 253px;
}

.u-section-7 .u-container-layout-1 {
  padding: 30px 0;
}

.u-section-7 .u-text-2 {
  margin: 0 30px 0 0;
}

.u-section-7 .u-layout-cell-2 {
  min-height: 253px;
}

.u-section-7 .u-container-layout-2 {
  padding: 30px 30px 28px;
}

.u-section-7 .u-text-3 {
  font-weight: 400;
  margin: 80px 0 0;
}

@media (max-width: 1199px) {
  .u-section-7 .u-sheet-1 {
    min-height: 340px;
  }

  .u-section-7 .u-text-1 {
    margin-right: 582px;
  }

  .u-section-7 .u-layout-wrap-1 {
    position: relative;
  }

  .u-section-7 .u-layout-cell-1 {
    min-height: 211px;
  }

  .u-section-7 .u-text-2 {
    margin-right: 0;
  }

  .u-section-7 .u-layout-cell-2 {
    min-height: 211px;
  }
}

@media (max-width: 991px) {
  .u-section-7 .u-sheet-1 {
    min-height: 398px;
  }

  .u-section-7 .u-text-1 {
    margin-right: 362px;
  }

  .u-section-7 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-7 .u-layout-cell-2 {
    min-height: 100px;
  }
}

@media (max-width: 767px) {
  .u-section-7 .u-sheet-1 {
    min-height: 567px;
  }

  .u-section-7 .u-text-1 {
    margin-right: 122px;
  }

  .u-section-7 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-7 .u-sheet-1 {
    min-height: 472px;
  }

  .u-section-7 .u-text-1 {
    margin-right: 0;
  }
} .u-section-8 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-8 .u-sheet-1 {
  min-height: 625px;
}

.u-section-8 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: 10px;
}

.u-section-8 .u-layout-cell-1 {
  min-height: 627px;
}

.u-section-8 .u-container-layout-1 {
  padding: 0;
}

.u-section-8 .u-group-1 {
  --radius: 20px;
  min-height: 491px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
}

.u-section-8 .u-container-layout-2 {
  padding: 30px;
}

.u-section-8 .u-text-1 {
  font-size: 1.875rem;
  font-weight: 600;
  margin: 0;
}

.u-section-8 .u-text-2 {
  margin: 20px 0 0;
}

.u-section-8 .u-btn-1 {
  margin: 34px auto 0 20px;
  padding: 0;
}

.u-section-8 .u-layout-cell-2 {
  min-height: 615px;
}

.u-section-8 .u-container-layout-3 {
  padding: 0;
}

.u-section-8 .u-group-2 {
  width: 800px;
  min-height: 607px;
  height: auto;
  margin: 0 -7px 0 auto;
}

.u-section-8 .u-container-layout-4 {
  padding: 30px 30px 0;
}

.u-section-8 .u-text-3 {
  margin: 0 10px 0 0;
}

.u-section-8 .u-text-4 {
  font-weight: 700;
  margin: 20px auto 0 0;
}

.u-section-8 .u-text-5 {
  margin: 20px 10px 0 0;
}

.u-section-8 .u-text-6 {
  font-weight: 700;
  margin: 20px auto 0 0;
}

.u-section-8 .u-text-7 {
  margin: 20px 10px 0 0;
}

.u-section-8 .u-text-8 {
  font-weight: 700;
  margin: 20px auto 0 0;
}

.u-section-8 .u-text-9 {
  margin-left: 0;
  margin-right: auto;
  margin-bottom: 0;
}

@media (max-width: 1199px) {
  .u-section-8 .u-sheet-1 {
    min-height: 526px;
  }

  .u-section-8 .u-layout-cell-1 {
    min-height: 523px;
  }

  .u-section-8 .u-group-1 {
    --radius: 30px;
    height: auto;
  }

  .u-section-8 .u-text-1 {
    font-size: 2.25rem;
  }

  .u-section-8 .u-btn-1 {
    margin-left: 0;
  }

  .u-section-8 .u-layout-cell-2 {
    min-height: 513px;
  }

  .u-section-8 .u-group-2 {
    width: 660px;
    margin-right: 0;
    height: auto;
  }

  .u-section-8 .u-text-7 {
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-8 .u-sheet-1 {
    min-height: 1233px;
  }

  .u-section-8 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-8 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-8 .u-group-2 {
    width: 513px;
  }

  .u-section-8 .u-text-3 {
    margin-right: 0;
  }

  .u-section-8 .u-text-5 {
    margin-right: 0;
  }
}

@media (max-width: 767px) {
  .u-section-8 .u-sheet-1 {
    min-height: 1792px;
  }

  .u-section-8 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-8 .u-container-layout-4 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-8 .u-sheet-1 {
    min-height: 1464px;
  }

  .u-section-8 .u-group-2 {
    width: 340px;
  }
} .u-section-9 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-9 .u-sheet-1 {
  min-height: 583px;
}

.u-section-9 .u-text-1 {
  margin: 20px 900px 0 0;
}

.u-section-9 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: 0;
}

.u-section-9 .u-layout-cell-1 {
  min-height: 500px;
}

.u-section-9 .u-container-layout-1 {
  padding: 0;
}

.u-section-9 .u-text-2 {
  margin: 0 20px 0 0;
}

.u-section-9 .u-text-3 {
  font-weight: 400;
  margin: 20px 96px 0 0;
}

.u-section-9 .u-btn-1 {
  margin: 20px 96px 0 0;
  padding: 0;
}

.u-section-9 .u-btn-2 {
  margin: 20px 96px 0 0;
  padding: 0;
}

.u-section-9 .u-btn-3 {
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-9 .u-image-1 {
  min-height: 238px;
  background-position: 50% 50%, 50% 50%;
  background-repeat: no-repeat, no-repeat;
  background-image: linear-gradient(to bottom, rgba(132, 39, 127, 0.25), rgba(132, 39, 127, 0.25)), url('../images/site11.webp');
  background-size: cover, cover;
  --radius: 20px;
}

.u-section-9 .u-container-layout-2 {
  padding: 30px;
}

.u-section-9 .u-layout-cell-3 {
  min-height: 238px;
}

.u-section-9 .u-container-layout-3 {
  padding: 0;
}

.u-section-9 .u-list-1 {
  margin-top: 0;
  margin-bottom: -4px;
}

.u-section-9 .u-repeater-1 {
  grid-auto-columns: calc(50% - 5px);
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 246px;
  grid-gap: 10px;
}

.u-section-9 .u-list-item-1 {
  --radius: 20px;
}

.u-section-9 .u-container-layout-4 {
  padding: 10px;
}

.u-section-9 .u-btn-4 {
  margin: 0 0 0 9px;
  padding: 0;
}

.u-section-9 .u-list-item-2 {
  --radius: 20px;
}

.u-section-9 .u-container-layout-5 {
  padding: 10px;
}

.u-section-9 .u-btn-5 {
  margin: 0 0 0 9px;
  padding: 0;
}

.u-section-9 .u-list-item-3 {
  --radius: 20px;
}

.u-section-9 .u-container-layout-6 {
  padding: 10px;
}

.u-section-9 .u-btn-6 {
  margin: 0 0 0 9px;
  padding: 0;
}

.u-section-9 .u-list-item-4 {
  --radius: 20px;
}

.u-section-9 .u-container-layout-7 {
  padding: 10px;
}

.u-section-9 .u-btn-7 {
  margin: 0 0 0 9px;
  padding: 0;
}

.u-section-9 .u-list-item-5 {
  --radius: 20px;
}

.u-section-9 .u-container-layout-8 {
  padding: 10px;
}

.u-section-9 .u-btn-8 {
  margin: 0 0 0 9px;
  padding: 0;
}

.u-section-9 .u-list-item-6 {
  --radius: 20px;
}

.u-section-9 .u-container-layout-9 {
  padding: 10px;
}

.u-section-9 .u-btn-9 {
  margin: 0 0 0 9px;
  padding: 0;
}

.u-section-9 .u-list-item-7 {
  --radius: 20px;
}

.u-section-9 .u-container-layout-10 {
  padding: 10px;
}

.u-section-9 .u-btn-10 {
  margin: 0 0 0 9px;
  padding: 0;
}

@media (max-width: 1199px) {
  .u-section-9 .u-sheet-1 {
    min-height: 471px;
  }

  .u-section-9 .u-text-1 {
    margin-right: 700px;
  }

  .u-section-9 .u-layout-wrap-1 {
    margin-top: 509px;
    position: relative;
  }

  .u-section-9 .u-layout-cell-1 {
    background-position: 50% 50%, 50% 50%;
    background-repeat: no-repeat, no-repeat;
    background-size: cover, cover;
    --radius: 20px;
    min-height: 417px;
  }

  .u-section-9 .u-text-2 {
    margin-right: 0;
  }

  .u-section-9 .u-text-3 {
    margin-right: 0;
    margin-left: -20px;
  }

  .u-section-9 .u-btn-1 {
    margin-right: 0;
  }

  .u-section-9 .u-btn-2 {
    margin-right: 0;
  }

  .u-section-9 .u-image-1 {
    min-height: 198px;
  }

  .u-section-9 .u-layout-cell-3 {
    min-height: 198px;
  }

  .u-section-9 .u-repeater-1 {
    min-height: 211px;
  }

  .u-section-9 .u-btn-4 {
    margin-left: 0;
  }

  .u-section-9 .u-btn-6 {
    margin-left: 0;
  }

  .u-section-9 .u-btn-7 {
    margin-left: 0;
  }

  .u-section-9 .u-btn-8 {
    margin-left: 0;
  }

  .u-section-9 .u-btn-9 {
    margin-left: 0;
  }

  .u-section-9 .u-btn-10 {
    margin-left: 0;
  }
}

@media (max-width: 991px) {
  .u-section-9 .u-sheet-1 {
    min-height: 227px;
  }

  .u-section-9 .u-text-1 {
    margin-right: 480px;
  }

  .u-section-9 .u-layout-wrap-1 {
    margin-top: 542px;
  }

  .u-section-9 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-9 .u-image-1 {
    min-height: 154px;
  }

  .u-section-9 .u-layout-cell-3 {
    min-height: 100px;
  }

  .u-section-9 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 767px) {
  .u-section-9 .u-sheet-1 {
    min-height: 312px;
  }

  .u-section-9 .u-text-1 {
    margin-right: 240px;
  }

  .u-section-9 .u-layout-wrap-1 {
    margin-top: 1280px;
  }

  .u-section-9 .u-image-1 {
    min-height: 213px;
  }

  .u-section-9 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-9 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}

@media (max-width: 575px) {
  .u-section-9 .u-sheet-1 {
    min-height: 199px;
  }

  .u-section-9 .u-text-1 {
    margin-right: 40px;
  }

  .u-section-9 .u-layout-wrap-1 {
    margin-top: 1040px;
  }

  .u-section-9 .u-image-1 {
    min-height: 134px;
  }
} .u-section-10 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-10 .u-sheet-1 {
  min-height: 332px;
}

.u-section-10 .u-text-1 {
  margin: 20px 0 0;
}

.u-section-10 .u-layout-wrap-1 {
  margin-top: 10px;
  margin-bottom: 20px;
}

.u-section-10 .u-layout-cell-1 {
  min-height: 253px;
}

.u-section-10 .u-container-layout-1 {
  padding: 30px 30px 30px 0;
}

.u-section-10 .u-text-2 {
  margin: 0 47px 0 0;
}

.u-section-10 .u-layout-cell-2 {
  min-height: 253px;
}

.u-section-10 .u-container-layout-2 {
  padding: 30px 30px 28px;
}

.u-section-10 .u-text-3 {
  font-weight: 400;
  margin: 80px 0 0;
}

@media (max-width: 1199px) {
  .u-section-10 .u-sheet-1 {
    min-height: 340px;
  }

  .u-section-10 .u-layout-wrap-1 {
    position: relative;
  }

  .u-section-10 .u-layout-cell-1 {
    min-height: 211px;
  }

  .u-section-10 .u-text-2 {
    margin-right: 0;
  }

  .u-section-10 .u-layout-cell-2 {
    min-height: 211px;
  }
}

@media (max-width: 991px) {
  .u-section-10 .u-sheet-1 {
    min-height: 398px;
  }

  .u-section-10 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-10 .u-layout-cell-2 {
    min-height: 100px;
  }
}

@media (max-width: 767px) {
  .u-section-10 .u-sheet-1 {
    min-height: 567px;
  }

  .u-section-10 .u-container-layout-1 {
    padding-right: 10px;
  }

  .u-section-10 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-10 .u-sheet-1 {
    min-height: 472px;
  }
} .u-section-11 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-11 .u-sheet-1 {
  min-height: 398px;
}

.u-section-11 .u-text-1 {
  font-weight: 300;
  margin: 20px 820px 0 30px;
}

.u-section-11 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: -167px;
}

.u-section-11 .u-layout-cell-1 {
  min-height: 309px;
}

.u-section-11 .u-container-layout-1 {
  padding: 30px;
}

.u-section-11 .u-text-2 {
  font-weight: 400;
  margin-bottom: 0;
  margin-top: 0;
}

.u-section-11 .u-text-3 {
  margin-left: 0;
  margin-right: 0;
  margin-bottom: 0;
}

.u-section-11 .u-layout-cell-2 {
  min-height: 309px;
}

.u-section-11 .u-container-layout-2 {
  padding: 0;
}

.u-section-11 .u-slider-1 {
  min-height: 309px;
  height: auto;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-11 .u-carousel-indicators-1 {
  position: absolute;
  bottom: 10px;
  width: auto;
  height: auto;
}

.u-section-11 .u-carousel-item-1 {
  margin-top: 0;
}

.u-section-11 .u-container-layout-3 {
  padding: 10px;
}

.u-section-11 .u-group-1 {
  --radius: 20px;
  min-height: 286px;
  height: auto;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-11 .u-container-layout-4 {
  padding: 20px;
}

.u-section-11 .u-text-4 {
  margin: 0 auto 0 0;
}

.u-section-11 .u-text-5 {
  font-weight: 700;
  margin: 20px 0 0;
}

.u-section-11 .u-text-6 {
  font-weight: 700;
  margin: 20px 0 0;
}

.u-section-11 .u-carousel-item-2 {
  margin-top: 0;
}

.u-section-11 .u-container-layout-5 {
  padding: 10px;
}

.u-section-11 .u-group-2 {
  --radius: 20px;
  min-height: 286px;
  height: auto;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-11 .u-container-layout-6 {
  padding: 20px;
}

.u-section-11 .u-text-7 {
  margin: 0 auto 0 0;
}

.u-section-11 .u-text-8 {
  font-weight: 700;
  margin: 20px 0 0;
}

.u-section-11 .u-text-9 {
  font-weight: 700;
  margin: 20px 0 0;
}

.u-section-11 .u-carousel-control-1 {
  width: 50px;
  height: 50px;
  left: -350px;
  position: absolute;
  top: 169px;
  right: auto;
  background-image: none;
  --radius: 5px;
  padding: 10px;
}

.u-section-11 .u-carousel-control-2 {
  width: 50px;
  height: 50px;
  left: -280px;
  position: absolute;
  top: 169px;
  right: auto;
  background-image: none;
  --radius: 5px;
  padding: 10px;
}

@media (max-width: 1199px) {
  .u-section-11 .u-text-1 {
    margin-left: 0;
    margin-right: 650px;
  }

  .u-section-11 .u-layout-wrap-1 {
    margin-bottom: -82px;
    position: relative;
  }

  .u-section-11 .u-layout-cell-1 {
    min-height: 258px;
  }

  .u-section-11 .u-layout-cell-2 {
    min-height: 258px;
  }

  .u-section-11 .u-carousel-item-1 {
    margin-bottom: 0;
  }

  .u-section-11 .u-group-1 {
    height: auto;
  }

  .u-section-11 .u-carousel-item-2 {
    margin-bottom: 0;
  }

  .u-section-11 .u-group-2 {
    height: auto;
  }

  .u-section-11 .u-carousel-control-1 {
    left: -283px;
    bottom: 48px;
  }

  .u-section-11 .u-carousel-control-2 {
    left: -213px;
    bottom: 48px;
  }
}

@media (max-width: 991px) {
  .u-section-11 .u-sheet-1 {
    min-height: 182px;
  }

  .u-section-11 .u-text-1 {
    margin-right: 430px;
  }

  .u-section-11 .u-layout-wrap-1 {
    margin-bottom: 20px;
  }

  .u-section-11 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-11 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-11 .u-slider-1 {
    min-height: 332px;
  }

  .u-section-11 .u-carousel-control-1 {
    left: -211px;
    bottom: 11px;
  }

  .u-section-11 .u-carousel-control-2 {
    left: -141px;
    bottom: 11px;
  }
}

@media (max-width: 767px) {
  .u-section-11 .u-sheet-1 {
    min-height: 282px;
  }

  .u-section-11 .u-text-1 {
    margin-right: 190px;
  }

  .u-section-11 .u-layout-cell-1 {
    min-height: 204px;
  }

  .u-section-11 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-11 .u-container-layout-4 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-11 .u-container-layout-6 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-11 .u-carousel-control-1 {
    left: 13px;
    top: -82px;
    bottom: 48px;
  }

  .u-section-11 .u-carousel-control-2 {
    left: 83px;
    top: -82px;
    bottom: 48px;
  }
}

@media (max-width: 575px) {
  .u-section-11 .u-sheet-1 {
    min-height: 668px;
  }

  .u-section-11 .u-text-1 {
    margin-right: 0;
  }

  .u-section-11 .u-layout-cell-1 {
    min-height: 198px;
  }

  .u-section-11 .u-slider-1 {
    min-height: 384px;
  }

  .u-section-11 .u-carousel-control-1 {
    width: 30px;
    height: 30px;
    left: 10px;
    top: -59px;
  }

  .u-section-11 .u-carousel-control-2 {
    width: 30px;
    height: 30px;
    left: 60px;
    top: -59px;
  }
} .u-section-12 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-12 .u-sheet-1 {
  min-height: 586px;
}

.u-section-12 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: 60px;
}

.u-section-12 .u-layout-cell-1 {
  min-height: 143px;
}

.u-section-12 .u-container-layout-1 {
  padding: 30px;
}

.u-section-12 .u-text-1 {
  letter-spacing: 2px;
  font-weight: 400;
  margin: 0 315px 0 0;
}

.u-section-12 .u-layout-cell-2 {
  min-height: 58px;
}

.u-section-12 .u-container-layout-2 {
  padding: 26px 30px;
}

.u-section-12 .u-text-2 {
  font-weight: 400;
  margin: 4px 0 0;
}

.u-section-12 .u-layout-cell-3 {
  min-height: 372px;
}

.u-section-12 .u-container-layout-3 {
  padding: 30px;
}

.u-section-12 .u-accordion-1 {
  width: 938px;
  margin: 0 auto;
}

.u-section-12 .u-accordion-item-1 {
  margin-bottom: 0;
}

.u-section-12 .u-accordion-link-1 {
  background-image: none;
  --radius: 20px;
  font-size: 1.5rem;
  padding: 20px 30px;
}

.u-section-12 .u-icon-2 {
  height: 20px;
  width: 20px;
}

.u-section-12 .u-accordion-pane-1 {
  min-height: 150px;
}

.u-section-12 .u-container-layout-4 {
  padding: 20px;
}

.u-section-12 .u-accordion-item-2 {
  margin: 10px 0 0;
}

.u-section-12 .u-accordion-link-2 {
  background-image: none;
  --radius: 20px;
  font-size: 1.5rem;
  padding: 20px 30px;
}

.u-section-12 .u-icon-4 {
  height: 20px;
  width: 20px;
}

.u-section-12 .u-accordion-pane-2 {
  min-height: 150px;
}

.u-section-12 .u-container-layout-5 {
  padding: 20px;
}

.u-section-12 .u-accordion-item-3 {
  margin: 10px 0 0;
}

.u-section-12 .u-accordion-link-3 {
  background-image: none;
  --radius: 20px;
  font-size: 1.5rem;
  padding: 20px 30px;
}

.u-section-12 .u-icon-6 {
  height: 20px;
  width: 20px;
}

.u-section-12 .u-accordion-pane-3 {
  min-height: 150px;
}

.u-section-12 .u-container-layout-6 {
  padding: 20px;
}

.u-section-12 .u-accordion-item-4 {
  margin: 10px 0 0;
}

.u-section-12 .u-accordion-link-4 {
  background-image: none;
  --radius: 20px;
  font-size: 1.5rem;
  padding: 20px 30px;
}

.u-section-12 .u-icon-8 {
  height: 20px;
  width: 20px;
}

.u-section-12 .u-accordion-pane-4 {
  min-height: 150px;
}

.u-section-12 .u-container-layout-7 {
  padding: 20px;
}

.u-section-12 .u-accordion-item-5 {
  margin: 10px 0 0;
}

.u-section-12 .u-accordion-link-5 {
  background-image: none;
  --radius: 20px;
  font-size: 1.5rem;
  padding: 20px 30px;
}

.u-section-12 .u-icon-10 {
  height: 20px;
  width: 20px;
}

.u-section-12 .u-accordion-pane-5 {
  min-height: 150px;
}

.u-section-12 .u-container-layout-8 {
  padding: 20px;
}

@media (max-width: 1199px) {
  .u-section-12 .u-sheet-1 {
    min-height: 495px;
  }

  .u-section-12 .u-layout-wrap-1 {
    position: relative;
  }

  .u-section-12 .u-layout-cell-1 {
    min-height: 119px;
  }

  .u-section-12 .u-text-1 {
    margin-right: 215px;
  }

  .u-section-12 .u-layout-cell-2 {
    min-height: 48px;
  }

  .u-section-12 .u-layout-cell-3 {
    min-height: 310px;
  }
}

@media (max-width: 991px) {
  .u-section-12 .u-sheet-1 {
    min-height: 140px;
  }

  .u-section-12 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-12 .u-text-1 {
    margin-right: 105px;
  }

  .u-section-12 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-12 .u-layout-cell-3 {
    min-height: 100px;
  }

  .u-section-12 .u-accordion-1 {
    width: 720px;
  }
}

@media (max-width: 767px) {
  .u-section-12 .u-sheet-1 {
    min-height: 240px;
  }

  .u-section-12 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-12 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-12 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-12 .u-accordion-1 {
    width: 520px;
  }

  .u-section-12 .u-accordion-link-1 {
    font-size: 1rem;
  }

  .u-section-12 .u-container-layout-4 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-12 .u-accordion-link-2 {
    font-size: 1rem;
  }

  .u-section-12 .u-container-layout-5 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-12 .u-accordion-link-3 {
    font-size: 1rem;
  }

  .u-section-12 .u-container-layout-6 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-12 .u-accordion-link-4 {
    font-size: 1rem;
  }

  .u-section-12 .u-container-layout-7 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-12 .u-accordion-link-5 {
    font-size: 1rem;
  }

  .u-section-12 .u-container-layout-8 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-12 .u-text-1 {
    margin-right: 0;
  }

  .u-section-12 .u-accordion-1 {
    width: 320px;
  }
} .u-section-13 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-13 .u-sheet-1 {
  min-height: 320px;
}

.u-section-13 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: 20px;
  position: relative;
}

.u-section-13 .u-layout-cell-1 {
  min-height: 400px;
  --radius: 10px;
}

.u-section-13 .u-container-layout-1 {
  padding: 30px;
}

.u-section-13 .u-text-1 {
  margin-bottom: 0;
  margin-top: 0;
  font-size: 2.25rem;
}

.u-section-13 .u-text-2 {
  margin-left: 0;
  margin-right: 0;
  margin-bottom: 0;
  font-weight: 400;
}

.u-section-13 .u-layout-cell-2 {
  min-height: 400px;
}

.u-section-13 .u-container-layout-2 {
  padding: 30px;
}

.u-section-13 .u-text-3 {
  font-size: 2.25rem;
  margin: 0 auto 0 0;
}

.u-section-13 .u-list-1 {
  margin: 20px 0 0;
}

.u-section-13 .u-repeater-1 {
  grid-auto-columns: calc(50% - 5px);
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 246px;
  grid-gap: 10px;
}

.u-section-13 .u-list-item-1 {
  --radius: 20px;
}

.u-section-13 .u-container-layout-3 {
  padding: 10px;
}

.u-section-13 .u-text-4 {
  margin: 0 auto 0 0;
}

.u-section-13 .u-list-item-2 {
  --radius: 20px;
}

.u-section-13 .u-container-layout-4 {
  padding: 10px;
}

.u-section-13 .u-text-5 {
  margin: 0 auto 0 0;
}

.u-section-13 .u-list-item-3 {
  --radius: 20px;
}

.u-section-13 .u-container-layout-5 {
  padding: 10px;
}

.u-section-13 .u-text-6 {
  margin: 0 auto 0 0;
}

.u-section-13 .u-list-item-4 {
  --radius: 20px;
}

.u-section-13 .u-container-layout-6 {
  padding: 10px;
}

.u-section-13 .u-text-7 {
  margin: 0 auto 0 0;
}

.u-section-13 .u-list-item-5 {
  --radius: 20px;
}

.u-section-13 .u-container-layout-7 {
  padding: 10px;
}

.u-section-13 .u-text-8 {
  margin: 0 auto 0 0;
}

.u-section-13 .u-list-item-6 {
  --radius: 20px;
}

.u-section-13 .u-container-layout-8 {
  padding: 10px;
}

.u-section-13 .u-text-9 {
  margin: 0 auto 0 0;
}

.u-section-13 .u-list-item-7 {
  --radius: 20px;
}

.u-section-13 .u-container-layout-9 {
  padding: 10px;
}

.u-section-13 .u-text-10 {
  margin: 0 auto 0 0;
}

@media (max-width: 1199px) {
  .u-section-13 .u-sheet-1 {
    min-height: 253px;
  }

  .u-section-13 .u-layout-cell-1 {
    min-height: 333px;
  }

  .u-section-13 .u-layout-cell-2 {
    min-height: 333px;
  }

  .u-section-13 .u-list-1 {
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-13 .u-repeater-1 {
    min-height: 205px;
  }
}

@media (max-width: 991px) {
  .u-section-13 .u-sheet-1 {
    min-height: 20px;
  }

  .u-section-13 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-13 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-13 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 767px) {
  .u-section-13 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-13 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-13 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
} .u-section-14 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-14 .u-sheet-1 {
  min-height: 218px;
}

.u-section-14 .u-line-1 {
  margin-top: 20px;
  margin-bottom: 0;
  transform-origin: left center;
}

.u-section-14 .u-layout-wrap-1 {
  margin-top: 30px;
  margin-bottom: 18px;
}

.u-section-14 .u-layout-cell-1 {
  min-height: 147px;
}

.u-section-14 .u-container-layout-1 {
  padding: 30px;
}

.u-section-14 .u-text-1 {
  font-weight: 600;
  font-size: 1.5rem;
  margin: 0 auto 0 0;
}

.u-section-14 .u-layout-cell-2 {
  min-height: 147px;
}

.u-section-14 .u-container-layout-2 {
  padding: 29px 0;
}

.u-section-14 .u-list-1 {
  width: 1119px;
  margin: 0 -79px 0 auto;
}

.u-section-14 .u-repeater-1 {
  grid-auto-columns: 20%;
  grid-template-columns: repeat(5, 20%);
  min-height: 88px;
  grid-gap: 0px;
}

.u-section-14 .u-container-layout-3 {
  padding: 10px 10px 5px;
}

.u-section-14 .u-btn-1 {
  margin-bottom: 0;
  margin-top: 0;
  padding: 0;
}

.u-section-14 .u-container-layout-4 {
  padding: 10px 10px 5px;
}

.u-section-14 .u-btn-2 {
  margin-bottom: 0;
  margin-top: 0;
  padding: 0;
}

.u-section-14 .u-container-layout-5 {
  padding: 10px 10px 5px;
}

.u-section-14 .u-btn-3 {
  margin-bottom: 0;
  margin-top: 0;
  padding: 0;
}

.u-section-14 .u-container-layout-6 {
  padding: 10px 10px 5px;
}

.u-section-14 .u-btn-4 {
  margin-bottom: 0;
  margin-top: 0;
  padding: 0;
}

.u-section-14 .u-container-layout-7 {
  padding: 10px 10px 5px;
}

.u-section-14 .u-btn-5 {
  margin-bottom: 0;
  margin-top: 0;
  padding: 0;
}

.u-section-14 .u-container-layout-8 {
  padding: 10px 10px 5px;
}

.u-section-14 .u-btn-6 {
  margin-bottom: 0;
  margin-top: 0;
  padding: 0;
}

.u-section-14 .u-container-layout-9 {
  padding: 10px 10px 5px;
}

.u-section-14 .u-btn-7 {
  margin-bottom: 0;
  margin-top: 0;
  padding: 0;
}

@media (max-width: 1199px) {
  .u-section-14 .u-sheet-1 {
    min-height: 194px;
  }

  .u-section-14 .u-layout-wrap-1 {
    margin-bottom: -169px;
    position: relative;
  }

  .u-section-14 .u-layout-cell-1 {
    min-height: 123px;
  }

  .u-section-14 .u-layout-cell-2 {
    min-height: 123px;
  }

  .u-section-14 .u-list-1 {
    width: 850px;
    margin-right: 0;
  }

  .u-section-14 .u-repeater-1 {
    min-height: 73px;
  }
}

@media (max-width: 991px) {
  .u-section-14 .u-sheet-1 {
    min-height: 171px;
  }

  .u-section-14 .u-layout-wrap-1 {
    margin-bottom: -103px;
  }

  .u-section-14 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-14 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-14 .u-list-1 {
    width: 663px;
  }

  .u-section-14 .u-repeater-1 {
    grid-auto-columns: 33.333333333333336%;
    grid-template-columns: repeat(3, 33.333333333333336%);
    min-height: 227px;
  }
}

@media (max-width: 767px) {
  .u-section-14 .u-sheet-1 {
    min-height: 271px;
  }

  .u-section-14 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-14 .u-text-1 {
    font-size: 1rem;
  }

  .u-section-14 .u-list-1 {
    width: 540px;
  }

  .u-section-14 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5px);
    grid-template-columns: repeat(2, calc(50% - 5px));
  }
}

@media (max-width: 575px) {
  .u-section-14 .u-layout-wrap-1 {
    margin-bottom: 20px;
  }

  .u-section-14 .u-list-1 {
    width: 340px;
  }
} .u-section-15 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
  min-height: 680px;
}

.u-section-15 .u-gallery-1 {
  height: 600px;
  margin-top: 20px;
  margin-bottom: 60px;
}

.u-section-15 .u-gallery-inner-1 {
  grid-template-columns: repeat(3, auto);
  grid-gap: 10px;
}

.u-section-15 .u-over-slide-1 {
  background-image: linear-gradient(0deg, rgba(0,0,0,0.2), rgba(0,0,0,0.2));
  padding: 20px;
}

.u-section-15 .u-over-slide-2 {
  background-image: linear-gradient(0deg, rgba(0,0,0,0.2), rgba(0,0,0,0.2));
  padding: 20px;
}

.u-section-15 .u-over-slide-3 {
  background-image: linear-gradient(0deg, rgba(0,0,0,0.2), rgba(0,0,0,0.2));
  padding: 20px;
}

.u-section-15 .u-over-slide-4 {
  background-image: linear-gradient(0deg, rgba(0,0,0,0.2), rgba(0,0,0,0.2));
  padding: 20px;
}

.u-section-15 .u-over-slide-5 {
  background-image: linear-gradient(0deg, rgba(0,0,0,0.2), rgba(0,0,0,0.2));
  padding: 20px;
}

.u-section-15 .u-over-slide-6 {
  background-image: linear-gradient(0deg, rgba(0,0,0,0.2), rgba(0,0,0,0.2));
  padding: 20px;
}

@media (max-width: 1199px) {
   .u-section-15 {
    min-height: 540px;
  }

  .u-section-15 .u-gallery-1 {
    height: 500px;
    margin-bottom: -80px;
  }
}

@media (max-width: 991px) {
   .u-section-15 {
    min-height: 918px;
  }

  .u-section-15 .u-gallery-1 {
    height: 878px;
  }

  .u-section-15 .u-gallery-inner-1 {
    grid-template-columns: repeat(2, auto);
  }
}

@media (max-width: 767px) {
   .u-section-15 {
    min-height: 2471px;
  }

  .u-section-15 .u-gallery-1 {
    height: 2431px;
  }

  .u-section-15 .u-gallery-inner-1 {
    grid-template-columns: repeat(1, auto);
  }
}

@media (max-width: 575px) {
   .u-section-15 {
    min-height: 1571px;
  }

  .u-section-15 .u-gallery-1 {
    height: 1531px;
  }
} .u-section-16 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
  min-height: 380px;
}

.u-section-16 .u-layout-wrap-1 {
  width: 100%;
  margin: 20px 0 0;
}

.u-section-16 .u-image-1 {
  background-image: url("../images/sketch.webp");
  background-size: cover;
  min-height: 360px;
  background-position: 50% 50%;
  --radius: 10px;
}

.u-section-16 .u-container-layout-1 {
  padding: 30px;
}

.u-section-16 .u-layout-cell-2 {
  min-height: 360px;
}

.u-section-16 .u-container-layout-2 {
  padding: 30px 29px;
}

.u-section-16 .u-text-1 {
  font-weight: 400;
  margin: 0 auto 0 0;
}

.u-section-16 .u-text-2 {
  margin: 20px 20px 0 0;
}

@media (max-width: 1199px) {
   .u-section-16 {
    min-height: 370px;
  }

  .u-section-16 .u-layout-wrap-1 {
    position: relative;
  }
}

@media (max-width: 991px) {
   .u-section-16 {
    min-height: 293px;
  }

  .u-section-16 .u-layout-cell-2 {
    min-height: 100px;
  }
}

@media (max-width: 767px) {
   .u-section-16 {
    min-height: 398px;
  }

  .u-section-16 .u-image-1 {
    min-height: 720px;
  }

  .u-section-16 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-16 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
   .u-section-16 {
    min-height: 258px;
  }
} .u-section-17 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-17 .u-sheet-1 {
  min-height: 750px;
}

.u-section-17 .u-list-1 {
  margin-bottom: -60px;
  margin-top: 20px;
}

.u-section-17 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 710px;
  grid-gap: 10px;
}

.u-section-17 .u-container-layout-1 {
  padding: 10px 10px 9px;
}

.u-section-17 .u-image-1 {
  height: 235px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
}

.u-section-17 .u-text-1 {
  font-weight: 400;
  margin: 20px 0 0;
}

.u-section-17 .u-text-2 {
  margin: 20px 0 0;
}

.u-section-17 .u-icon-1 {
  width: 30px;
  height: 30px;
  margin: -29px 60px 0 auto;
  padding: 0;
}

.u-section-17 .u-container-layout-2 {
  padding: 10px 10px 9px;
}

.u-section-17 .u-image-2 {
  height: 235px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
}

.u-section-17 .u-text-3 {
  font-weight: 400;
  margin: 20px 0 0;
}

.u-section-17 .u-text-4 {
  margin: 20px 0 0;
}

.u-section-17 .u-icon-2 {
  width: 30px;
  height: 30px;
  margin: -29px 60px 0 auto;
  padding: 0;
}

.u-section-17 .u-container-layout-3 {
  padding: 10px 10px 9px;
}

.u-section-17 .u-image-3 {
  height: 235px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
}

.u-section-17 .u-text-5 {
  font-weight: 400;
  margin: 20px 0 0;
}

.u-section-17 .u-text-6 {
  margin: 20px 0 0;
}

.u-section-17 .u-icon-3 {
  width: 30px;
  height: 30px;
  margin: -29px 60px 0 auto;
  padding: 0;
}

.u-section-17 .u-container-layout-4 {
  padding: 10px 10px 9px;
}

.u-section-17 .u-image-4 {
  height: 235px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
}

.u-section-17 .u-text-7 {
  font-weight: 400;
  margin: 20px 0 0;
}

.u-section-17 .u-text-8 {
  margin: 20px 0 0;
}

.u-section-17 .u-icon-4 {
  width: 30px;
  height: 30px;
  margin: -29px 60px 0 auto;
  padding: 0;
}

.u-section-17 .u-container-layout-5 {
  padding: 10px 10px 9px;
}

.u-section-17 .u-image-5 {
  height: 235px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
}

.u-section-17 .u-text-9 {
  font-weight: 400;
  margin: 20px 0 0;
}

.u-section-17 .u-text-10 {
  margin: 20px 0 0;
}

.u-section-17 .u-icon-5 {
  width: 30px;
  height: 30px;
  margin: -29px 60px 0 auto;
  padding: 0;
}

.u-section-17 .u-container-layout-6 {
  padding: 10px 10px 9px;
}

.u-section-17 .u-image-6 {
  height: 235px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
}

.u-section-17 .u-text-11 {
  font-weight: 400;
  margin: 20px 0 0;
}

.u-section-17 .u-text-12 {
  margin: 20px 0 0;
}

.u-section-17 .u-icon-6 {
  width: 30px;
  height: 30px;
  margin: -29px 60px 0 auto;
  padding: 0;
}

@media (max-width: 1199px) {
  .u-section-17 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 591px;
  }

  .u-section-17 .u-image-1 {
    height: 194px;
  }

  .u-section-17 .u-image-2 {
    height: 194px;
  }

  .u-section-17 .u-image-3 {
    height: 194px;
  }

  .u-section-17 .u-image-4 {
    height: 194px;
  }

  .u-section-17 .u-image-5 {
    height: 194px;
  }

  .u-section-17 .u-image-6 {
    height: 194px;
  }
}

@media (max-width: 991px) {
  .u-section-17 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 1047px;
  }

  .u-section-17 .u-image-1 {
    height: 229px;
  }

  .u-section-17 .u-image-2 {
    height: 229px;
  }

  .u-section-17 .u-image-3 {
    height: 229px;
  }

  .u-section-17 .u-image-4 {
    height: 229px;
  }

  .u-section-17 .u-image-5 {
    height: 229px;
  }

  .u-section-17 .u-image-6 {
    height: 229px;
  }
}

@media (max-width: 767px) {
  .u-section-17 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-17 .u-image-1 {
    height: 322px;
  }

  .u-section-17 .u-image-2 {
    height: 322px;
  }

  .u-section-17 .u-image-3 {
    height: 322px;
  }

  .u-section-17 .u-image-4 {
    height: 322px;
  }

  .u-section-17 .u-image-5 {
    height: 322px;
  }

  .u-section-17 .u-image-6 {
    height: 322px;
  }
}

@media (max-width: 575px) {
  .u-section-17 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-17 .u-image-1 {
    height: 198px;
  }

  .u-section-17 .u-image-2 {
    height: 198px;
  }

  .u-section-17 .u-image-3 {
    height: 198px;
  }

  .u-section-17 .u-image-4 {
    height: 198px;
  }

  .u-section-17 .u-image-5 {
    height: 198px;
  }

  .u-section-17 .u-image-6 {
    height: 198px;
  }
} .u-section-18 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-18 .u-sheet-1 {
  min-height: 492px;
}

.u-section-18 .u-text-1 {
  margin: 20px 0 0;
}

.u-section-18 .u-icon-1 {
  font-size: 1.1112em;
}

.u-section-18 .u-btn-1 {
  border-style: solid;
  padding: 0;
}

.u-section-18 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: -382px;
  position: relative;
}

.u-section-18 .u-layout-cell-1 {
  min-height: 410px;
}

.u-section-18 .u-container-layout-1 {
  padding: 0;
}

.u-section-18 .u-text-2 {
  margin: 0 356px 0 0;
}

.u-section-18 .u-text-3 {
  margin: 20px 16px 0 0;
}

.u-section-18 .u-text-4 {
  font-weight: 500;
  margin: 60px 16px 0 0;
}

.u-section-18 .u-image-1 {
  min-height: 205px;
  background-image: url("../images/laxmi5.webp");
  --radius: 20px;
  background-position: 50% 50%;
}

.u-section-18 .u-container-layout-2 {
  padding: 0;
}

.u-section-18 .u-image-2 {
  min-height: 205px;
  background-image: url("../images/precon1.webp");
  --radius: 20px;
  background-position: 50% 50%;
}

.u-section-18 .u-container-layout-3 {
  padding: 30px;
}

.u-section-18 .u-image-3 {
  min-height: 205px;
  background-image: url("../images/laxmi1.webp");
  --radius: 20px;
  background-position: 50% 50%;
}

.u-section-18 .u-container-layout-4 {
  padding: 30px;
}

@media (max-width: 1199px) {
  .u-section-18 .u-sheet-1 {
    min-height: 868px;
  }

  .u-section-18 .u-layout-cell-1 {
    min-height: 342px;
  }

  .u-section-18 .u-text-2 {
    margin-right: 256px;
  }

  .u-section-18 .u-text-3 {
    margin-right: 0;
  }

  .u-section-18 .u-text-4 {
    margin-right: 0;
  }

  .u-section-18 .u-image-1 {
    min-height: 171px;
    --top-left-radius: 200px;
  }

  .u-section-18 .u-image-2 {
    min-height: 171px;
  }

  .u-section-18 .u-image-3 {
    min-height: 171px;
  }
}

@media (max-width: 991px) {
  .u-section-18 .u-sheet-1 {
    min-height: 1056px;
  }

  .u-section-18 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-18 .u-image-1 {
    min-height: 267px;
  }

  .u-section-18 .u-image-2 {
    min-height: 267px;
  }

  .u-section-18 .u-image-3 {
    min-height: 267px;
  }
}

@media (max-width: 767px) {
  .u-section-18 .u-sheet-1 {
    min-height: 1617px;
  }

  .u-section-18 .u-text-2 {
    margin-right: 16px;
  }

  .u-section-18 .u-image-1 {
    min-height: 370px;
  }

  .u-section-18 .u-image-2 {
    min-height: 370px;
  }

  .u-section-18 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-18 .u-image-3 {
    min-height: 370px;
  }

  .u-section-18 .u-container-layout-4 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-18 .u-sheet-1 {
    min-height: 1215px;
  }

  .u-section-18 .u-text-2 {
    margin-right: 0;
  }

  .u-section-18 .u-image-1 {
    min-height: 233px;
  }

  .u-section-18 .u-image-2 {
    min-height: 233px;
  }

  .u-section-18 .u-image-3 {
    min-height: 233px;
  }
} .u-section-19 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-19 .u-sheet-1 {
  min-height: 400px;
}

.u-section-19 .u-text-1 {
  font-size: 2.25rem;
  font-weight: 600;
  margin: 20px 0 0;
}

.u-section-19 .u-list-1 {
  margin-bottom: 20px;
  margin-top: 20px;
  width: 100%;
}

.u-section-19 .u-repeater-1 {
  grid-auto-columns: calc(50% - 5px);
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 600px;
  grid-gap: 10px;
}

.u-section-19 .u-list-item-1 {
  --radius: 20px;
}

.u-section-19 .u-container-layout-1 {
  padding: 30px;
}

.u-section-19 .u-text-2 {
  font-weight: 400;
  margin-bottom: 0;
  margin-top: 0;
}

.u-section-19 .u-text-3 {
  margin-left: 0;
  margin-right: auto;
  margin-bottom: 0;
  font-weight: 500;
}

.u-section-19 .u-text-4 {
  font-weight: 400;
  margin: 20px auto 0 0;
}

.u-section-19 .u-list-item-2 {
  --radius: 20px;
}

.u-section-19 .u-container-layout-2 {
  padding: 30px;
}

.u-section-19 .u-text-5 {
  font-weight: 400;
  margin-bottom: 0;
  margin-top: 0;
}

.u-section-19 .u-text-6 {
  margin-left: 0;
  margin-right: auto;
  margin-bottom: 0;
  font-weight: 500;
}

.u-section-19 .u-text-7 {
  font-weight: 400;
  margin: 20px auto 0 0;
}

.u-section-19 .u-list-item-3 {
  --radius: 20px;
}

.u-section-19 .u-container-layout-3 {
  padding: 30px;
}

.u-section-19 .u-text-8 {
  font-weight: 400;
  margin-bottom: 0;
  margin-top: 0;
}

.u-section-19 .u-text-9 {
  margin-left: 0;
  margin-right: auto;
  margin-bottom: 0;
  font-weight: 500;
}

.u-section-19 .u-text-10 {
  font-weight: 400;
  margin: 20px auto 0 0;
}

.u-section-19 .u-list-item-4 {
  --radius: 20px;
}

.u-section-19 .u-container-layout-4 {
  padding: 30px;
}

.u-section-19 .u-text-11 {
  font-weight: 400;
  margin-bottom: 0;
  margin-top: 0;
}

.u-section-19 .u-text-12 {
  margin-left: 0;
  margin-right: auto;
  margin-bottom: 0;
  font-weight: 500;
}

.u-section-19 .u-text-13 {
  font-weight: 400;
  margin: 20px auto 0 0;
}

@media (max-width: 991px) {
  .u-section-19 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 767px) {
  .u-section-19 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-19 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-19 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-19 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-19 .u-container-layout-4 {
    padding-left: 10px;
    padding-right: 10px;
  }
}.u-section-20 .u-sheet-1 {
  min-height: 525px;
}

.u-section-20 .u-group-1 {
  min-height: 475px;
  margin-top: 50px;
  margin-bottom: 0;
  height: auto;
}

.u-section-20 .u-container-layout-1 {
  padding: 30px;
}

.u-section-20 .u-image-1 {
  width: 540px;
  height: 415px;
  margin: 0 auto 0 0;
}

@media (max-width: 1199px) {
  .u-section-20 .u-group-1 {
    margin-top: 217px;
    height: auto;
  }

  .u-section-20 .u-image-1 {
    width: 440px;
    height: 338px;
  }
}

@media (max-width: 991px) {
  .u-section-20 .u-image-1 {
    width: 330px;
    height: 254px;
  }
}

@media (max-width: 767px) {
  .u-section-20 .u-group-1 {
    margin-top: 204px;
  }

  .u-section-20 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-20 .u-image-1 {
    width: 260px;
    height: 200px;
  }
}

@media (max-width: 575px) {
  .u-section-20 .u-group-1 {
    margin-top: 191px;
  }

  .u-section-20 .u-image-1 {
    width: 160px;
    height: 123px;
  }
}.u-section-21 .u-sheet-1 {
  min-height: 475px;
}

.u-section-21 .u-group-1 {
  min-height: 475px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(229, 229, 229, 0.5), rgba(229, 229, 229, 0.5));
  background-size: cover;
}

.u-section-21 .u-container-layout-1 {
  padding: 30px;
}

.u-section-21 .u-image-1 {
  width: 540px;
  height: 415px;
  margin: 0 0 0 auto;
}

@media (max-width: 1199px) {
  .u-section-21 .u-group-1 {
    margin-top: 217px;
    height: auto;
  }

  .u-section-21 .u-image-1 {
    width: 440px;
    height: 338px;
  }
}

@media (max-width: 991px) {
  .u-section-21 .u-image-1 {
    width: 330px;
    height: 254px;
  }
}

@media (max-width: 767px) {
  .u-section-21 .u-group-1 {
    margin-top: 204px;
  }

  .u-section-21 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-21 .u-image-1 {
    width: 260px;
    height: 200px;
  }
}

@media (max-width: 575px) {
  .u-section-21 .u-group-1 {
    margin-top: 190px;
  }

  .u-section-21 .u-image-1 {
    width: 160px;
    height: 123px;
  }
}.u-section-22 .u-sheet-1 {
  min-height: 475px;
}

.u-section-22 .u-group-1 {
  min-height: 475px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(229, 229, 229, 0.5), rgba(229, 229, 229, 0.5));
  background-size: cover;
}

.u-section-22 .u-container-layout-1 {
  padding: 30px;
}

.u-section-22 .u-image-1 {
  width: 540px;
  height: 415px;
  margin: 0 auto 0 0;
}

@media (max-width: 1199px) {
  .u-section-22 .u-group-1 {
    margin-top: 217px;
    height: auto;
  }

  .u-section-22 .u-image-1 {
    width: 440px;
    height: 338px;
  }
}

@media (max-width: 991px) {
  .u-section-22 .u-image-1 {
    width: 330px;
    height: 254px;
  }
}

@media (max-width: 767px) {
  .u-section-22 .u-group-1 {
    margin-top: 204px;
  }

  .u-section-22 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-22 .u-image-1 {
    width: 260px;
    height: 200px;
  }
}

@media (max-width: 575px) {
  .u-section-22 .u-group-1 {
    margin-top: 190px;
  }

  .u-section-22 .u-image-1 {
    width: 160px;
    height: 123px;
  }
}.u-section-23 .u-sheet-1 {
  min-height: 700px;
}

.u-section-23 .u-image-1 {
  width: 400px;
  height: 265px;
  --animation-custom_in-translate_x: 0px;
  --animation-custom_in-translate_y: 0px;
  --animation-custom_in-opacity: 0;
  --animation-custom_in-rotate: 0deg;
  --animation-custom_in-scale: 1;
  margin: 218px auto 0;
}

.u-section-23 .u-text-1 {
  margin: 30px auto 60px;
}

@media (max-width: 1199px) {
  .u-section-23 .u-text-1 {
    transition-duration: 0.5s;
  }
}

@media (max-width: 575px) {
  .u-section-23 .u-sheet-1 {
    min-height: 660px;
  }

  .u-section-23 .u-image-1 {
    width: 340px;
    height: 225px;
  }
} .u-section-24 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-24 .u-sheet-1 {
  min-height: 489px;
}

.u-section-24 .u-text-1 {
  font-weight: 300;
  margin: 20px 967px 0 30px;
}

.u-section-24 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: 20px;
}

.u-section-24 .u-layout-cell-1 {
  min-height: 400px;
}

.u-section-24 .u-container-layout-1 {
  padding: 30px;
}

.u-section-24 .u-text-2 {
  font-weight: 400;
  margin-bottom: 0;
  margin-top: 0;
  font-size: 2.25rem;
}

.u-section-24 .u-text-3 {
  margin: 20px 0 0;
}

.u-section-24 .u-btn-1 {
  margin: 20px 0 0;
  padding: 0;
}

.u-section-24 .u-layout-cell-2 {
  min-height: 400px;
}

.u-section-24 .u-container-layout-2 {
  padding: 0;
}

.u-section-24 .u-list-1 {
  margin-bottom: 0;
  margin-top: 0;
}

.u-section-24 .u-repeater-1 {
  grid-auto-columns: 355px 355px;
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 400px;
  grid-gap: 10px;
}

.u-section-24 .u-container-layout-3 {
  padding: 8px 10px;
}

.u-section-24 .u-image-1 {
  height: 232px;
  --radius: 5px;
  transition-duration: 0.5s;
  margin: 2px 0 0;
}

.u-section-24 .u-text-4 {
  font-weight: 400;
  margin: 30px 40px 0 0;
}

.u-section-24 .u-text-5 {
  margin: 21px auto 0 0;
}

.u-section-24 .u-icon-2 {
  width: 30px;
  height: 30px;
  margin: -30px 62px 0 auto;
  padding: 0;
}

.u-section-24 .u-container-layout-4 {
  padding: 8px 10px;
}

.u-section-24 .u-image-2 {
  height: 232px;
  --radius: 5px;
  transition-duration: 0.5s;
  margin: 2px 0 0;
}

.u-section-24 .u-text-6 {
  font-weight: 400;
  margin: 30px 40px 0 0;
}

.u-section-24 .u-text-7 {
  margin: 21px auto 0 0;
}

.u-section-24 .u-icon-3 {
  width: 30px;
  height: 30px;
  margin: -30px 62px 0 auto;
  padding: 0;
}

.u-section-24 .u-container-layout-5 {
  padding: 8px 10px;
}

.u-section-24 .u-image-3 {
  height: 232px;
  --radius: 5px;
  transition-duration: 0.5s;
  margin: 2px 0 0;
}

.u-section-24 .u-text-8 {
  font-weight: 400;
  margin: 30px 40px 0 0;
}

.u-section-24 .u-text-9 {
  margin: 21px auto 0 0;
}

.u-section-24 .u-icon-4 {
  width: 30px;
  height: 30px;
  margin: -30px 62px 0 auto;
  padding: 0;
}

.u-section-24 .u-gallery-nav-1 {
  position: absolute;
  left: 10px;
  width: 40px;
  height: 40px;
  top: 108px;
}

.u-section-24 .u-gallery-nav-2 {
  position: absolute;
  width: 40px;
  height: 40px;
  left: auto;
  top: 108px;
  right: 10px;
}

@media (max-width: 1199px) {
  .u-section-24 .u-sheet-1 {
    min-height: 422px;
  }

  .u-section-24 .u-text-1 {
    margin-right: 797px;
    margin-left: 0;
  }

  .u-section-24 .u-layout-wrap-1 {
    position: relative;
  }

  .u-section-24 .u-layout-cell-1 {
    min-height: 333px;
  }

  .u-section-24 .u-layout-cell-2 {
    min-height: 333px;
  }

  .u-section-24 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5px);
  }

  .u-section-24 .u-image-1 {
    height: 192px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-24 .u-text-4 {
    margin-right: 0;
  }

  .u-section-24 .u-image-2 {
    height: 192px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-24 .u-text-6 {
    margin-right: 0;
  }

  .u-section-24 .u-image-3 {
    height: 192px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-24 .u-text-8 {
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-24 .u-sheet-1 {
    min-height: 189px;
  }

  .u-section-24 .u-text-1 {
    margin-right: 577px;
  }

  .u-section-24 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-24 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-24 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-24 .u-image-1 {
    height: 148px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-24 .u-image-2 {
    height: 148px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-24 .u-image-3 {
    height: 148px;
    margin-right: initial;
    margin-left: initial;
  }
}

@media (max-width: 767px) {
  .u-section-24 .u-sheet-1 {
    min-height: 289px;
  }

  .u-section-24 .u-text-1 {
    margin-right: 337px;
  }

  .u-section-24 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-24 .u-list-1 {
    min-height: 383px;
  }

  .u-section-24 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-24 .u-image-1 {
    height: 158px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-24 .u-image-2 {
    height: 158px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-24 .u-image-3 {
    height: 158px;
    margin-right: initial;
    margin-left: initial;
  }
}

@media (max-width: 575px) {
  .u-section-24 .u-text-1 {
    margin-right: 137px;
  }

  .u-section-24 .u-image-1 {
    height: 97px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-24 .u-image-2 {
    height: 97px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-24 .u-image-3 {
    height: 97px;
    margin-right: initial;
    margin-left: initial;
  }
}

.u-section-24 .u-image-1,
.u-section-24 .u-image-1:before,
.u-section-24 .u-image-1 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-24 .u-image-1.u-image-1.u-image-1:hover {
  border-radius: 30px !important;
}

.u-section-24 .u-image-1.u-image-1.u-image-1.hover {
  border-radius: 30px !important;
}

.u-section-24 .u-image-2,
.u-section-24 .u-image-2:before,
.u-section-24 .u-image-2 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-24 .u-image-2.u-image-2.u-image-2:hover {
  border-radius: 30px !important;
}

.u-section-24 .u-image-2.u-image-2.u-image-2.hover {
  border-radius: 30px !important;
}

.u-section-24 .u-image-3,
.u-section-24 .u-image-3:before,
.u-section-24 .u-image-3 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-24 .u-image-3.u-image-3.u-image-3:hover {
  border-radius: 30px !important;
}

.u-section-24 .u-image-3.u-image-3.u-image-3.hover {
  border-radius: 30px !important;
}