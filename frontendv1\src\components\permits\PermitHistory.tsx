import React, { useState, useEffect } from "react";
import {
	Search,
	Calendar,
	Download,
	Eye,
} from "lucide-react";
import { Permit, PermitFilters } from "../../types/permits";
import PermitStatusBadge from "./shared/PermitStatusBadge";

interface PermitHistoryProps {
	siteId: string;
}

// Mock historical permits data
const mockHistoricalPermits: Permit[] = [
	{
		id: "permit-h1",
		permitNumber: "HW-2024-003",
		permitType: {
			id: "hot-work",
			name: "Hot Work Permit",
			description: "For welding, cutting, and other hot work activities",
			category: "High Risk",
			defaultValidityHours: 8,
			requiredTrainings: ["hot-work-safety"],
			requiredCertifications: ["welding-cert"],
			riskLevel: "high",
			template: {} as any,
			isActive: true,
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		title: "Pipe Welding - Basement Level",
		description: "Welding operations for plumbing installation",
		location: "Zone C - Basement",
		siteId: "site-1",
		requestedDate: new Date("2024-01-14T08:00:00"),
		validFrom: new Date("2024-01-14T09:00:00"),
		validUntil: new Date("2024-01-14T17:00:00"),
		actualStartTime: new Date("2024-01-14T09:15:00"),
		actualEndTime: new Date("2024-01-14T16:45:00"),
		status: "closed",
		priority: "medium",
		requestedBy: "supervisor-1",
		requestedByName: "John Smith",
		assignedWorkers: [
			{
				workerId: "worker-4",
				workerName: "Tom Anderson",
				primaryTrade: "Plumber",
				role: "worker",
				hasRequiredTraining: true,
				hasRequiredCertifications: true,
				acknowledgedAt: new Date("2024-01-14T08:45:00"),
			},
		],
		approvals: [],
		riskAssessment: {} as any,
		dailyRiskAssessments: [],
		formData: {},
		attachments: [],
		createdAt: new Date("2024-01-14T07:30:00"),
		updatedAt: new Date("2024-01-14T16:45:00"),
		history: [],
	},
	{
		id: "permit-h2",
		permitNumber: "WH-2024-001",
		permitType: {
			id: "work-at-height",
			name: "Work at Height Permit",
			description: "For work above 2 meters",
			category: "High Risk",
			defaultValidityHours: 8,
			requiredTrainings: ["work-at-height"],
			requiredCertifications: ["harness-cert"],
			riskLevel: "high",
			template: {} as any,
			isActive: true,
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		title: "Roof Installation - Building A",
		description: "Installation of roofing materials on Building A",
		location: "Zone A - Roof Level",
		siteId: "site-1",
		requestedDate: new Date("2024-01-13T08:00:00"),
		validFrom: new Date("2024-01-13T09:00:00"),
		validUntil: new Date("2024-01-13T17:00:00"),
		actualStartTime: new Date("2024-01-13T09:00:00"),
		actualEndTime: new Date("2024-01-13T17:00:00"),
		status: "closed",
		priority: "high",
		requestedBy: "supervisor-3",
		requestedByName: "Mark Davis",
		assignedWorkers: [
			{
				workerId: "worker-5",
				workerName: "Chris Wilson",
				primaryTrade: "Roofer",
				role: "supervisor",
				hasRequiredTraining: true,
				hasRequiredCertifications: true,
				acknowledgedAt: new Date("2024-01-13T08:30:00"),
			},
			{
				workerId: "worker-6",
				workerName: "Alex Johnson",
				primaryTrade: "Roofer",
				role: "worker",
				hasRequiredTraining: true,
				hasRequiredCertifications: true,
				acknowledgedAt: new Date("2024-01-13T08:30:00"),
			},
		],
		approvals: [],
		riskAssessment: {} as any,
		dailyRiskAssessments: [],
		formData: {},
		attachments: [],
		createdAt: new Date("2024-01-13T07:00:00"),
		updatedAt: new Date("2024-01-13T17:00:00"),
		history: [],
	},
	{
		id: "permit-h3",
		permitNumber: "GW-2024-005",
		permitType: {
			id: "general-work",
			name: "General Work Permit",
			description: "For general construction activities",
			category: "Standard",
			defaultValidityHours: 8,
			requiredTrainings: ["general-safety"],
			requiredCertifications: [],
			riskLevel: "medium",
			template: {} as any,
			isActive: true,
			createdAt: new Date(),
			updatedAt: new Date(),
		},
		title: "Electrical Installation - Floor 2",
		description: "Installation of electrical wiring and fixtures",
		location: "Zone B - Floor 2",
		siteId: "site-1",
		requestedDate: new Date("2024-01-12T08:00:00"),
		status: "cancelled",
		priority: "low",
		requestedBy: "supervisor-2",
		requestedByName: "Sarah Johnson",
		assignedWorkers: [],
		approvals: [],
		riskAssessment: {} as any,
		dailyRiskAssessments: [],
		formData: {},
		attachments: [],
		createdAt: new Date("2024-01-12T07:30:00"),
		updatedAt: new Date("2024-01-12T10:00:00"),
		history: [],
	},
];

const PermitHistory: React.FC<PermitHistoryProps> = ({ siteId }) => {
	const [permits, _setPermits] = useState<Permit[]>(mockHistoricalPermits);
	const [filteredPermits, setFilteredPermits] = useState<Permit[]>(
		mockHistoricalPermits,
	);
	const [filters, setFilters] = useState<PermitFilters>({
		search: "",
		status: "all",
		permitType: "",
		priority: "all",
		assignedWorker: "",
		dateRange: {},
		location: "",
	});
	const [dateRange, setDateRange] = useState({
		start: "",
		end: "",
	});

	useEffect(() => {
		// Fetch historical permits for the site
		console.log(`Fetching permit history for site ${siteId}`);
	}, [siteId]);

	useEffect(() => {
		// Apply filters
		let filtered = permits;

		if (filters.search) {
			filtered = filtered.filter(
				(permit) =>
					permit.title.toLowerCase().includes(filters.search.toLowerCase()) ||
					permit.permitNumber
						.toLowerCase()
						.includes(filters.search.toLowerCase()) ||
					permit.description
						.toLowerCase()
						.includes(filters.search.toLowerCase()),
			);
		}

		if (filters.status !== "all") {
			filtered = filtered.filter((permit) => permit.status === filters.status);
		}

		if (filters.priority !== "all") {
			filtered = filtered.filter(
				(permit) => permit.priority === filters.priority,
			);
		}

		if (filters.location) {
			filtered = filtered.filter((permit) =>
				permit.location.toLowerCase().includes(filters.location.toLowerCase()),
			);
		}

		if (dateRange.start) {
			filtered = filtered.filter(
				(permit) => permit.requestedDate >= new Date(dateRange.start),
			);
		}

		setFilteredPermits(filtered);
	}, [permits, filters, dateRange]);

	const formatDuration = (start?: Date, end?: Date) => {
		if (!start || !end) return "N/A";
		const diff = end.getTime() - start.getTime();
		const hours = Math.floor(diff / (1000 * 60 * 60));
		const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
		return `${hours}h ${minutes}m`;
	};

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="flex justify-between items-center">
				<h2 className="text-2xl font-bold text-gray-900">Permit History</h2>
				<button className="flex items-center px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
					<Download className="h-4 w-4 mr-2" />
					Export History
				</button>
			</div>

			{/* Filters */}
			<div className="bg-white rounded-lg border border-gray-200 p-4">
				<div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
					<div className="relative">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
						<input
							type="text"
							placeholder="Search permits..."
							value={filters.search}
							onChange={(e) =>
								setFilters({ ...filters, search: e.target.value })
							}
							className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
						/>
					</div>

					<select
						value={filters.status}
						onChange={(e) =>
							setFilters({ ...filters, status: e.target.value as any })
						}
						className="px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
					>
						<option value="all">All Statuses</option>
						<option value="closed">Closed</option>
						<option value="cancelled">Cancelled</option>
						<option value="expired">Expired</option>
					</select>

					<select
						value={filters.priority}
						onChange={(e) =>
							setFilters({ ...filters, priority: e.target.value as any })
						}
						className="px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
					>
						<option value="all">All Priorities</option>
						<option value="critical">Critical</option>
						<option value="high">High</option>
						<option value="medium">Medium</option>
						<option value="low">Low</option>
					</select>

					<input
						type="date"
						placeholder="Start date"
						value={dateRange.start}
						onChange={(e) =>
							setDateRange({ ...dateRange, start: e.target.value })
						}
						className="px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
					/>

					<input
						type="text"
						placeholder="Location..."
						value={filters.location}
						onChange={(e) =>
							setFilters({ ...filters, location: e.target.value })
						}
						className="px-3 py-2 border border-gray-300 rounded-md focus:ring-green-500 focus:border-green-500"
					/>
				</div>
			</div>

			{/* Permits Table */}
			<div className="bg-white rounded-lg border border-gray-200 shadow-sm overflow-hidden">
				<table className="min-w-full divide-y divide-gray-200">
					<thead className="bg-gray-50">
						<tr>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Permit Details
							</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Status
							</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Date & Duration
							</th>
							<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
								Workers
							</th>
							<th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
								Actions
							</th>
						</tr>
					</thead>
					<tbody className="bg-white divide-y divide-gray-200">
						{filteredPermits.map((permit) => (
							<tr key={permit.id} className="hover:bg-gray-50">
								<td className="px-6 py-4">
									<div>
										<h4 className="text-sm font-medium text-gray-900">
											{permit.permitType.name}
										</h4>
										<p className="text-xs text-gray-400 mt-1">
											{permit.permitNumber} • {permit.location}
										</p>
									</div>
								</td>
								<td className="px-6 py-4">
									<PermitStatusBadge status={permit.status} size="sm" />
								</td>
								<td className="px-6 py-4 text-sm text-gray-500">
									<div>
										<p>{permit.requestedDate.toLocaleDateString()}</p>
										<p className="text-xs text-gray-400">
											Duration:{" "}
											{formatDuration(
												permit.actualStartTime,
												permit.actualEndTime,
											)}
										</p>
									</div>
								</td>
								<td className="px-6 py-4 text-sm text-gray-500">
									{permit.assignedWorkers.length} worker(s)
								</td>
								<td className="px-6 py-4 text-right text-sm font-medium">
									<button className="text-green-600 hover:text-green-900">
										<Eye className="h-4 w-4" />
									</button>
								</td>
							</tr>
						))}
					</tbody>
				</table>

				{filteredPermits.length === 0 && (
					<div className="p-12 text-center">
						<Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
						<h3 className="text-lg font-medium text-gray-900 mb-2">
							No permits found
						</h3>
						<p className="text-gray-500">
							{filters.search ||
							filters.status !== "all" ||
							filters.priority !== "all" ||
							filters.location ||
							dateRange.start
								? "Try adjusting your filters to see more results."
								: "No historical permits available for this site."}
						</p>
					</div>
				)}
			</div>
		</div>
	);
};

export default PermitHistory;
