import { StrictMode } from "react";
import { createRoot } from "react-dom/client";
import { ApolloClient, InMemoryCache, ApolloProvider } from "@apollo/client";
import { ToastContainer } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";
import App from "./App.tsx";
import "./index.css";

const uri = import.meta.env.VITE_GRAPHQL_URI_1;
if (!uri) throw new Error("GraphQL endpoint is not defined.");

const client = new ApolloClient({
	uri: uri,
	cache: new InMemoryCache(),
});

createRoot(document.getElementById("root")!).render(
	<StrictMode>
		<ApolloProvider client={client}>
			<App />
			<ToastContainer position="top-right" autoClose={3000} />
		</ApolloProvider>
	</StrictMode>,
);
