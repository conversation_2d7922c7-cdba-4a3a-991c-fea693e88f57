using System.Text.Json.Serialization;

namespace Shared.DTOs
{
    public class SiteDataDto
    {
        [JsonPropertyName("projectDetails")]
        public ProjectDetailsDto? ProjectDetails { get; set; }

        [JsonPropertyName("siteSpecification")]
        public SiteSpecificationDto? SiteSpecification { get; set; }

        [JsonPropertyName("regulatoryCompliance")]
        public RegulatoryComplianceDto? RegulatoryCompliance { get; set; }

        [JsonPropertyName("siteCommittee")]
        public SiteCommitteeDto? SiteCommittee { get; set; }

        [JsonPropertyName("emergencyContacts")]
        public EmergencyContactsDto? EmergencyContacts { get; set; }

        [JsonPropertyName("projectTimeline")]
        public ProjectTimelineDto? ProjectTimeline { get; set; }
    }

    public class ProjectDetailsDto
    {
        [JsonPropertyName("name")]
        public string? Name { get; set; }

        [JsonPropertyName("cost")]
        public string? Cost { get; set; }

        [Json<PERSON>ropertyName("mainContractor")]
        public string? MainContractor { get; set; }

        [JsonPropertyName("subcontractors")]
        public List<string>? Subcontractors { get; set; }

        [JsonPropertyName("keyPersonel")]
        public KeyPersonelDto? KeyPersonel { get; set; }

        [JsonPropertyName("contractType")]
        public string? ContractType { get; set; }

        [JsonPropertyName("projectType")]
        public List<string>? ProjectType { get; set; }
    }

    public class KeyPersonelDto
    {
        [JsonPropertyName("architect")]
        public string? Architect { get; set; }

        [JsonPropertyName("engineer")]
        public string? Engineer { get; set; }
    }

    public class SiteSpecificationDto
    {
        [JsonPropertyName("siteLocation")]
        public SiteLocationDto? SiteLocation { get; set; }

        [JsonPropertyName("buildingStats")]
        public BuildingStatsDto? BuildingStats { get; set; }

        [JsonPropertyName("buildingFootprint")]
        public BuildingFootprintDto? BuildingFootprint { get; set; }

        [JsonPropertyName("utilitiesServices")]
        public UtilitiesServicesDto? UtilitiesServices { get; set; }

        [JsonPropertyName("accessRoads")]
        public AccessRoadsDto? AccessRoads { get; set; }
    }

    public class SiteLocationDto
    {
        [JsonPropertyName("totalArea")]
        public string? TotalArea { get; set; }

        [JsonPropertyName("locationMap")]
        public string? LocationMap { get; set; }
    }

    public class BuildingStatsDto
    {
        [JsonPropertyName("floors")]
        public string? Floors { get; set; }

        [JsonPropertyName("basement")]
        public string? Basement { get; set; }

        [JsonPropertyName("parking")]
        public string? Parking { get; set; }
    }

    public class BuildingFootprintDto
    {
        [JsonPropertyName("buildingArea")]
        public string? BuildingArea { get; set; }

        [JsonPropertyName("builtArea")]
        public string? BuiltArea { get; set; }
    }

    public class UtilitiesServicesDto
    {
        [JsonPropertyName("water")]
        public string? Water { get; set; }

        [JsonPropertyName("electricity")]
        public string? Electricity { get; set; }

        [JsonPropertyName("sewer")]
        public string? Sewer { get; set; }

        [JsonPropertyName("internet")]
        public string? Internet { get; set; }
    }

    public class AccessRoadsDto
    {
        [JsonPropertyName("mainAccessRoads")]
        public List<string>? MainAccessRoads { get; set; }

        [JsonPropertyName("secondaryAccessRoads")]
        public List<string>? SecondaryAccessRoads { get; set; }
    }

    public class RegulatoryComplianceDto
    {
        [JsonPropertyName("buildingPermit")]
        public BuildingPermitDto? BuildingPermit { get; set; }

        [JsonPropertyName("classification")]
        public ClassificationDto? Classification { get; set; }

        [JsonPropertyName("fireSafetyRating")]
        public string? FireSafetyRating { get; set; }

        [JsonPropertyName("complianceStandard")]
        public ComplianceStandardDto? ComplianceStandard { get; set; }

        [JsonPropertyName("occupancyType")]
        public string? OccupancyType { get; set; }
    }

    public class BuildingPermitDto
    {
        [JsonPropertyName("permitNumber")]
        public string? PermitNumber { get; set; }

        [JsonPropertyName("permitSpecification")]
        public string? PermitSpecification { get; set; }

        [JsonPropertyName("permitType")]
        public string? PermitType { get; set; }
    }

    public class ClassificationDto
    {
        [JsonPropertyName("buildingClass")]
        public string? BuildingClass { get; set; }

        [JsonPropertyName("constructionType")]
        public string? ConstructionType { get; set; }
    }

    public class ComplianceStandardDto
    {
        [JsonPropertyName("accesibility")]
        public string? Accessibility { get; set; }

        [JsonPropertyName("environmental")]
        public string? Environmental { get; set; }
    }

    public class SiteCommitteeDto
    {
        [JsonPropertyName("committeeMembers")]
        public List<string>? CommitteeMembers { get; set; }
    }

    public class EmergencyContactsDto
    {
        [JsonPropertyName("police")]
        public List<string>? Police { get; set; }

        [JsonPropertyName("fireDepartment")]
        public List<string>? FireDepartment { get; set; }

        [JsonPropertyName("medicalServices")]
        public List<string>? MedicalServices { get; set; }

        [JsonPropertyName("emergencyManagement")]
        public List<string>? EmergencyManagement { get; set; }
    }

    public class ProjectTimelineDto
    {
        [JsonPropertyName("startDate")]
        public string? StartDate { get; set; }

        [JsonPropertyName("endDate")]
        public string? EndDate { get; set; }

        [JsonPropertyName("milestones")]
        public List<string>? Milestones { get; set; }
    }
}
