using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;

namespace GraphQLApi.Services
{
    public class RelationshipService : IRelationshipService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;
        private readonly ILogger<RelationshipService> _logger;

        public RelationshipService(
            IDbContextFactory<AppDbContext> contextFactory,
            ILogger<RelationshipService> logger)
        {
            _contextFactory = contextFactory;
            _logger = logger;
        }

        public async System.Threading.Tasks.Task AssignTrainingsToWorkerAsync(int workerId, List<int> trainingIds)
        {
            if (!trainingIds.Any()) return;

            await using var context = await _contextFactory.CreateDbContextAsync();
            
            var worker = await context.Workers
                .Include(w => w.Trainings)
                .FirstOrDefaultAsync(w => w.Id == workerId);
                
            if (worker == null) return;

            // Batch load trainings to avoid N+1
            var trainings = await context.Trainings
                .Where(t => trainingIds.Contains(t.Id))
                .ToListAsync();

            foreach (var training in trainings)
            {
                if (!worker.Trainings.Contains(training))
                {
                    worker.Trainings.Add(training);
                }
            }

            await context.SaveChangesAsync();
        }

        public async System.Threading.Tasks.Task AssignTradesToWorkerAsync(int workerId, List<int> tradeIds)
        {
            if (!tradeIds.Any()) return;

            await using var context = await _contextFactory.CreateDbContextAsync();
            
            var worker = await context.Workers
                .Include(w => w.Trades)
                .FirstOrDefaultAsync(w => w.Id == workerId);
                
            if (worker == null) return;

            // Batch load trades to avoid N+1
            var trades = await context.Trades
                .Where(t => tradeIds.Contains(t.Id))
                .ToListAsync();

            foreach (var trade in trades)
            {
                if (!worker.Trades.Contains(trade))
                {
                    worker.Trades.Add(trade);
                }
            }

            await context.SaveChangesAsync();
        }

        public async System.Threading.Tasks.Task AssignSkillsToWorkerAsync(int workerId, List<int> skillIds)
        {
            if (!skillIds.Any()) return;

            await using var context = await _contextFactory.CreateDbContextAsync();

            var worker = await context.Workers
                .Include(w => w.Skills)
                .FirstOrDefaultAsync(w => w.Id == workerId);

            if (worker == null) return;

            // Batch load skills to avoid N+1
            var skills = await context.Skills
                .Where(s => skillIds.Contains(s.Id))
                .ToListAsync();

            foreach (var skill in skills)
            {
                if (!worker.Skills.Contains(skill))
                {
                    worker.Skills.Add(skill);
                }
            }

            await context.SaveChangesAsync();
        }

        public async System.Threading.Tasks.Task AssignWorkersToTrainingAsync(int trainingId, List<int> workerIds)
        {
            if (!workerIds.Any()) return;

            await using var context = await _contextFactory.CreateDbContextAsync();

            var training = await context.Trainings
                .Include(t => t.Workers)
                .FirstOrDefaultAsync(t => t.Id == trainingId);

            if (training == null) return;

            // Batch load workers to avoid N+1
            var workers = await context.Workers
                .Where(w => workerIds.Contains(w.Id))
                .ToListAsync();

            foreach (var worker in workers)
            {
                if (!training.Workers.Contains(worker))
                {
                    training.Workers.Add(worker);
                }
            }

            await context.SaveChangesAsync();
        }

        public async System.Threading.Tasks.Task AssignWorkersToTradeAsync(int tradeId, List<int> workerIds)
        {
            if (!workerIds.Any()) return;

            await using var context = await _contextFactory.CreateDbContextAsync();

            var trade = await context.Trades
                .Include(t => t.Workers)
                .FirstOrDefaultAsync(t => t.Id == tradeId);

            if (trade == null) return;

            // Batch load workers to avoid N+1
            var workers = await context.Workers
                .Where(w => workerIds.Contains(w.Id))
                .ToListAsync();

            foreach (var worker in workers)
            {
                if (!trade.Workers.Contains(worker))
                {
                    trade.Workers.Add(worker);
                }
            }

            await context.SaveChangesAsync();
        }

        public async System.Threading.Tasks.Task AssignWorkersToSkillAsync(int skillId, List<int> workerIds)
        {
            if (!workerIds.Any()) return;

            await using var context = await _contextFactory.CreateDbContextAsync();

            var skill = await context.Skills
                .Include(s => s.Workers)
                .FirstOrDefaultAsync(s => s.Id == skillId);

            if (skill == null) return;

            // Batch load workers to avoid N+1
            var workers = await context.Workers
                .Where(w => workerIds.Contains(w.Id))
                .ToListAsync();

            foreach (var worker in workers)
            {
                if (!skill.Workers.Contains(worker))
                {
                    skill.Workers.Add(worker);
                }
            }

            await context.SaveChangesAsync();
        }

        public async System.Threading.Tasks.Task AssignWorkersToTaskAsync(int taskId, List<int> workerIds)
        {
            if (!workerIds.Any()) return;

            await using var context = await _contextFactory.CreateDbContextAsync();
            
            var task = await context.Tasks
                .Include(t => t.WorkersAssigned)
                .FirstOrDefaultAsync(t => t.Id == taskId);
                
            if (task == null) return;

            // Batch load workers to avoid N+1
            var workers = await context.Workers
                .Where(w => workerIds.Contains(w.Id))
                .ToListAsync();

            foreach (var worker in workers)
            {
                if (!task.WorkersAssigned.Contains(worker))
                {
                    task.WorkersAssigned.Add(worker);
                }
            }

            await context.SaveChangesAsync();
        }

        public async System.Threading.Tasks.Task AssignEquipmentToTaskAsync(int taskId, List<int> equipmentIds)
        {
            if (!equipmentIds.Any()) return;

            await using var context = await _contextFactory.CreateDbContextAsync();

            var task = await context.Tasks
                .Include(t => t.EquipmentInvolved)
                .FirstOrDefaultAsync(t => t.Id == taskId);

            if (task == null) return;

            // Batch load equipment to avoid N+1
            var equipment = await context.Equipment
                .Where(e => equipmentIds.Contains(e.Id))
                .ToListAsync();

            foreach (var equip in equipment)
            {
                if (!task.EquipmentInvolved.Contains(equip))
                {
                    task.EquipmentInvolved.Add(equip);
                }
            }

            await context.SaveChangesAsync();
        }

        public async System.Threading.Tasks.Task UpdateWorkerRelationshipsAsync(int workerId, List<int>? trainingIds, List<int>? tradeIds, List<int>? skillIds)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var worker = await context.Workers
                .Include(w => w.Trainings)
                .Include(w => w.Trades)
                .Include(w => w.Skills)
                .FirstOrDefaultAsync(w => w.Id == workerId);

            if (worker == null) return;

            // Update trainings if specified
            if (trainingIds != null)
            {
                worker.Trainings.Clear();
                if (trainingIds.Any())
                {
                    var trainings = await context.Trainings
                        .Where(t => trainingIds.Contains(t.Id))
                        .ToListAsync();
                    foreach (var training in trainings)
                    {
                        worker.Trainings.Add(training);
                    }
                }
            }

            // Update trades if specified
            if (tradeIds != null)
            {
                worker.Trades.Clear();
                if (tradeIds.Any())
                {
                    var trades = await context.Trades
                        .Where(t => tradeIds.Contains(t.Id))
                        .ToListAsync();
                    foreach (var trade in trades)
                    {
                        worker.Trades.Add(trade);
                    }
                }
            }

            // Update skills if specified
            if (skillIds != null)
            {
                worker.Skills.Clear();
                if (skillIds.Any())
                {
                    var skills = await context.Skills
                        .Where(s => skillIds.Contains(s.Id))
                        .ToListAsync();
                    foreach (var skill in skills)
                    {
                        worker.Skills.Add(skill);
                    }
                }
            }

            await context.SaveChangesAsync();
        }

        public async System.Threading.Tasks.Task UpdateTrainingRelationshipsAsync(int trainingId, List<int>? workerIds)
        {
            if (workerIds == null) return;

            await using var context = await _contextFactory.CreateDbContextAsync();

            var training = await context.Trainings
                .Include(t => t.Workers)
                .FirstOrDefaultAsync(t => t.Id == trainingId);

            if (training == null) return;

            training.Workers.Clear();
            if (workerIds.Any())
            {
                var workers = await context.Workers
                    .Where(w => workerIds.Contains(w.Id))
                    .ToListAsync();
                foreach (var worker in workers)
                {
                    training.Workers.Add(worker);
                }
            }

            await context.SaveChangesAsync();
        }

        public async System.Threading.Tasks.Task UpdateTradeRelationshipsAsync(int tradeId, List<int>? workerIds)
        {
            if (workerIds == null) return;

            await using var context = await _contextFactory.CreateDbContextAsync();

            var trade = await context.Trades
                .Include(t => t.Workers)
                .FirstOrDefaultAsync(t => t.Id == tradeId);

            if (trade == null) return;

            trade.Workers.Clear();
            if (workerIds.Any())
            {
                var workers = await context.Workers
                    .Where(w => workerIds.Contains(w.Id))
                    .ToListAsync();
                foreach (var worker in workers)
                {
                    trade.Workers.Add(worker);
                }
            }

            await context.SaveChangesAsync();
        }

        public async System.Threading.Tasks.Task UpdateSkillRelationshipsAsync(int skillId, List<int>? workerIds)
        {
            if (workerIds == null) return;

            await using var context = await _contextFactory.CreateDbContextAsync();

            var skill = await context.Skills
                .Include(s => s.Workers)
                .FirstOrDefaultAsync(s => s.Id == skillId);

            if (skill == null) return;

            skill.Workers.Clear();
            if (workerIds.Any())
            {
                var workers = await context.Workers
                    .Where(w => workerIds.Contains(w.Id))
                    .ToListAsync();
                foreach (var worker in workers)
                {
                    skill.Workers.Add(worker);
                }
            }

            await context.SaveChangesAsync();
        }

        public async System.Threading.Tasks.Task UpdateTaskRelationshipsAsync(int taskId, List<int>? workerIds, List<int>? equipmentIds)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();

            var task = await context.Tasks
                .Include(t => t.WorkersAssigned)
                .Include(t => t.EquipmentInvolved)
                .FirstOrDefaultAsync(t => t.Id == taskId);

            if (task == null) return;

            // Update workers if specified
            if (workerIds != null)
            {
                task.WorkersAssigned.Clear();
                if (workerIds.Any())
                {
                    var workers = await context.Workers
                        .Where(w => workerIds.Contains(w.Id))
                        .ToListAsync();
                    foreach (var worker in workers)
                    {
                        task.WorkersAssigned.Add(worker);
                    }
                }
            }

            // Update equipment if specified
            if (equipmentIds != null)
            {
                task.EquipmentInvolved.Clear();
                if (equipmentIds.Any())
                {
                    var equipment = await context.Equipment
                        .Where(e => equipmentIds.Contains(e.Id))
                        .ToListAsync();
                    foreach (var equip in equipment)
                    {
                        task.EquipmentInvolved.Add(equip);
                    }
                }
            }

            await context.SaveChangesAsync();
        }
    }
}
