@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
	font-family: "Inter", system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI",
		<PERSON><PERSON>, "Helvetica Neue", Arial, sans-serif;
	-webkit-font-smoothing: antialiased;
	-moz-osx-font-smoothing: grayscale;
}

body {
	@apply bg-[#f3f2ee] text-gray-800;
	margin: 0;
	padding: 0;
}

/* Custom scrollbar */
::-webkit-scrollbar {
	width: 8px;
	height: 8px;
}

::-webkit-scrollbar-track {
	background: transparent;
}

::-webkit-scrollbar-thumb {
	background-color: rgba(156, 163, 175, 0.5);
	border-radius: 20px;
}

::-webkit-scrollbar-thumb:hover {
	background-color: rgba(156, 163, 175, 0.7);
}

/* Hide scrollbar utility class */
.scrollbar-hide {
	-ms-overflow-style: none; /* Internet Explorer 10+ */
	scrollbar-width: none; /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
	display: none; /* Safari and Chrome */
}

/* Flyout menu animations and styling */
.flyout-menu {
	/* Ensure flyout appears as seamless extension */
	border-left: 1px solid rgba(229, 231, 235, 0.3);
}

/* Flyout menu item hover styling */
.flyout-menu-item:hover:not(.active) {
	background-color: #fdfdf9 !important;
	color: #22c55e !important; /* green-500 */
}

.flyout-enter {
	transform: translateX(-100%);
	opacity: 0;
}

.flyout-enter-active {
	transform: translateX(0);
	opacity: 1;
	transition: transform 300ms ease-out, opacity 300ms ease-out;
}

.flyout-exit {
	transform: translateX(0);
	opacity: 1;
}

.flyout-exit-active {
	transform: translateX(-100%);
	opacity: 0;
	transition: transform 250ms ease-in, opacity 250ms ease-in;
}
