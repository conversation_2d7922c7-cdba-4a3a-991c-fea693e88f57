using Shared.Interfaces;

namespace Shared.GraphQL.Models
{
    /// <summary>
    /// Entity representing a document file with a custom name
    /// </summary>
    public class DocumentFile : IAuditableEntity, ISoftDeletable
    {
        /// <summary>
        /// Unique identifier for the document file record
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Custom name/title for the document as provided by the user
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// Foreign key to the FileMetadata record
        /// </summary>
        public int FileMetadataId { get; set; }

        /// <summary>
        /// Navigation property to the FileMetadata
        /// </summary>
        public virtual FileMetadata FileMetadata { get; set; } = null!;

        /// <summary>
        /// URL to access the file through the file controller
        /// </summary>
        public string Url => FileMetadata?.Url ?? string.Empty;

        // IAuditableEntity implementation
        public DateTime CreatedAt { get; set; }
        public string CreatedBy { get; set; } = string.Empty;
        public DateTime? UpdatedAt { get; set; }
        public string UpdatedBy { get; set; } = string.Empty;

        // ISoftDeletable implementation
        public bool IsDeleted { get; set; }
        public DateTime? DeletedAt { get; set; }
        public string? DeletedBy { get; set; }
    }
}
