using Microsoft.AspNetCore.Mvc.Testing;
using Microsoft.Extensions.DependencyInjection;
using System.Text;
using Xunit;
using GraphQLApi.Services;
using Shared.Constants;

namespace GraphQLApi.Tests.Integration
{
    /// <summary>
    /// Integration tests for MinIO service
    /// These tests require a running MinIO instance
    /// </summary>
    public class MinIOIntegrationTests : IClassFixture<WebApplicationFactory<Program>>
    {
        private readonly WebApplicationFactory<Program> _factory;
        private readonly IMinioService _minioService;

        public MinIOIntegrationTests(WebApplicationFactory<Program> factory)
        {
            _factory = factory;
            
            // Get the MinIO service from DI container
            using var scope = _factory.Services.CreateScope();
            _minioService = scope.ServiceProvider.GetRequiredService<IMinioService>();
        }

        [Fact]
        [Trait("Category", "Integration")]
        public async Task UploadDownloadDeleteWorkflow_ShouldWork()
        {
            // Skip if MinIO is not available
            if (!await IsMinIOAvailable())
            {
                return; // Skip test if MinIO is not running
            }

            // Arrange
            var fileName = $"test-{Guid.NewGuid()}.txt";
            var content = "This is a test file for MinIO integration testing.";
            var contentBytes = Encoding.UTF8.GetBytes(content);
            var bucketName = FileStorageConstants.BucketNames.TEMP;

            using var uploadStream = new MemoryStream(contentBytes);

            try
            {
                // Act 1: Upload file
                var fileMetadata = await _minioService.UploadFileAsync(
                    uploadStream,
                    fileName,
                    bucketName,
                    "text/plain",
                    "Integration test file",
                    "tests",
                    false,
                    DateTime.UtcNow.AddHours(1));

                // Assert 1: File metadata should be created
                Assert.NotNull(fileMetadata);
                Assert.Equal(fileName, fileMetadata.FileName);
                Assert.Equal(bucketName, fileMetadata.BucketName);
                Assert.Equal("text/plain", fileMetadata.ContentType);
                Assert.Equal(contentBytes.Length, fileMetadata.Size);

                // Act 2: Check if file exists
                var exists = await _minioService.FileExistsAsync(
                    fileMetadata.BucketName, 
                    fileMetadata.ObjectKey);

                // Assert 2: File should exist
                Assert.True(exists);

                // Act 3: Download file
                using var downloadStream = await _minioService.DownloadFileAsync(fileMetadata);
                var downloadedBytes = new byte[downloadStream.Length];
                await downloadStream.ReadAsync(downloadedBytes, 0, downloadedBytes.Length);
                var downloadedContent = Encoding.UTF8.GetString(downloadedBytes);

                // Assert 3: Downloaded content should match uploaded content
                Assert.Equal(content, downloadedContent);

                // Act 4: Get presigned URL
                var presignedUrl = await _minioService.GetPresignedUrlAsync(fileMetadata, 3600);

                // Assert 4: Presigned URL should be generated
                Assert.NotNull(presignedUrl);
                Assert.Contains(bucketName, presignedUrl);

                // Act 5: Delete file
                var deleteResult = await _minioService.DeleteFileAsync(
                    fileMetadata.BucketName, 
                    fileMetadata.ObjectKey);

                // Assert 5: File should be deleted successfully
                Assert.True(deleteResult);

                // Act 6: Verify file no longer exists
                var existsAfterDelete = await _minioService.FileExistsAsync(
                    fileMetadata.BucketName, 
                    fileMetadata.ObjectKey);

                // Assert 6: File should not exist after deletion
                Assert.False(existsAfterDelete);
            }
            catch (Exception ex)
            {
                // Clean up in case of test failure
                try
                {
                    await _minioService.DeleteFileAsync(bucketName, $"tests/{fileName}");
                }
                catch
                {
                    // Ignore cleanup errors
                }
                
                throw new Exception($"MinIO integration test failed: {ex.Message}", ex);
            }
        }

        [Fact]
        [Trait("Category", "Integration")]
        public async Task BucketInitialization_ShouldCreateAllRequiredBuckets()
        {
            // Skip if MinIO is not available
            if (!await IsMinIOAvailable())
            {
                return;
            }

            // Act
            var result = await _minioService.InitializeBucketsAsync();

            // Assert
            Assert.True(result, "Bucket initialization should succeed");

            // Verify each bucket exists
            var buckets = new[]
            {
                FileStorageConstants.BucketNames.PROFILE_PICTURE,
                FileStorageConstants.BucketNames.CERTIFICATION,
                FileStorageConstants.BucketNames.SIGNATURES,
                FileStorageConstants.BucketNames.TEMP,
                FileStorageConstants.BucketNames.DOCS
            };

            // Note: BucketExistsAsync is not exposed in the interface
            // The fact that InitializeBucketsAsync succeeded indicates buckets exist
            // This is sufficient for the integration test
        }

        [Fact]
        [Trait("Category", "Integration")]
        public async Task FileValidation_ShouldRejectInvalidFiles()
        {
            // Skip if MinIO is not available
            if (!await IsMinIOAvailable())
            {
                return;
            }

            // Test file too large
            var largeContent = new byte[51 * 1024 * 1024]; // 51MB
            using var largeStream = new MemoryStream(largeContent);

            await Assert.ThrowsAsync<ArgumentException>(async () =>
                await _minioService.UploadFileAsync(
                    largeStream,
                    "large.txt",
                    FileStorageConstants.BucketNames.TEMP,
                    "text/plain"));

            // Test invalid file type
            var content = Encoding.UTF8.GetBytes("test");
            using var stream = new MemoryStream(content);

            await Assert.ThrowsAsync<ArgumentException>(async () =>
                await _minioService.UploadFileAsync(
                    stream,
                    "test.exe",
                    FileStorageConstants.BucketNames.TEMP,
                    "application/x-executable"));
        }

        private async Task<bool> IsMinIOAvailable()
        {
            try
            {
                // Try to list buckets to check if MinIO is available
                await _minioService.InitializeBucketsAsync();
                return true;
            }
            catch
            {
                // MinIO is not available, skip integration tests
                return false;
            }
        }
    }
}
