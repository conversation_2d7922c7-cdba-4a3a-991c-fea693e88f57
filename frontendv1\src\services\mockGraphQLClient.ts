import { 
  mockWorkers, 
  mockTrainings, 
  mockTrades, 
  mockSkills, 
  mockTrainingHistory, 
  mockToolboxSessions 
} from '../data/mockData';
import { 
  Worker, 
  Training, 
  Trade, 
  Skill, 
  WorkerTrainingHistory, 
  ToolboxSession,
  TrainingStatus 
} from '../types';

// Simulate network delay
const delay = (ms: number = 500) => new Promise(resolve => setTimeout(resolve, ms));

// Mock GraphQL response wrapper
interface MockGraphQLResponse<T> {
  data: T;
  loading: boolean;
  error?: Error;
}

// Mock GraphQL Client
export class MockGraphQLClient {
  private static instance: MockGraphQLClient;
  
  // In-memory data store (simulating backend state)
  private workers: Worker[] = [...mockWorkers];
  private trainings: Training[] = [...mockTrainings];
  private trades: Trade[] = [...mockTrades];
  private skills: Skill[] = [...mockSkills];
  private trainingHistory: WorkerTrainingHistory[] = [...mockTrainingHistory];
  private toolboxSessions: ToolboxSession[] = [...mockToolboxSessions];

  private constructor() {}

  static getInstance(): MockGraphQLClient {
    if (!MockGraphQLClient.instance) {
      MockGraphQLClient.instance = new MockGraphQLClient();
    }
    return MockGraphQLClient.instance;
  }

  // Worker Queries with tenant support
  async getWorkers(tenantId: string, siteId?: string): Promise<MockGraphQLResponse<{ workers: Worker[] }>> {
    await delay();

    let filteredWorkers = this.workers.filter(worker => worker.tenantId === tenantId);

    if (siteId) {
      // Filter by site assignment rather than direct siteId
      filteredWorkers = filteredWorkers.filter(worker =>
        worker.siteAssignments?.some(assignment =>
          assignment.siteId === siteId && assignment.status === 'active'
        )
      );
    }

    return {
      data: { workers: filteredWorkers },
      loading: false
    };
  }

  async getWorkerById(id: number, tenantId: string): Promise<MockGraphQLResponse<{ worker: Worker | null }>> {
    await delay();

    const worker = this.workers.find(w => w.id === id && w.tenantId === tenantId) || null;

    return {
      data: { worker },
      loading: false
    };
  }

  // Training Queries with tenant support
  async getTrainings(tenantId: string): Promise<MockGraphQLResponse<{ trainings: Training[] }>> {
    await delay();

    const filteredTrainings = this.trainings.filter(training => training.tenantId === tenantId);

    return {
      data: { trainings: filteredTrainings },
      loading: false
    };
  }

  async getTrainingExpiryTracker(_siteId?: string): Promise<MockGraphQLResponse<{ trainingExpiryTracker: any[] }>> {
    await delay();
    
    const today = new Date();
    const expiryData = this.trainingHistory
      .filter(th => th.expiryDate)
      .map(th => {
        const worker = this.workers.find(w => w.id === th.workerId);
        const training = this.trainings.find(t => t.id === th.trainingId);
        const expiryDate = new Date(th.expiryDate!);
        const daysUntilExpiry = Math.ceil((expiryDate.getTime() - today.getTime()) / (1000 * 3600 * 24));
        
        let status = 'valid';
        if (daysUntilExpiry < 0) status = 'expired';
        else if (daysUntilExpiry <= 30) status = 'expiring-soon';
        else if (daysUntilExpiry <= 90) status = 'expiring-later';

        return {
          workerId: th.workerId,
          workerName: worker?.name || 'Unknown',
          trainingId: th.trainingId,
          trainingName: training?.name || 'Unknown Training',
          completionDate: th.completionDate,
          expiryDate: th.expiryDate,
          daysUntilExpiry,
          status
        };
      });

    return {
      data: { trainingExpiryTracker: expiryData },
      loading: false
    };
  }

  // Toolbox Session Queries
  async getToolboxSessions(_siteId?: string, dateFrom?: string, dateTo?: string): Promise<MockGraphQLResponse<{ toolboxSessions: ToolboxSession[] }>> {
    await delay();
    
    let filteredSessions = this.toolboxSessions;
    
    if (dateFrom || dateTo) {
      filteredSessions = this.toolboxSessions.filter(session => {
        const sessionDate = new Date(session.sessionTime);
        if (dateFrom && sessionDate < new Date(dateFrom)) return false;
        if (dateTo && sessionDate > new Date(dateTo)) return false;
        return true;
      });
    }

    return {
      data: { toolboxSessions: filteredSessions },
      loading: false
    };
  }

  // Trade and Skill Queries with tenant support
  async getTrades(tenantId: string): Promise<MockGraphQLResponse<{ trades: Trade[] }>> {
    await delay();
    const filteredTrades = this.trades.filter(trade => trade.tenantId === tenantId);
    return {
      data: { trades: filteredTrades },
      loading: false
    };
  }

  async getSkills(tenantId: string): Promise<MockGraphQLResponse<{ skills: Skill[] }>> {
    await delay();
    const filteredSkills = this.skills.filter(skill => skill.tenantId === tenantId);
    return {
      data: { skills: filteredSkills },
      loading: false
    };
  }

  // Worker Mutations
  async createWorker(input: Partial<Worker>): Promise<MockGraphQLResponse<{ createWorker: Worker }>> {
    await delay();
    
    const newWorker: Worker = {
      id: Math.max(...this.workers.map(w => w.id)) + 1,
      name: input.name || '',
      company: input.company || '',
      nationalId: input.nationalId || '',
      phoneNumber: input.phoneNumber || '',
      email: input.email,
      dateOfBirth: input.dateOfBirth,
      gender: input.gender || 'Male',
      manHours: input.manHours || 0,
      photoUrl: input.photoUrl,
      inductionDate: input.inductionDate,
      medicalCheckDate: input.medicalCheckDate,
      rating: input.rating || 0,
      // siteId: input.siteId || 'site1',
      trades: input.trades || [],
      skills: input.skills || [],
      trainings: input.trainings || [],
      trainingHistory: input.trainingHistory || [],
      certifications: input.certifications || [],
      age: input.age,
      trainingsCompleted: input.trainingsCompleted || 0,
      createdAt: new Date().toISOString(),
      createdBy: 'Current User',
      updatedAt: new Date().toISOString(),
      updatedBy: 'Current User',
      tenantId: '',
      status: 'active',
      siteAssignments: []
    };

    this.workers.push(newWorker);

    return {
      data: { createWorker: newWorker },
      loading: false
    };
  }

  async updateWorker(id: number, input: Partial<Worker>): Promise<MockGraphQLResponse<{ updateWorker: Worker }>> {
    await delay();
    
    const workerIndex = this.workers.findIndex(w => w.id === id);
    if (workerIndex === -1) {
      throw new Error(`Worker with id ${id} not found`);
    }

    const updatedWorker = {
      ...this.workers[workerIndex],
      ...input,
      updatedAt: new Date().toISOString(),
      updatedBy: 'Current User'
    };

    this.workers[workerIndex] = updatedWorker;

    return {
      data: { updateWorker: updatedWorker },
      loading: false
    };
  }

  async uploadWorkerPhoto(workerId: number, /*photo*/_: File): Promise<MockGraphQLResponse<{ uploadWorkerPhoto: { photoUrl: string; hikvisionRegistered: boolean; message: string } }>> {
    await delay(1000); // Simulate longer upload time
    
    // Simulate photo upload
    const photoUrl = `https://example.com/photos/worker-${workerId}-${Date.now()}.jpg`;
    
    // Update worker with new photo
    await this.updateWorker(workerId, { photoUrl });

    return {
      data: {
        uploadWorkerPhoto: {
          photoUrl,
          hikvisionRegistered: true,
          message: 'Photo uploaded and registered with Hikvision successfully'
        }
      },
      loading: false
    };
  }

  async deleteWorkerPhoto(workerId: number): Promise<MockGraphQLResponse<{ deleteWorkerPhoto: { success: boolean; message: string } }>> {
    await delay();
    
    // Update worker to remove photo
    await this.updateWorker(workerId, { photoUrl: undefined });

    return {
      data: {
        deleteWorkerPhoto: {
          success: true,
          message: 'Photo deleted and worker removed from Hikvision system'
        }
      },
      loading: false
    };
  }

  // Training Mutations
  async recordTrainingCompletion(input: {
    workerId: number;
    trainingId: number;
    completionDate: string;
    expiryDate?: string;
    score?: number;
    notes?: string;
  }): Promise<MockGraphQLResponse<{ recordTrainingCompletion: WorkerTrainingHistory }>> {
    await delay();
    
    const newTrainingHistory: WorkerTrainingHistory = {
      id: Math.max(...this.trainingHistory.map(th => th.id)) + 1,
      workerId: input.workerId,
      trainingId: input.trainingId,
      completionDate: input.completionDate,
      expiryDate: input.expiryDate,
      score: input.score,
      notes: input.notes,
      status: TrainingStatus.Completed,
      createdAt: new Date().toISOString(),
      createdBy: 'Current User',
      updatedAt: new Date().toISOString(),
      updatedBy: 'Current User'
    };

    this.trainingHistory.push(newTrainingHistory);

    // Update worker's training history
    const workerIndex = this.workers.findIndex(w => w.id === input.workerId);
    if (workerIndex !== -1) {
      this.workers[workerIndex].trainingHistory.push(newTrainingHistory);
      this.workers[workerIndex].trainingsCompleted += 1;
    }

    return {
      data: { recordTrainingCompletion: newTrainingHistory },
      loading: false
    };
  }

  async bulkAssignTraining(_trainingId: number, workerIds: number[], _scheduledDate?: string): Promise<MockGraphQLResponse<{ bulkAssignTraining: { success: boolean; assignedCount: number; message: string } }>> {
    await delay();
    
    // Simulate bulk assignment
    const assignedCount = workerIds.length;

    return {
      data: {
        bulkAssignTraining: {
          success: true,
          assignedCount,
          message: `Training assigned to ${assignedCount} workers successfully`
        }
      },
      loading: false
    };
  }

  // Toolbox Session Mutations - Backend aligned
  async createToolboxSession(input: {
    sessionTime: string;
    topic: string;
    conductor: string;
    notes?: string;
    photoFile?: File;
    attendeeIds: number[];
  }): Promise<MockGraphQLResponse<{ createToolboxSession: ToolboxSession }>> {
    await delay();

    const newSessionId = Math.max(...this.toolboxSessions.map(s => s.id), 0) + 1;

    const newSession: ToolboxSession = {
      id: newSessionId,
      sessionTime: input.sessionTime,
      topic: input.topic,
      conductor: input.conductor,
      photoUrl: input.photoFile ? `https://example.com/toolbox-photos/session-${Date.now()}.jpg` : undefined,
      notes: input.notes,
      attendances: input.attendeeIds.map((workerId, index) => ({
        id: Date.now() + index,
        toolboxSessionId: newSessionId,
        workerId,
        wasPresent: true, // Default to present, can be updated later
        notes: undefined,
        createdAt: new Date().toISOString(),
        createdBy: input.conductor,
        updatedAt: new Date().toISOString(),
        updatedBy: input.conductor
      })),
      createdAt: new Date().toISOString(),
      createdBy: input.conductor,
      updatedAt: new Date().toISOString(),
      updatedBy: input.conductor
    };

    this.toolboxSessions.push(newSession);

    return {
      data: { createToolboxSession: newSession },
      loading: false
    };
  }

  // Reset data (for testing purposes)
  resetData(): void {
    this.workers = [...mockWorkers];
    this.trainings = [...mockTrainings];
    this.trades = [...mockTrades];
    this.skills = [...mockSkills];
    this.trainingHistory = [...mockTrainingHistory];
    this.toolboxSessions = [...mockToolboxSessions];
  }
}

// Export singleton instance
export const mockGraphQLClient = MockGraphQLClient.getInstance();
