# Leadership Team Page Implementation Guide

## Data Source & Sheet Structure

### Google Sheet Requirements
- **Sheet Name**: `Team` (in the same spreadsheet as projects: `SHEET_ID=1D26OB_j31OvqwJ-h-_-0dc3aiI8Pru9KOenAGgArdG4`)
- **Columns**:
  ```
  full_name | designation | level | subsidiary_company | bio | image_url | social_links | department | skills | languages | certifications | years_of_experience | education | awards
  ```
- **Key Rules**:
  - `subsidiary_company`: Only `Eacon`, `Lexi<PERSON>`, or `Gokul` (dont show this on the website they are for internal use)
  - `level`: `Leader` or `Team` (display **only** `Leader` entries)
  - `social_links`: Comma-separated URLs in strict order: `LinkedIn, X.com, Facebook`. Use placeholders (e.g., `#`) for missing links

## Frontend Requirements

### A. Leadership Grid
- **Desktop**: 3-column grid (CSS Grid/`grid-template-columns: repeat(3, 1fr)`)
- **Mobile**: Single column (`1fr`)
- **Card Design**:
  1. **Image**: Square container (e.g., `300x300px`), centered with hover scale-up animation
  2. **Details**:
     - Name (Figtree font, `24px`, `#84277F`)
     - Designation (opensans, `18px`, `#333`)
  3. **CTA**: "+" button (fixed at card bottom-right) to trigger modal

### B. Profile Modal (Digital CV)
- **Layout**:
  - **Left Panel**:
    - Leader image (responsive, `500px` max-width)
    - Social icons (LinkedIn/X/Facebook in specified order; hide missing links)
  - **Right Panel**:
    - Name, designation, subsidiary
    - Display sections directly (no accordions): `bio`, `skills`, `education`, `awards`
    - Metrics sidebar: `years_of_experience`, `languages`, `certifications`
    - Close button (top-right)
    - If a field is missing, dont display the section thats missing

- **Design Rules**:
  - Modern overlay (white background, `#E8C7E5` accent borders)
  - Close button (top-right), smooth entrance animation (`0.3s ease`)
  - Responsive: Stack panels vertically on mobile
  - **Adaptive Design**: Omit any sections where data is missing for a particular leader
  - Follow the same color scheme and design patterns used in previous modal dialogs and projects page

### C. Technical Constraints
- Reuse existing Google Sheets API logic
- Preserve header/footer CSS and all other existing elements; modify **only** the leadership grid section
- The implementation should integrate seamlessly with the existing page structure
- To avoid scrolling issues refer to the docs on how we solved the scrolling issues on the portfolio page

## Best Practices Integration
- **Visuals**: Use high-quality, consistent headshots (optimized for web)
- **Accessibility**: Alt text for images, ARIA labels for modals
- **Performance**: Lazy-load images, compress assets
- **Corporate Alignment**:
  - Colors: `#84277F` (accent), `#E8C7E5` (header), `#F9F0F8` (background)
  - Fonts: Figtree for titles, Open Sans for body text

## Deliverables
1. Vanilla JS code to:
   - Fetch/filter `level=Leader`
   - Generate grid/modal dynamically
2. CSS for responsive grid/modals (no frameworks)
3. QA checklist: Cross-browser testing, load time <2s
4. 