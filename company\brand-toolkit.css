.u-section-1 .u-sheet-1 {
  min-height: 683px;
}

.u-section-1 .u-layout-wrap-1 {
  margin-top: 40px;
  margin-bottom: -22px;
}

.u-section-1 .u-layout-cell-1 {
  min-height: 285px;
  --radius: 20px;
}

.u-section-1 .u-container-layout-1 {
  box-shadow: 5px 5px 8px 0 rgba(204,211,219,0.4);
  padding: 30px;
}

.u-section-1 .u-text-1 {
  font-weight: 600;
  font-size: 1.5rem;
  margin: 0;
}

.u-section-1 .u-text-2 {
  font-size: 1rem;
  font-weight: 500;
  font-family: "Open Sans", sans-serif;
  margin: 8px 0 0;
}

.u-section-1 .u-layout-cell-2 {
  min-height: 285px;
  --radius: 20px;
}

.u-section-1 .u-container-layout-2 {
  box-shadow: 5px 5px 8px 0 rgba(204,211,219,0.4);
  padding: 29px 30px;
}

.u-section-1 .u-text-3 {
  font-size: 1.5rem;
  font-weight: 300;
  margin: 0 20px 0 0;
}

.u-section-1 .u-list-1 {
  margin: 19px 0 0;
}

.u-section-1 .u-repeater-1 {
  grid-auto-columns: calc(25% - 7.5px);
  grid-template-columns: repeat(4, calc(25% - 7.5px));
  min-height: 158px;
  grid-gap: 10px;
}

.u-section-1 .u-list-item-1 {
  --radius: 20px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-1 .u-container-layout-3 {
  padding: 10px 0;
}

.u-section-1 .u-text-4 {
  font-size: 1rem;
  width: 116px;
  margin: 56px auto 0;
}

.u-section-1 .u-list-item-2 {
  --radius: 20px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-1 .u-container-layout-4 {
  padding: 10px 0;
}

.u-section-1 .u-text-5 {
  font-size: 1rem;
  width: 116px;
  margin: 56px auto 0;
}

.u-section-1 .u-list-item-3 {
  --radius: 20px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-1 .u-container-layout-5 {
  padding: 10px 0;
}

.u-section-1 .u-text-6 {
  font-size: 1rem;
  width: 116px;
  margin: 56px auto 0;
}

.u-section-1 .u-list-item-4 {
  --radius: 20px;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-1 .u-container-layout-6 {
  padding: 10px 0;
}

.u-section-1 .u-text-7 {
  font-size: 1rem;
  width: 116px;
  margin: 56px auto 0;
}

.u-section-1 .u-layout-cell-3 {
  min-height: 323px;
  --radius: 20px;
}

.u-section-1 .u-container-layout-7 {
  box-shadow: 5px 5px 8px 0 rgba(204,211,219,0.4);
  padding: 30px;
}

.u-section-1 .u-list-2 {
  margin-bottom: 0;
  margin-top: 0;
}

.u-section-1 .u-repeater-2 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 247px;
  grid-gap: 10px;
}

.u-section-1 .u-container-layout-8 {
  padding: 10px;
}

.u-section-1 .u-image-1 {
  width: 105px;
  height: 99px;
  margin: 0 auto 0 0;
}

.u-section-1 .u-text-8 {
  font-size: 1rem;
  font-weight: 600;
  margin: -64px 40px 0 124px;
}

.u-section-1 .u-container-layout-9 {
  padding: 10px;
}

.u-section-1 .u-image-2 {
  width: 105px;
  height: 99px;
  margin: 0 auto 0 0;
}

.u-section-1 .u-text-9 {
  font-size: 1rem;
  font-weight: 600;
  margin: -64px 40px 0 124px;
}

.u-section-1 .u-layout-cell-4 {
  min-height: 323px;
  --radius: 20px;
}

.u-section-1 .u-container-layout-10 {
  box-shadow: 5px 5px 8px 0 rgba(204,211,219,0.4);
  padding: 30px;
}

.u-section-1 .u-text-10 {
  font-size: 1.5rem;
  font-weight: 300;
  margin: 0 151px 0 0;
}

.u-section-1 .u-text-11 {
  font-weight: 600;
  font-size: 3rem;
  margin: 20px 230px 0 0;
}

.u-section-1 .u-text-12 {
  font-size: 1.5rem;
  margin: -58px 0 0 97px;
}

.u-section-1 .u-text-13 {
  font-size: 3rem;
  margin: 30px 222px 0 0;
}

.u-section-1 .u-text-14 {
  margin: -67px 56px 0 111px;
}

.u-section-1 .u-layout-cell-5 {
  min-height: 327px;
  --radius: 20px;
}

.u-section-1 .u-container-layout-11 {
  box-shadow: 5px 5px 8px 0 rgba(204,211,219,0.4);
  padding: 30px;
}

.u-section-1 .u-text-15 {
  font-size: 3rem;
  margin: 0 10px;
}

@media (max-width: 1199px) {
  .u-section-1 .u-sheet-1 {
    min-height: 850px;
  }

  .u-section-1 .u-layout-wrap-1 {
    margin-bottom: 59px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 391px;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 391px;
  }

  .u-section-1 .u-text-3 {
    margin-right: 0;
  }

  .u-section-1 .u-list-1 {
    margin-top: 69px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-1 .u-repeater-1 {
    min-height: 128px;
  }

  .u-section-1 .u-layout-cell-3 {
    min-height: 380px;
  }

  .u-section-1 .u-repeater-2 {
    grid-template-columns: 100%;
  }

  .u-section-1 .u-container-layout-8 {
    padding-left: 0;
    padding-right: 0;
  }

  .u-section-1 .u-image-1 {
    margin-top: 1px;
    margin-left: 10px;
  }

  .u-section-1 .u-text-8 {
    width: auto;
    margin-top: -62px;
    margin-right: -18px;
    margin-left: 135px;
  }

  .u-section-1 .u-container-layout-9 {
    padding-left: 0;
    padding-right: 0;
  }

  .u-section-1 .u-image-2 {
    margin-top: 1px;
    margin-left: 10px;
  }

  .u-section-1 .u-text-9 {
    width: auto;
    margin-top: -62px;
    margin-right: -18px;
    margin-left: 135px;
  }

  .u-section-1 .u-layout-cell-4 {
    min-height: 380px;
  }

  .u-section-1 .u-container-layout-10 {
    padding-left: 29px;
    padding-right: 29px;
  }

  .u-section-1 .u-text-10 {
    font-weight: 600;
    margin-right: 85px;
  }

  .u-section-1 .u-text-11 {
    margin-right: 163px;
  }

  .u-section-1 .u-text-12 {
    width: auto;
    margin-left: 98px;
  }

  .u-section-1 .u-text-13 {
    margin-right: 156px;
  }

  .u-section-1 .u-text-14 {
    width: auto;
    margin-top: -58px;
    margin-right: 3px;
    margin-left: 98px;
  }

  .u-section-1 .u-layout-cell-5 {
    min-height: 380px;
  }

  .u-section-1 .u-text-15 {
    margin-left: 0;
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-1 .u-sheet-1 {
    min-height: 194px;
  }

  .u-section-1 .u-layout-wrap-1 {
    position: relative;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-1 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5px);
    grid-template-columns: repeat(2, calc(50% - 5px));
    min-height: 408px;
  }

  .u-section-1 .u-layout-cell-3 {
    min-height: 100px;
  }

  .u-section-1 .u-text-8 {
    margin-top: -64px;
    margin-left: 62px;
  }

  .u-section-1 .u-text-9 {
    margin-top: -64px;
    margin-left: 62px;
  }

  .u-section-1 .u-layout-cell-4 {
    min-height: 100px;
  }

  .u-section-1 .u-text-10 {
    margin-right: 12px;
  }

  .u-section-1 .u-text-11 {
    margin-right: 90px;
  }

  .u-section-1 .u-text-12 {
    margin-left: 25px;
  }

  .u-section-1 .u-text-13 {
    margin-right: 83px;
  }

  .u-section-1 .u-text-14 {
    margin-top: -67px;
    margin-right: 0;
    margin-left: 28px;
  }

  .u-section-1 .u-layout-cell-5 {
    min-height: 100px;
  }
}

@media (max-width: 767px) {
  .u-section-1 .u-sheet-1 {
    min-height: 475px;
  }

  .u-section-1 .u-container-layout-1 {
    padding: 20px;
  }

  .u-section-1 .u-text-1 {
    margin-top: 50px;
  }

  .u-section-1 .u-container-layout-2 {
    padding: 20px;
  }

  .u-section-1 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-1 .u-container-layout-7 {
    padding: 20px;
  }

  .u-section-1 .u-container-layout-10 {
    padding: 20px;
  }

  .u-section-1 .u-text-13 {
    font-size: 2.6666666666666665rem;
  }

  .u-section-1 .u-container-layout-11 {
    padding: 20px;
  }
}

@media (max-width: 575px) {
  .u-section-1 .u-sheet-1 {
    min-height: 346px;
  }

  .u-section-1 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-1 .u-text-8 {
    margin-right: 0;
    margin-left: 0;
  }

  .u-section-1 .u-text-9 {
    margin-right: 0;
    margin-left: 0;
  }

  .u-section-1 .u-text-10 {
    margin-right: 0;
  }

  .u-section-1 .u-text-11 {
    margin-right: 0;
  }

  .u-section-1 .u-text-12 {
    margin-left: 0;
  }

  .u-section-1 .u-text-13 {
    margin-right: 0;
  }

  .u-section-1 .u-text-14 {
    margin-left: 0;
  }
}