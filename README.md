# Laxmi Group Website

This repository contains the website for **Laxmi Group Kenya**, a leading construction company specializing in sustainable solutions across commercial, residential, and industrial projects. The website was designed and developed to showcase Laxmi Group's portfolio, services, and leadership, as well as provide a seamless user experience for potential clients and partners.

## Table of Contents

- [About Laxmi Group](#about-laxmi-group)
- [Features](#features)
- [Tech Stack](#tech-stack)
- [Setup Instructions](#setup-instructions)
- [Development](#development)
- [Contributing](#contributing)
- [License](#license)

## About Laxmi Group

Laxmi Group is dedicated to building the future with innovation, sustainability, and quality. With over 50 years of combined experience in construction, they provide a wide range of services, including residential, commercial, and industrial projects.

The website is built to showcase their work, leadership team, and core values, providing detailed information on their services, past projects, and commitment to quality and safety.

## Features

- **Portfolio Showcase**: Displays the company's completed and ongoing projects.
- **Team Page**: Highlights the leadership and key members of Laxmi Group.
- **Services**: Lists the construction services provided, including project management, engineering, and more.
- **Contact Form**: Allows users to reach out for project inquiries, partnerships, or other business communications.
- **SEO Optimization**: Integrated SEO-friendly structure for better discoverability.
- **Responsive Design**: The website is fully responsive, providing an optimal experience on both desktop and mobile devices.

## Tech Stack

- **Frontend**: HTML5, CSS3, JavaScript (jQuery)
- **Frameworks**: Custom CSS and JavaScript (with added enhancements)
- **Fonts**: Google Fonts (Roboto, Open Sans, Playfair Display)
- **SEO**: Meta Tags, Open Graph for social media integration
- **Version Control**: Git, GitHub for code management

## Setup Instructions

Follow these steps to set up the project locally for development:

1. **Clone the repository**:
   ```bash
   git clone https://github.com/lewiskimaru/laxmi-group-website.git
