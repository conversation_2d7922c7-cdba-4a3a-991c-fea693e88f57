import React, { useState } from "react";
import {
	BarChart3,
	Download,
	Calendar,
	Users,
	Clock,
	TrendingUp,
} from "lucide-react";

interface ReportsTabProps {
	siteId: string;
}

const ReportsTab: React.FC<ReportsTabProps> = ({ siteId:_siteId }) => {
	const [dateRange, setDateRange] = useState({
		start: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
			.toISOString()
			.split("T")[0], // 30 days ago
		end: new Date().toISOString().split("T")[0], // today
	});

	const [reportType, setReportType] = useState<
		"attendance" | "overtime" | "productivity"
	>("attendance");

	// Mock report data
	const reportData = {
		attendance: {
			totalWorkDays: 22,
			averageAttendance: 38.5,
			totalAbsences: 12,
			lateArrivals: 8,
			perfectAttendance: 15,
		},
		overtime: {
			totalOvertimeHours: 156,
			averageOvertimePerWorker: 3.9,
			overtimeRequests: 24,
			approvedRequests: 18,
		},
		productivity: {
			averageHoursPerDay: 8.2,
			totalProjectHours: 1804,
			efficiencyRating: 92,
			breakCompliance: 95,
		},
	};

	const handleExportReport = () => {
		console.log("Exporting report...", { reportType, dateRange });
		alert(
			`Exporting ${reportType} report for ${dateRange.start} to ${dateRange.end}`,
		);
	};

	const handleGenerateReport = () => {
		console.log("Generating report...", { reportType, dateRange });
		alert("Report generation would be implemented here");
	};

	const formatDate = (dateStr: string) => {
		return new Date(dateStr).toLocaleDateString("en-GB", {
			day: "2-digit",
			month: "short",
			year: "numeric",
		});
	};

	return (
		<div className="space-y-6">
			{/* Report Configuration */}
			<div className="bg-white rounded-lg border border-gray-200 p-6">
				<h3 className="text-lg font-semibold text-gray-900 mb-4">
					Report Configuration
				</h3>

				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					{/* Report Type */}
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-2">
							Report Type
						</label>
						<select
							value={reportType}
							onChange={(e) => setReportType(e.target.value as any)}
							className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
						>
							<option value="attendance">Attendance Report</option>
							<option value="overtime">Overtime Report</option>
							<option value="productivity">Productivity Report</option>
						</select>
					</div>

					{/* Date Range */}
					<div>
						<label className="block text-sm font-medium text-gray-700 mb-2">
							Start Date
						</label>
						<input
							type="date"
							value={dateRange.start}
							onChange={(e) =>
								setDateRange((prev) => ({ ...prev, start: e.target.value }))
							}
							className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
						/>
					</div>

					<div>
						<label className="block text-sm font-medium text-gray-700 mb-2">
							End Date
						</label>
						<input
							type="date"
							value={dateRange.end}
							onChange={(e) =>
								setDateRange((prev) => ({ ...prev, end: e.target.value }))
							}
							className="w-full border border-gray-300 rounded-md px-3 py-2 text-sm focus:ring-green-500 focus:border-green-500"
						/>
					</div>
				</div>

				<div className="flex justify-end space-x-3 mt-4">
					<button
						onClick={handleGenerateReport}
						className="flex items-center px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 transition-colors"
					>
						<BarChart3 className="h-4 w-4 mr-2" />
						Generate Report
					</button>
					<button
						onClick={handleExportReport}
						className="flex items-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md text-sm font-medium hover:bg-gray-50 transition-colors"
					>
						<Download className="h-4 w-4 mr-2" />
						Export
					</button>
				</div>
			</div>

			{/* Report Summary */}
			<div className="bg-white rounded-lg border border-gray-200 p-6">
				<div className="flex items-center justify-between mb-4">
					<h3 className="text-lg font-semibold text-gray-900">
						{reportType.charAt(0).toUpperCase() + reportType.slice(1)} Summary
					</h3>
					<span className="text-sm text-gray-500">
						{formatDate(dateRange.start)} - {formatDate(dateRange.end)}
					</span>
				</div>

				{/* Attendance Report */}
				{reportType === "attendance" && (
					<div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4">
						<div className="text-center p-4 bg-blue-50 rounded-lg">
							<Calendar className="h-8 w-8 text-blue-600 mx-auto mb-2" />
							<p className="text-2xl font-bold text-blue-600">
								{reportData.attendance.totalWorkDays}
							</p>
							<p className="text-sm text-gray-600">Work Days</p>
						</div>
						<div className="text-center p-4 bg-green-50 rounded-lg">
							<Users className="h-8 w-8 text-green-600 mx-auto mb-2" />
							<p className="text-2xl font-bold text-green-600">
								{reportData.attendance.averageAttendance}
							</p>
							<p className="text-sm text-gray-600">Avg Attendance</p>
						</div>
						<div className="text-center p-4 bg-red-50 rounded-lg">
							<Clock className="h-8 w-8 text-red-600 mx-auto mb-2" />
							<p className="text-2xl font-bold text-red-600">
								{reportData.attendance.totalAbsences}
							</p>
							<p className="text-sm text-gray-600">Total Absences</p>
						</div>
						<div className="text-center p-4 bg-yellow-50 rounded-lg">
							<Clock className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
							<p className="text-2xl font-bold text-yellow-600">
								{reportData.attendance.lateArrivals}
							</p>
							<p className="text-sm text-gray-600">Late Arrivals</p>
						</div>
						<div className="text-center p-4 bg-purple-50 rounded-lg">
							<TrendingUp className="h-8 w-8 text-purple-600 mx-auto mb-2" />
							<p className="text-2xl font-bold text-purple-600">
								{reportData.attendance.perfectAttendance}
							</p>
							<p className="text-sm text-gray-600">Perfect Attendance</p>
						</div>
					</div>
				)}

				{/* Overtime Report */}
				{reportType === "overtime" && (
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
						<div className="text-center p-4 bg-blue-50 rounded-lg">
							<Clock className="h-8 w-8 text-blue-600 mx-auto mb-2" />
							<p className="text-2xl font-bold text-blue-600">
								{reportData.overtime.totalOvertimeHours}
							</p>
							<p className="text-sm text-gray-600">Total OT Hours</p>
						</div>
						<div className="text-center p-4 bg-green-50 rounded-lg">
							<Users className="h-8 w-8 text-green-600 mx-auto mb-2" />
							<p className="text-2xl font-bold text-green-600">
								{reportData.overtime.averageOvertimePerWorker}
							</p>
							<p className="text-sm text-gray-600">Avg OT/Worker</p>
						</div>
						<div className="text-center p-4 bg-yellow-50 rounded-lg">
							<BarChart3 className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
							<p className="text-2xl font-bold text-yellow-600">
								{reportData.overtime.overtimeRequests}
							</p>
							<p className="text-sm text-gray-600">OT Requests</p>
						</div>
						<div className="text-center p-4 bg-purple-50 rounded-lg">
							<TrendingUp className="h-8 w-8 text-purple-600 mx-auto mb-2" />
							<p className="text-2xl font-bold text-purple-600">
								{reportData.overtime.approvedRequests}
							</p>
							<p className="text-sm text-gray-600">Approved</p>
						</div>
					</div>
				)}

				{/* Productivity Report */}
				{reportType === "productivity" && (
					<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
						<div className="text-center p-4 bg-blue-50 rounded-lg">
							<Clock className="h-8 w-8 text-blue-600 mx-auto mb-2" />
							<p className="text-2xl font-bold text-blue-600">
								{reportData.productivity.averageHoursPerDay}
							</p>
							<p className="text-sm text-gray-600">Avg Hours/Day</p>
						</div>
						<div className="text-center p-4 bg-green-50 rounded-lg">
							<BarChart3 className="h-8 w-8 text-green-600 mx-auto mb-2" />
							<p className="text-2xl font-bold text-green-600">
								{reportData.productivity.totalProjectHours}
							</p>
							<p className="text-sm text-gray-600">Total Hours</p>
						</div>
						<div className="text-center p-4 bg-yellow-50 rounded-lg">
							<TrendingUp className="h-8 w-8 text-yellow-600 mx-auto mb-2" />
							<p className="text-2xl font-bold text-yellow-600">
								{reportData.productivity.efficiencyRating}%
							</p>
							<p className="text-sm text-gray-600">Efficiency</p>
						</div>
						<div className="text-center p-4 bg-purple-50 rounded-lg">
							<Users className="h-8 w-8 text-purple-600 mx-auto mb-2" />
							<p className="text-2xl font-bold text-purple-600">
								{reportData.productivity.breakCompliance}%
							</p>
							<p className="text-sm text-gray-600">Break Compliance</p>
						</div>
					</div>
				)}
			</div>

			{/* Available Reports */}
			<div className="bg-white rounded-lg border border-gray-200 p-6">
				<h3 className="text-lg font-semibold text-gray-900 mb-4">
					Available Reports
				</h3>

				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
					<div className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer">
						<div className="flex items-center mb-2">
							<Users className="h-5 w-5 text-blue-600 mr-2" />
							<h4 className="font-medium text-gray-900">Daily Attendance</h4>
						</div>
						<p className="text-sm text-gray-600">
							Worker attendance tracking and analysis
						</p>
					</div>

					<div className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer">
						<div className="flex items-center mb-2">
							<Clock className="h-5 w-5 text-green-600 mr-2" />
							<h4 className="font-medium text-gray-900">Overtime Analysis</h4>
						</div>
						<p className="text-sm text-gray-600">
							Overtime hours and cost analysis
						</p>
					</div>

					<div className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer">
						<div className="flex items-center mb-2">
							<TrendingUp className="h-5 w-5 text-purple-600 mr-2" />
							<h4 className="font-medium text-gray-900">
								Productivity Metrics
							</h4>
						</div>
						<p className="text-sm text-gray-600">
							Worker productivity and efficiency metrics
						</p>
					</div>

					<div className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer">
						<div className="flex items-center mb-2">
							<BarChart3 className="h-5 w-5 text-red-600 mr-2" />
							<h4 className="font-medium text-gray-900">Payroll Export</h4>
						</div>
						<p className="text-sm text-gray-600">
							Export data for payroll processing
						</p>
					</div>

					<div className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer">
						<div className="flex items-center mb-2">
							<Calendar className="h-5 w-5 text-yellow-600 mr-2" />
							<h4 className="font-medium text-gray-900">Monthly Summary</h4>
						</div>
						<p className="text-sm text-gray-600">
							Comprehensive monthly time summary
						</p>
					</div>

					<div className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 cursor-pointer">
						<div className="flex items-center mb-2">
							<Download className="h-5 w-5 text-gray-600 mr-2" />
							<h4 className="font-medium text-gray-900">Custom Export</h4>
						</div>
						<p className="text-sm text-gray-600">
							Create custom time and attendance exports
						</p>
					</div>
				</div>
			</div>
		</div>
	);
};

export default ReportsTab;
