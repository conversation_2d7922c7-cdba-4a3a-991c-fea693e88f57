import React, { useState, useMemo } from 'react';
import { Users, Clock, AlertTriangle, CheckCircle, Wifi } from 'lucide-react';
import KPICard from '../dashboard/KPICard';
import TimeLogFilters from './TimeLogFilters';
import TimeLogTable from './TimeLogTable';
import EditTimeLogModal from './EditTimeLogModal';
import SyncAttendanceModal from './SyncAttendanceModal';
import { TimeLog } from '../../types';
import { TimeLogFilters as Filters, AttendanceSummary, TerminalStatus, EditTimeLogData, HikvisionSyncResult } from '../../types/time';
import { getCurrentDate, determineWorkerStatus, calculateTotalHours, calculateOvertimeHours } from '../../utils/timeUtils';

interface AttendanceTabProps {
	siteId: string;
}

// Mock data - in real app this would come from props or API
const mockTimeLogs: TimeLog[] = [
  {
    id: 'log1',
    workerId: 'W001',
    workerName: '<PERSON>',
    workerPhoto: undefined,
    workerTrade: 'Mason',
    date: getCurrentDate(),
    clockIn: '07:55',
    clockOut: '17:05',
    breakDuration: 60,
    totalHours: 8.17,
    overtime: 0.17,
    status: 'on-site',
    toolboxTalkAttended: true,
    isVerifiedByHikvision: true,
    hikvisionPersonId: 'hik_person_001',
    terminalId: 'term1'
  },
  {
    id: 'log2',
    workerId: 'W002',
    workerName: 'Mary Wanjiku',
    workerPhoto: undefined,
    workerTrade: 'Electrician',
    date: getCurrentDate(),
    clockIn: '08:15',
    clockOut: '17:00',
    breakDuration: 45,
    totalHours: 8.0,
    overtime: 0,
    status: 'late',
    toolboxTalkAttended: true,
    isManuallyEdited: true,
    editReason: 'Terminal malfunction - manual entry',
    isVerifiedByHikvision: false
  },
  {
    id: 'log3',
    workerId: 'W003',
    workerName: 'Peter Ochieng',
    workerPhoto: undefined,
    workerTrade: 'Carpenter',
    date: getCurrentDate(),
    clockIn: '07:45',
    clockOut: undefined,
    breakDuration: 30,
    totalHours: undefined,
    overtime: undefined,
    status: 'on-site',
    toolboxTalkAttended: true,
    isVerifiedByHikvision: true,
    hikvisionPersonId: 'hik_person_003',
    terminalId: 'term2'
  },
  {
    id: 'log4',
    workerId: 'W004',
    workerName: 'Grace Nyong',
    workerPhoto: undefined,
    workerTrade: 'Plumber',
    date: getCurrentDate(),
    clockIn: undefined,
    clockOut: undefined,
    breakDuration: undefined,
    totalHours: undefined,
    overtime: undefined,
    status: 'absent',
    toolboxTalkAttended: false,
    isVerifiedByHikvision: false
  }
];

const mockTerminals: TerminalStatus[] = [
	{
		id: "term1",
		name: "Main Gate Terminal",
		location: "Site Entrance",
		status: "online",
		lastSync: new Date(Date.now() - 5 * 60 * 1000).toISOString(),
		totalCheckInsToday: 28,
		ipAddress: "*************",
	},
	{
		id: "term2",
		name: "Warehouse Terminal",
		location: "Equipment Storage",
		status: "online",
		lastSync: new Date(Date.now() - 2 * 60 * 1000).toISOString(),
		totalCheckInsToday: 15,
		ipAddress: "*************",
	},
	{
		id: "term3",
		name: "Office Terminal",
		location: "Site Office",
		status: "offline",
		lastSync: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
		totalCheckInsToday: 0,
		ipAddress: "*************",
	},
];

const AttendanceTab: React.FC<AttendanceTabProps> = ({ siteId }) => {
	const [timeLogs, setTimeLogs] = useState<TimeLog[]>(mockTimeLogs);
	const [terminals] = useState<TerminalStatus[]>(mockTerminals);

	// Calculate terminal statistics
	const terminalStats = useMemo(() => {
		const onlineTerminals = terminals.filter(
			(t) => t.status === "online",
		).length;
		const totalTerminals = terminals.length;
		const totalCheckInsToday = terminals.reduce(
			(sum, t) => sum + t.totalCheckInsToday,
			0,
		);

    return {
      onlineTerminals,
      totalTerminals,
      totalCheckInsToday
    };
  }, [terminals]);
  const [filters, setFilters] = useState<Filters>({
    search: '',
    status: 'all',
    trade: '',
    date: getCurrentDate()
  });
  const [selectedTimeLog, setSelectedTimeLog] = useState<TimeLog | null>(null);
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [loading, _setLoading] = useState(false);
//   const [isSyncing, setIsSyncing] = useState(false);
  const [isSyncModalOpen, setIsSyncModalOpen] = useState(false);

	// Get unique trades for filter
	const availableTrades = useMemo(() => {
		const trades = [...new Set(timeLogs.map((log) => log.workerTrade))];
		return trades.sort();
	}, [timeLogs]);

	// Filter time logs
	const filteredTimeLogs = useMemo(() => {
		return timeLogs.filter((log) => {
			// Date filter
			if (log.date !== filters.date) return false;

			// Search filter
			if (filters.search) {
				const searchTerm = filters.search.toLowerCase();
				if (
					!log.workerName.toLowerCase().includes(searchTerm) &&
					!log.workerId.toLowerCase().includes(searchTerm) &&
					!log.workerTrade.toLowerCase().includes(searchTerm)
				) {
					return false;
				}
			}

			// Status filter
			if (filters.status !== "all" && log.status !== filters.status) {
				return false;
			}

			// Trade filter
			if (filters.trade && log.workerTrade !== filters.trade) {
				return false;
			}

			return true;
		});
	}, [timeLogs, filters]);

	// Calculate attendance summary
	const attendanceSummary: AttendanceSummary = useMemo(() => {
		const todayLogs = timeLogs.filter((log) => log.date === filters.date);

    return {
      totalWorkers: todayLogs.length,
      presentToday: todayLogs.filter(log => log.status === 'on-site' || log.status === 'late' || log.status === 'off-site').length,
      late: todayLogs.filter(log => log.status === 'late').length,
      absent: todayLogs.filter(log => log.status === 'absent').length,
      onSite: todayLogs.filter(log => log.status === 'on-site').length,
      verifiedByHikvision: todayLogs.filter(log => log.isVerifiedByHikvision === true).length,
      manualEntries: todayLogs.filter(log => log.isVerifiedByHikvision === false || log.isManuallyEdited === true).length
    };
  }, [timeLogs, filters.date]);

	const handleEditTimeLog = (timeLog: TimeLog) => {
		setSelectedTimeLog(timeLog);
		setIsEditModalOpen(true);
	};

	const handleSaveTimeLog = (timeLogId: string, data: EditTimeLogData) => {
		setTimeLogs((prev) =>
			prev.map((log) => {
				if (log.id === timeLogId) {
					const updatedLog = {
						...log,
						clockIn: data.clockIn || log.clockIn,
						clockOut: data.clockOut || log.clockOut,
						breakDuration: data.breakDuration,
						isManuallyEdited: true,
						editReason: data.reason,
						editedBy: "Current User",
						editedAt: new Date().toISOString(),
					};

					// Recalculate hours and status
					if (updatedLog.clockIn && updatedLog.clockOut) {
						updatedLog.totalHours = calculateTotalHours(
							updatedLog.clockIn,
							updatedLog.clockOut,
							updatedLog.breakDuration || 0,
						);
						updatedLog.overtime = calculateOvertimeHours(updatedLog.totalHours);
					}

					updatedLog.status = determineWorkerStatus(
						updatedLog.clockIn,
						updatedLog.clockOut,
					);

					return updatedLog;
				}
				return log;
			}),
		);
	};

  const handleExport = () => {
    console.log('Exporting time logs...', filteredTimeLogs);
    alert('Export functionality would be implemented here');
  };

  const handleSyncHikvision = () => {
    setIsSyncModalOpen(true);
  };

  const handleSyncComplete = (result: HikvisionSyncResult) => {
    if (result.success) {
      // In real app, this would refresh the time logs from the server
      // For now, we'll just show a success message
      console.log('Sync completed:', result);
    }
  };

	return (
		<>
			<div className="space-y-6">
				{/* KPI Cards */}
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
					<KPICard
						title="Total Workers"
						value={attendanceSummary.totalWorkers}
						icon={<Users className="h-6 w-6" />}
					/>
					<KPICard
						title="Present Today"
						value={attendanceSummary.presentToday}
						icon={<CheckCircle className="h-6 w-6" />}
					/>
					<KPICard
						title="Late Arrivals"
						value={attendanceSummary.late}
						icon={<AlertTriangle className="h-6 w-6" />}
					/>
					<KPICard
						title="Absent"
						value={attendanceSummary.absent}
						icon={<Clock className="h-6 w-6" />}
					/>
					<KPICard
						title="Terminal Status"
						value={`${terminalStats.onlineTerminals}/${terminalStats.totalTerminals} Online`}
						change={terminalStats.totalCheckInsToday}
						icon={<Wifi className="h-6 w-6" />}
					/>
				</div>

        {/* Filters */}
        <TimeLogFilters
          filters={filters}
          onFiltersChange={setFilters}
          onExport={handleExport}
          onSync={handleSyncHikvision}
          trades={availableTrades}
        />

				{/* Time Logs Table */}
				<TimeLogTable
					timeLogs={filteredTimeLogs}
					onEdit={handleEditTimeLog}
					loading={loading}
				/>
			</div>

      {/* Edit Modal */}
      <EditTimeLogModal
        timeLog={selectedTimeLog}
        isOpen={isEditModalOpen}
        onClose={() => {
          setIsEditModalOpen(false);
          setSelectedTimeLog(null);
        }}
        onSave={handleSaveTimeLog}
      />

      {/* Sync Modal */}
      <SyncAttendanceModal
        isOpen={isSyncModalOpen}
        onClose={() => setIsSyncModalOpen(false)}
        siteId={siteId}
        selectedDate={filters.date}
        onSyncComplete={handleSyncComplete}
      />
    </>
  );
};

export default AttendanceTab;
