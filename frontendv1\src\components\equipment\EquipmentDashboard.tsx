import { useState, useEffect } from "react";
import {
	Wrench,
	HardHat,
	AlertTriangle,
	Clock,
	TrendingUp,
	Package,
	CheckCircle,
	XCircle,
	Plus,
	QrCode,
	ClipboardCheck,
} from "lucide-react";
import StatCard from "../data/shared/StatCard";
import QuickActionCard from "../data/shared/QuickActionCard";
import { EquipmentDashboardData } from "../../types/equipment";

interface EquipmentDashboardProps {
	siteId: string;
	onNavigateToTab: (tabId: string) => void;
}

const EquipmentDashboard = ({
	siteId,
	onNavigateToTab,
}: EquipmentDashboardProps) => {
	const [dashboardData, _setDashboardData] = useState<EquipmentDashboardData>({
		stats: {
			totalEquipment: 45,
			availableEquipment: 32,
			inUseEquipment: 13,
			maintenanceRequired: 3,
			overdueInspections: 2,
			totalPPEItems: 128,
			lowStockPPE: 8,
			totalMaintenanceCost: 15420,
			averageUtilization: 78,
		},
		recentAssignments: [],
		upcomingMaintenance: [],
		overdueInspections: [],
		lowStockPPE: [],
		utilizationTrends: [],
	});

	const [isLoading, setIsLoading] = useState(true);

	useEffect(() => {
		// Simulate API call
		const fetchDashboardData = async () => {
			setIsLoading(true);
			// Mock data loading
			setTimeout(() => {
				setIsLoading(false);
			}, 1000);
		};

		fetchDashboardData();
	}, [siteId]);

	const getUtilizationColor = (utilization: number) => {
		if (utilization >= 80) return "text-green-600";
		if (utilization >= 60) return "text-yellow-600";
		return "text-red-600";
	};

	// const getStatusColor = (status: string) => {
	// 	switch (status) {
	// 		case "available":
	// 			return "text-green-600";
	// 		case "in-use":
	// 			return "text-blue-600";
	// 		case "maintenance":
	// 			return "text-yellow-600";
	// 		case "damaged":
	// 			return "text-red-600";
	// 		default:
	// 			return "text-gray-600";
	// 	}
	// };

	if (isLoading) {
		return (
			<div className="space-y-6">
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
					{[...Array(8)].map((_, i) => (
						<div
							key={i}
							className="bg-white rounded-lg border border-gray-200 p-6 animate-pulse"
						>
							<div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
							<div className="h-8 bg-gray-200 rounded w-1/2"></div>
						</div>
					))}
				</div>
			</div>
		);
	}

	return (
		<div className="space-y-6">
			{/* Key Metrics */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
				<StatCard
					title="Total Equipment"
					count={dashboardData.stats.totalEquipment}
					icon={<Wrench className="h-6 w-6" />}
					onClick={() => onNavigateToTab("general-equipment")}
					subtitle={`${dashboardData.stats.availableEquipment} available`}
				/>
				<StatCard
					title="PPE Items"
					count={dashboardData.stats.totalPPEItems}
					icon={<HardHat className="h-6 w-6" />}
					onClick={() => onNavigateToTab("ppe-inventory")}
					subtitle={`${dashboardData.stats.lowStockPPE} low stock`}
					alertCount={dashboardData.stats.lowStockPPE}
				/>
				<StatCard
					title="Maintenance Required"
					count={dashboardData.stats.maintenanceRequired}
					icon={<AlertTriangle className="h-6 w-6" />}
					onClick={() => onNavigateToTab("maintenance")}
					alertCount={dashboardData.stats.maintenanceRequired}
				/>
				<StatCard
					title="Overdue Inspections"
					count={dashboardData.stats.overdueInspections}
					icon={<Clock className="h-6 w-6" />}
					onClick={() => onNavigateToTab("inspections")}
					alertCount={dashboardData.stats.overdueInspections}
				/>
			</div>

			{/* Equipment Status Overview */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				<div className="bg-white rounded-lg border border-gray-200 p-6">
					<h3 className="text-lg font-semibold mb-4 flex items-center">
						<Package className="h-5 w-5 mr-2" />
						Equipment Status
					</h3>
					<div className="space-y-3">
						<div className="flex justify-between items-center">
							<span className="text-sm text-gray-600">Available</span>
							<div className="flex items-center">
								<CheckCircle className="h-4 w-4 text-green-500 mr-1" />
								<span className="font-medium">
									{dashboardData.stats.availableEquipment}
								</span>
							</div>
						</div>
						<div className="flex justify-between items-center">
							<span className="text-sm text-gray-600">In Use</span>
							<div className="flex items-center">
								<div className="h-4 w-4 bg-blue-500 rounded-full mr-1"></div>
								<span className="font-medium">
									{dashboardData.stats.inUseEquipment}
								</span>
							</div>
						</div>
						<div className="flex justify-between items-center">
							<span className="text-sm text-gray-600">
								Maintenance Required
							</span>
							<div className="flex items-center">
								<XCircle className="h-4 w-4 text-red-500 mr-1" />
								<span className="font-medium">
									{dashboardData.stats.maintenanceRequired}
								</span>
							</div>
						</div>
					</div>
				</div>

				<div className="bg-white rounded-lg border border-gray-200 p-6">
					<h3 className="text-lg font-semibold mb-4 flex items-center">
						<TrendingUp className="h-5 w-5 mr-2" />
						Utilization Overview
					</h3>
					<div className="space-y-3">
						<div className="flex justify-between items-center">
							<span className="text-sm text-gray-600">Average Utilization</span>
							<span
								className={`font-medium ${getUtilizationColor(dashboardData.stats.averageUtilization)}`}
							>
								{dashboardData.stats.averageUtilization}%
							</span>
						</div>
						<div className="w-full bg-gray-200 rounded-full h-2">
							<div
								className="bg-green-600 h-2 rounded-full"
								style={{ width: `${dashboardData.stats.averageUtilization}%` }}
							></div>
						</div>
						<div className="flex justify-between items-center text-sm text-gray-600">
							<span>Total Maintenance Cost</span>
							<span className="font-medium">
								${dashboardData.stats.totalMaintenanceCost.toLocaleString()}
							</span>
						</div>
					</div>
				</div>
			</div>

			{/* Quick Actions */}
			<div className="bg-white rounded-lg border border-gray-200 p-6">
				<h3 className="text-lg font-semibold mb-4">Quick Actions</h3>
				<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
					<QuickActionCard
						title="Add Equipment"
						icon={<Plus className="h-5 w-5" />}
						onClick={() => onNavigateToTab("general-equipment")}
					/>
					<QuickActionCard
						title="Assign PPE"
						icon={<HardHat className="h-5 w-5" />}
						onClick={() => onNavigateToTab("ppe-inventory")}
					/>
					<QuickActionCard
						title="Schedule Inspection"
						icon={<ClipboardCheck className="h-5 w-5" />}
						onClick={() => onNavigateToTab("inspections")}
					/>
					<QuickActionCard
						title="Scan QR Code"
						icon={<QrCode className="h-5 w-5" />}
						onClick={() => {
							/* TODO: Implement QR scanner */
						}}
					/>
				</div>
			</div>

			{/* Recent Activity */}
			<div className="bg-white rounded-lg border border-gray-200 p-6">
				<h3 className="text-lg font-semibold mb-4">
					Recent Equipment Activity
				</h3>
				<div className="space-y-3">
					<div className="flex items-center justify-between py-2 border-b border-gray-100">
						<div className="flex items-center">
							<div className="h-2 w-2 bg-green-500 rounded-full mr-3"></div>
							<span className="text-sm">
								Excavator EX-001 assigned to John Mwangi
							</span>
						</div>
						<span className="text-xs text-gray-500">2 hours ago</span>
					</div>
					<div className="flex items-center justify-between py-2 border-b border-gray-100">
						<div className="flex items-center">
							<div className="h-2 w-2 bg-yellow-500 rounded-full mr-3"></div>
							<span className="text-sm">
								Safety helmets restocked - 50 units added
							</span>
						</div>
						<span className="text-xs text-gray-500">4 hours ago</span>
					</div>
					<div className="flex items-center justify-between py-2 border-b border-gray-100">
						<div className="flex items-center">
							<div className="h-2 w-2 bg-blue-500 rounded-full mr-3"></div>
							<span className="text-sm">Crane CR-003 inspection completed</span>
						</div>
						<span className="text-xs text-gray-500">1 day ago</span>
					</div>
				</div>
			</div>
		</div>
	);
};

export default EquipmentDashboard;
