using HotChocolate.Types;

namespace GraphQLApi.GraphQL.Types
{
    /// <summary>
    /// Input type for document file uploads
    /// </summary>
    public class DocumentFileInput
    {
        /// <summary>
        /// Custom name/title for the document
        /// </summary>
        public string Name { get; set; } = string.Empty;

        /// <summary>
        /// The file to upload
        /// </summary>
        public IFile File { get; set; } = null!;

        /// <summary>
        /// Optional description for the document
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// Optional folder path within the bucket
        /// </summary>
        public string? FolderPath { get; set; }

        /// <summary>
        /// Whether the file should be publicly accessible
        /// </summary>
        public bool IsPublic { get; set; } = false;

        /// <summary>
        /// Optional expiration date for the file
        /// </summary>
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// Additional metadata as JSON string
        /// </summary>
        public string? AdditionalMetadata { get; set; }
    }
}
