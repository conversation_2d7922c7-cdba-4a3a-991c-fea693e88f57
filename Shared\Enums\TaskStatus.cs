﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace Shared.Enums
{
    public enum TaskStatus
    {
        TODO,
        IN_PROGRESS,
        COMPLETED,
        CANCELLED,
        ON_HOLD
    }

    public enum TaskPriority
    {
        <PERSON>OW,
        <PERSON><PERSON>UM,
        <PERSON>IGH,
        CRITICAL
    }

    public enum InspectionStatus
    {
        NOT_REQUIRED,
        PENDING,
        IN_PROGRESS,
        COMPLETED,
        FAILED,
        REWORK_REQUIRED
    }
}
