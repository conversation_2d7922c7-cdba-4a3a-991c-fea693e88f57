# Shared Design System Components

## Overview
This document outlines the design system components that should be consistent across both the main workforce management application and the Site Engineer mobile application.

## Core Design Tokens

### Color System
```css
/* Primary Brand Colors */
:root {
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-200: #bfdbfe;
  --primary-300: #93c5fd;
  --primary-400: #60a5fa;
  --primary-500: #3b82f6;  /* Main brand color */
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-800: #1e40af;
  --primary-900: #1e3a8a;
}

/* Status Colors */
:root {
  /* Success - Green */
  --success-50: #f0fdf4;
  --success-500: #22c55e;
  --success-600: #16a34a;
  --success-700: #15803d;

  /* Warning - Amber */
  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  --warning-700: #b45309;

  /* Error - Red */
  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;
  --error-700: #b91c1c;

  /* Info - Blue */
  --info-50: #f0f9ff;
  --info-500: #3b82f6;
  --info-600: #2563eb;
  --info-700: #1d4ed8;
}

/* Neutral Colors */
:root {
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
}
```

### Typography Scale
```css
/* Font Families */
.font-sans { font-family: 'Inter', system-ui, sans-serif; }
.font-mono { font-family: 'JetBrains Mono', monospace; }

/* Font Sizes - Mobile Optimized */
.text-xs { font-size: 0.75rem; line-height: 1rem; }      /* 12px */
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }  /* 14px */
.text-base { font-size: 1rem; line-height: 1.5rem; }     /* 16px */
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }  /* 18px */
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }   /* 20px */
.text-2xl { font-size: 1.5rem; line-height: 2rem; }      /* 24px */
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; } /* 30px */

/* Font Weights */
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
```

### Spacing System
```css
/* Consistent 4px grid system */
.space-1 { margin: 0.25rem; }   /* 4px */
.space-2 { margin: 0.5rem; }    /* 8px */
.space-3 { margin: 0.75rem; }   /* 12px */
.space-4 { margin: 1rem; }      /* 16px */
.space-6 { margin: 1.5rem; }    /* 24px */
.space-8 { margin: 2rem; }      /* 32px */
.space-12 { margin: 3rem; }     /* 48px */
.space-16 { margin: 4rem; }     /* 64px */
```

## Shared Component Library

### Button Components
```typescript
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'success' | 'warning' | 'error' | 'ghost';
  size: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  loading?: boolean;
  fullWidth?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
}

// Usage Examples:
<Button variant="primary" size="lg">Create Permit</Button>
<Button variant="success" size="md">Approve Task</Button>
<Button variant="warning" size="sm">Report Issue</Button>
```

### Status Badge Components
```typescript
interface StatusBadgeProps {
  status: 'active' | 'pending' | 'approved' | 'rejected' | 'completed' | 'draft';
  size: 'sm' | 'md' | 'lg';
  showIcon?: boolean;
}

// Permit Status Badge
<PermitStatusBadge status="approved" size="sm" showIcon />

// Task Status Badge  
<TaskStatusBadge status="in-progress" size="md" />

// Worker Status Badge
<WorkerStatusBadge status="on-site" size="sm" />
```

### Form Components
```typescript
// Input Field
interface InputProps {
  label: string;
  placeholder?: string;
  required?: boolean;
  error?: string;
  disabled?: boolean;
  type?: 'text' | 'email' | 'password' | 'number' | 'tel';
}

// Select Dropdown
interface SelectProps {
  label: string;
  options: { value: string; label: string; }[];
  placeholder?: string;
  required?: boolean;
  error?: string;
}

// Textarea
interface TextareaProps {
  label: string;
  placeholder?: string;
  rows?: number;
  required?: boolean;
  error?: string;
}
```

### Card Components
```typescript
interface CardProps {
  title?: string;
  subtitle?: string;
  children: React.ReactNode;
  actions?: React.ReactNode;
  className?: string;
  clickable?: boolean;
  onClick?: () => void;
}

// Usage:
<Card title="Worker Details" subtitle="John Doe - Electrician">
  <WorkerInfo worker={worker} />
</Card>
```

### Modal Components
```typescript
interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  size: 'sm' | 'md' | 'lg' | 'xl' | 'full';
  children: React.ReactNode;
  footer?: React.ReactNode;
}

// Mobile-optimized modal for Site Engineer app
<Modal isOpen={isOpen} onClose={onClose} title="Create Task" size="full">
  <TaskCreationForm />
</Modal>
```

## Mobile-Specific Design Patterns

### Touch Targets
```css
/* Minimum touch target size for mobile */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Button touch targets */
.btn-touch {
  padding: 12px 24px;
  min-height: 48px;
}
```

### Mobile Navigation
```typescript
// Bottom Tab Navigation
interface TabNavigationProps {
  tabs: {
    id: string;
    label: string;
    icon: React.ReactNode;
    badge?: number;
  }[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

// Mobile Header
interface MobileHeaderProps {
  title: string;
  showBack?: boolean;
  onBack?: () => void;
  actions?: React.ReactNode;
}
```

### Swipe Actions
```typescript
interface SwipeActionProps {
  children: React.ReactNode;
  leftActions?: {
    icon: React.ReactNode;
    label: string;
    color: string;
    onAction: () => void;
  }[];
  rightActions?: {
    icon: React.ReactNode;
    label: string;
    color: string;
    onAction: () => void;
  }[];
}
```

## Icon System

### Consistent Icon Usage
```typescript
// Use Lucide React icons consistently across both apps
import {
  Users,           // Workers/Team
  CheckSquare,     // Tasks
  Shield,          // Permits/Safety
  FileText,        // Reports/Documents
  Clock,           // Time/Schedule
  MapPin,          // Location
  AlertTriangle,   // Warnings/Issues
  CheckCircle,     // Success/Completed
  XCircle,         // Error/Rejected
  Eye,             // View/Details
  Edit,            // Edit/Modify
  Plus,            // Add/Create
  Search,          // Search
  Filter,          // Filter
  Download,        // Export/Download
  Upload,          // Import/Upload
  Camera,          // Photo capture
  Phone,           // Contact
  Mail,            // Email
  Calendar,        // Date/Schedule
  Home,            // Dashboard
  Settings,        // Configuration
  LogOut,          // Logout
  Menu,            // Navigation menu
  ChevronRight,    // Navigation arrow
  ChevronLeft,     // Back navigation
  ChevronDown,     // Dropdown
  ChevronUp        // Collapse
} from 'lucide-react';

// Icon sizing standards
const iconSizes = {
  xs: 'h-3 w-3',    // 12px
  sm: 'h-4 w-4',    // 16px
  md: 'h-5 w-5',    // 20px
  lg: 'h-6 w-6',    // 24px
  xl: 'h-8 w-8',    // 32px
};
```

## Responsive Design Guidelines

### Breakpoint Strategy
```css
/* Mobile-first approach */
/* Default: Mobile (320px+) */
@media (min-width: 640px) { /* sm: Small tablets */ }
@media (min-width: 768px) { /* md: Tablets */ }
@media (min-width: 1024px) { /* lg: Small laptops */ }
@media (min-width: 1280px) { /* xl: Desktops */ }
```

### Layout Patterns
```typescript
// Mobile Stack Layout
<div className="space-y-4 p-4">
  <MobileHeader title="My Team" />
  <SearchBar />
  <WorkerList />
</div>

// Desktop Grid Layout  
<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
  <WorkerCard />
  <WorkerCard />
  <WorkerCard />
</div>
```

## Accessibility Standards

### WCAG 2.1 AA Compliance
- Color contrast ratio minimum 4.5:1
- Focus indicators on all interactive elements
- Keyboard navigation support
- Screen reader compatibility
- Touch target minimum 44x44px

### Implementation Examples
```typescript
// Accessible button
<button
  className="btn-primary focus:ring-2 focus:ring-blue-500 focus:outline-none"
  aria-label="Create new permit"
  disabled={isLoading}
>
  {isLoading ? <Spinner /> : <Plus />}
  Create Permit
</button>

// Accessible form field
<div className="form-field">
  <label htmlFor="worker-name" className="form-label">
    Worker Name *
  </label>
  <input
    id="worker-name"
    type="text"
    required
    aria-describedby="worker-name-error"
    className="form-input"
  />
  {error && (
    <p id="worker-name-error" className="form-error" role="alert">
      {error}
    </p>
  )}
</div>
```

This shared design system ensures consistency across both applications while providing the flexibility needed for mobile-optimized experiences in the Site Engineer app.
