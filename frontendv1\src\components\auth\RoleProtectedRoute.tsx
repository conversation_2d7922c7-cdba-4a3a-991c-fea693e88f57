import { Navigate } from "react-router-dom";
import { type UserType } from "../../types/auth";
import { ReactNode } from "react";
import { useUser } from "../../hooks/userContext";

interface RoleProtectedRouteType {
	children: ReactNode;
	allowedRoles: UserType["role"][];
	redirectTo?: string;
}

export function RoleProtectedRoute({
	children,
	allowedRoles,
	redirectTo = "/",
}: RoleProtectedRouteType) {
	const userData = useUser();
	if (!userData || !userData.user) {
		console.log("User is not defined");
		return <Navigate to={redirectTo} replace />;
	}
	if (!allowedRoles.includes(userData.user.role)) {
		console.log("user has no role in required role");
		return <Navigate to={redirectTo} replace />;
	}
	return <>{children}</>;
}
