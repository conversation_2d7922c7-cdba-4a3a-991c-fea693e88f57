 .u-section-1 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-1 .u-sheet-1 {
  min-height: 360px;
}

.u-section-1 .u-text-1 {
  margin: 20px 0 0;
}

.u-section-1 .u-icon-1 {
  font-size: 1.1112em;
}

.u-section-1 .u-btn-1 {
  border-style: solid;
  padding: 0;
}

.u-section-1 .u-layout-wrap-1 {
  margin-top: 5px;
  margin-bottom: 20px;
}

.u-section-1 .u-layout-cell-1 {
  min-height: 292px;
}

.u-section-1 .u-container-layout-1 {
  padding: 30px 0;
}

.u-section-1 .u-text-2 {
  margin: 0;
}

.u-section-1 .u-list-1 {
  margin-top: 20px;
  margin-bottom: 0;
}

.u-section-1 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 97px;
  grid-gap: 10px;
}

.u-section-1 .u-container-layout-2 {
  padding: 10px;
}

.u-section-1 .u-text-3 {
  margin: 0;
}

.u-section-1 .u-text-4 {
  margin: 5px 0 0;
}

.u-section-1 .u-container-layout-3 {
  padding: 10px;
}

.u-section-1 .u-text-5 {
  margin: 0;
}

.u-section-1 .u-text-6 {
  margin: 5px 0 0;
}

.u-section-1 .u-container-layout-4 {
  padding: 10px;
}

.u-section-1 .u-text-7 {
  margin: 0;
}

.u-section-1 .u-text-8 {
  margin: 5px 0 0;
}

.u-section-1 .u-layout-cell-2 {
  min-height: 292px;
}

.u-section-1 .u-container-layout-5 {
  padding: 30px;
}

.u-section-1 .u-text-9 {
  font-weight: 400;
  margin: 0;
}

@media (max-width: 1199px) {
  .u-section-1 .u-sheet-1 {
    min-height: 296px;
  }

  .u-section-1 .u-layout-wrap-1 {
    position: relative;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 243px;
  }

  .u-section-1 .u-text-2 {
    margin-right: 7px;
  }

  .u-section-1 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 80px;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 243px;
  }
}

@media (max-width: 991px) {
  .u-section-1 .u-sheet-1 {
    min-height: 185px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 290px;
  }

  .u-section-1 .u-text-2 {
    margin-right: 0;
  }

  .u-section-1 .u-repeater-1 {
    grid-auto-columns: calc(33.3333% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
    min-height: 86px;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 100px;
  }
}

@media (max-width: 767px) {
  .u-section-1 .u-layout-cell-1 {
    min-height: 235px;
  }

  .u-section-1 .u-repeater-1 {
    min-height: 76px;
  }

  .u-section-1 .u-container-layout-2 {
    padding-bottom: 9px;
  }

  .u-section-1 .u-container-layout-3 {
    padding-bottom: 9px;
  }

  .u-section-1 .u-container-layout-4 {
    padding-bottom: 9px;
  }

  .u-section-1 .u-container-layout-5 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-1 .u-layout-cell-1 {
    min-height: 244px;
  }

  .u-section-1 .u-repeater-1 {
    min-height: 98px;
  }

  .u-section-1 .u-container-layout-4 {
    padding-bottom: 5px;
  }
} .u-section-2 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-2 .u-sheet-1 {
  min-height: 360px;
}

.u-section-2 .u-layout-wrap-1 {
  margin-top: 60px;
  margin-bottom: 60px;
  position: relative;
}

.u-section-2 .u-layout-cell-1 {
  min-height: 420px;
  background-repeat: no-repeat;
  background-size: cover;
  --radius: 30px;
}

.u-section-2 .u-image-1 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: url("../images/sketch.webp");
  background-size: cover;
}

.u-section-2 .u-container-layout-1 {
  padding: 30px;
}

.u-section-2 .u-layout-cell-2 {
  min-height: 420px;
  --radius: 30px;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 1), rgba(249, 240, 248, 1));
  background-size: cover;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-container-layout-2 {
  box-shadow: 5px 5px 8px 0 rgba(204,211,219,0.4);
  padding: 30px;
}

.u-section-2 .u-text-1 {
  font-size: 2.25rem;
  margin: 0 auto 0 0;
}

.u-section-2 .u-text-2 {
  font-weight: 400;
  margin: 20px 0 0;
}

@media (max-width: 1199px) {
  .u-section-2 .u-sheet-1 {
    min-height: 293px;
  }

  .u-section-2 .u-layout-cell-1 {
    min-height: 350px;
    background-position: 50% 50%;
  }

  .u-section-2 .u-layout-cell-2 {
    min-height: 350px;
  }
}

@media (max-width: 991px) {
  .u-section-2 .u-sheet-1 {
    min-height: 220px;
  }

  .u-section-2 .u-layout-cell-1 {
    min-height: 273px;
  }

  .u-section-2 .u-layout-cell-2 {
    min-height: 100px;
  }
}

@media (max-width: 767px) {
  .u-section-2 .u-sheet-1 {
    min-height: 680px;
  }

  .u-section-2 .u-layout-cell-1 {
    min-height: 378px;
  }

  .u-section-2 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-2 .u-sheet-1 {
    min-height: 414px;
  }

  .u-section-2 .u-layout-cell-1 {
    min-height: 238px;
  }

  .u-section-2 .u-text-1 {
    font-size: 1.875rem;
  }
}

.u-section-2 .u-layout-cell-2,
.u-section-2 .u-layout-cell-2:before,
.u-section-2 .u-layout-cell-2 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 .u-layout-cell-2.u-layout-cell-2.u-layout-cell-2:hover {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-layout-cell-2.u-layout-cell-2.u-layout-cell-2.hover {
  transform: translateX(0px) translateY(-5px) !important;
} .u-section-3 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-3 .u-sheet-1 {
  min-height: 360px;
  margin-bottom: 0;
}

.u-section-3 .u-gallery-1 {
  height: 600px;
  margin-top: 30px;
  margin-bottom: 60px;
  width: 100%;
}

.u-section-3 .u-gallery-inner-1 {
  grid-template-columns: repeat(3, auto);
  grid-gap: 10px;
}

.u-section-3 .u-over-slide-1 {
  background-image: linear-gradient(0deg, rgba(0,0,0,0.2), rgba(0,0,0,0.2));
  padding: 20px;
}

.u-section-3 .u-over-slide-2 {
  background-image: linear-gradient(0deg, rgba(0,0,0,0.2), rgba(0,0,0,0.2));
  padding: 20px;
}

.u-section-3 .u-over-slide-3 {
  background-image: linear-gradient(0deg, rgba(0,0,0,0.2), rgba(0,0,0,0.2));
  padding: 20px;
}

.u-section-3 .u-over-slide-4 {
  background-image: linear-gradient(0deg, rgba(0,0,0,0.2), rgba(0,0,0,0.2));
  padding: 20px;
}

.u-section-3 .u-over-slide-5 {
  background-image: linear-gradient(0deg, rgba(0,0,0,0.2), rgba(0,0,0,0.2));
  padding: 20px;
}

.u-section-3 .u-over-slide-6 {
  background-image: linear-gradient(0deg, rgba(0,0,0,0.2), rgba(0,0,0,0.2));
  padding: 20px;
}

@media (max-width: 991px) {
  .u-section-3 .u-sheet-1 {
    min-height: 1110px;
  }

  .u-section-3 .u-gallery-1 {
    height: 1350px;
  }

  .u-section-3 .u-gallery-inner-1 {
    grid-template-columns: repeat(2, auto);
  }
}

@media (max-width: 767px) {
  .u-section-3 .u-sheet-1 {
    min-height: 2090px;
  }

  .u-section-3 .u-gallery-1 {
    height: 2000px;
    margin-bottom: -3010px;
  }

  .u-section-3 .u-gallery-inner-1 {
    grid-template-columns: repeat(1, auto);
  }
}

@media (max-width: 575px) {
  .u-section-3 .u-sheet-1 {
    min-height: 1290px;
  }

  .u-section-3 .u-gallery-1 {
    height: 1200px;
    margin-bottom: -3810px;
  }
}