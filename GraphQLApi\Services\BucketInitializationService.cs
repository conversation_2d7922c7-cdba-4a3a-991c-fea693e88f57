using Microsoft.Extensions.Options;
using Shared.Configuration;

namespace GraphQLApi.Services
{
    /// <summary>
    /// Service for initializing MinIO buckets at application startup
    /// </summary>
    public class BucketInitializationService : IBucketInitializationService
    {
        private readonly IMinioService _minioService;
        private readonly MinIOConfiguration _config;
        private readonly ILogger<BucketInitializationService> _logger;

        public BucketInitializationService(
            IMinioService minioService,
            IOptions<MinIOConfiguration> config,
            ILogger<BucketInitializationService> logger)
        {
            _minioService = minioService;
            _config = config.Value;
            _logger = logger;
        }

        /// <summary>
        /// Initialize all required buckets for the application
        /// </summary>
        /// <returns>True if all buckets were initialized successfully</returns>
        public async Task<bool> InitializeAsync()
        {
            try
            {
                _logger.LogInformation("Starting bucket initialization...");

                // Check if auto-creation is enabled
                if (!_config.Settings.AutoCreateBuckets)
                {
                    _logger.LogInformation("Auto bucket creation is disabled. Skipping bucket initialization.");
                    return true;
                }

                // Initialize buckets using MinIO service
                var result = await _minioService.InitializeBucketsAsync();

                if (result)
                {
                    _logger.LogInformation("Bucket initialization completed successfully");
                }
                else
                {
                    _logger.LogError("Bucket initialization failed");
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error during bucket initialization");
                return false;
            }
        }
    }
}
