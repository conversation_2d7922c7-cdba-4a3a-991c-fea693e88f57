/* Leadership Team CSS */

/* Base Styles */
:root {
  --primary-color: #84277F;
  --header-bg: #E8C7E5;
  --accent-color: #F9F0F8;
  --page-bg: #fcf7fb;
  --white: #ffffff;
  --gray: #f0f0f0;
  --dark-gray: #333333;
  --shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

/* Accessibility - Screen Reader Only */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.sr-only-focusable:active,
.sr-only-focusable:focus {
  position: static;
  width: auto;
  height: auto;
  overflow: visible;
  clip: auto;
  white-space: normal;
}

html, body {
  font-family: 'Figtree', sans-serif;
  background-color: var(--page-bg);
  margin: 0;
  padding: 0;
  height: auto;
  min-height: 100%;
  overflow-y: visible;
}

/* Special styling for touchpad users */
body.using-touchpad {
  overflow-y: visible !important;
  height: auto !important;
}

/* When modal is open, prevent background scrolling */
body.modal-open {
  overflow: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0.5rem 1rem 2rem 1rem; /* Reduced top padding, kept other padding values */
}

/* Leadership Section */
.leadership-section {
  padding-top: 20px; /* Reduced from 40px to 20px as requested */
  padding-bottom: 2rem;
  background-color: var(--page-bg);
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-size: cover;
}

.leadership-title {
  font-family: 'Figtree', sans-serif;
  font-size: 36px;
  color: var(--primary-color);
  text-align: center;
  margin-bottom: 1.5rem;
}

/* Leaders Grid */
.leaders-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  min-height: 300px;
  width: 100%;
  height: auto;
  overflow: visible;
}

.leader-card {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: var(--transition);
  background-image: linear-gradient(180deg, rgba(249, 240, 248, 0.6), rgba(255, 255, 255, 1));
  padding: 30px;
  text-align: center;
  min-height: 400px;
  cursor: pointer; /* Add cursor pointer to indicate clickability */
}

/* Card hover effect removed as requested */

.leader-image-container {
  width: 100%;
  height: 0;
  padding-bottom: 100%; /* Creates a square container */
  position: relative;
  overflow: hidden;
  border-radius: 20px;
  margin-bottom: 20px;
}

.leader-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.leader-card:hover .leader-image {
  transform: scale(1.05);
}

.leader-name {
  font-family: 'Figtree', sans-serif;
  font-size: 24px;
  color: var(--primary-color);
  margin: 20px 0 10px;
}

.leader-designation {
  font-family: 'Open Sans', sans-serif;
  font-size: 18px;
  color: var(--dark-gray);
  margin: 0 0 20px;
}

.leader-cta {
  position: absolute;
  bottom: 20px;
  right: 20px;
  width: 40px;
  height: 40px;
  background-color: rgba(132, 39, 127, 0.7); /* Reduced opacity from solid color */
  color: var(--white);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 3px 8px rgba(132, 39, 127, 0.2);
  overflow: hidden;
  z-index: 2;
}

.leader-cta svg {
  width: 18px;
  height: 18px;
  transition: all 0.3s ease;
}

.leader-cta:hover {
  transform: translateY(-3px) scale(1.05);
  background-color: rgba(132, 39, 127, 0.85); /* Slightly darker but still semi-transparent */
  box-shadow: 0 5px 12px rgba(132, 39, 127, 0.3);
}

.leader-cta:hover svg {
  transform: scale(1.1);
}

.leader-cta:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(132, 39, 127, 0.2), 0 3px 8px rgba(132, 39, 127, 0.2);
  background-color: rgba(132, 39, 127, 0.75);
}

/* Loading State styles moved to dots-loading.css */

/* Loading spinner styles moved to dots-loading.css */

/* Error Message */
.error-message {
  text-align: center;
  padding: 2rem;
  background-color: #fff0f0;
  border-radius: 8px;
  margin: 2rem 0;
  display: none;
}

.retry-btn {
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: 5px;
  padding: 0.5rem 1.5rem;
  font-family: 'Figtree', sans-serif;
  cursor: pointer;
  transition: var(--transition);
}

.retry-btn:hover {
  background-color: #6b1f67;
}

/* Modal Styles */
.modal {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

.modal-content {
  background-color: var(--white);
  border-radius: 8px;
  width: 90%;
  max-width: 1200px;
  margin: 5vh auto;
  display: flex;
  flex-direction: column;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  max-height: 90vh;
  position: relative;
  border: none; /* Explicitly remove any border */
  outline: none; /* Remove outline as well */
}

.modal-inner {
  padding: 3rem 2rem 0;
  overflow-y: auto !important; /* Force overflow to be auto */
  flex-grow: 1;
  max-height: calc(90vh - 80px);
  -webkit-overflow-scrolling: touch !important; /* Force smooth scrolling on iOS */
  scroll-behavior: smooth;
  outline: none;
  position: relative;
  height: auto;
}

.close-modal {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 28px;
  font-weight: bold;
  color: var(--dark-gray);
  cursor: pointer;
  transition: transform 0.25s ease, opacity 0.25s ease;
  z-index: 1200;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  width: auto;
  height: auto;
  opacity: 0.7;
}

.close-modal:hover,
.close-modal:focus {
  color: #333333;
  transform: rotate(90deg);
  opacity: 1;
  outline: none;
}

.modal-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 2rem;
  padding-top: 0.5rem;
  padding-bottom: 1rem;
}

/* Left Panel */
.modal-left-panel {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  align-items: center;
  text-align: center;
}

.modal-leader-image-container {
  width: 100%;
  max-width: 400px;
  border-radius: 8px;
  overflow: hidden;
  margin: 0 auto;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  position: relative;
  height: auto;
}

.modal-leader-image {
  width: 100%;
  height: auto;
  display: block;
  border-radius: 8px;
  object-fit: cover;
  max-width: 100%;
}

.modal-social-links {
  display: flex;
  gap: 1.2rem;
  margin-top: 2rem;
  margin-bottom: 1.5rem;
  justify-content: center;
  padding: 0.5rem 0;
}

.social-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(132, 39, 127, 0.1);
  color: var(--primary-color);
  display: flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
  transition: all 0.3s ease;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.05);
  margin: 0 8px;
  border: 1px solid rgba(132, 39, 127, 0.2);
  font-size: 18px;
}

.social-icon:hover,
.social-icon:focus {
  background-color: var(--primary-color);
  color: white;
  transform: translateY(-3px);
  box-shadow: 0 4px 10px rgba(132, 39, 127, 0.3);
  border-color: transparent;
  outline: none;
}

/* Right Panel */
.modal-right-panel {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.modal-header {
  margin-bottom: 1rem;
}

.modal-leader-name {
  font-family: 'Figtree', sans-serif;
  font-size: 36px;
  color: #000000;
  margin: 0 0 0.5rem;
  padding-right: 40px; /* Add padding to prevent overlap with close button */
}

.modal-designation {
  font-family: 'Open Sans', sans-serif;
  font-size: 20px;
  color: var(--primary-color);
  margin: 0;
}

.modal-bio {
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 1.5rem;
}

.modal-section {
  margin-bottom: 1.5rem;
}

.modal-section h3 {
  font-family: 'Figtree', sans-serif;
  font-size: 20px;
  color: #000000;
  margin: 0 0 0.75rem;
  border-bottom: 1px solid var(--accent-color);
  padding-bottom: 0.5rem;
}

.modal-section ul {
  padding-left: 1.5rem;
  margin: 0;
}

.modal-section li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
}

/* Metrics Sidebar */
.modal-metrics {
  background-color: var(--accent-color);
  border-radius: 8px;
  padding: 1.5rem;
  margin-top: 1rem;
}

.metric-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 1rem;
}

.metric-label {
  font-size: 14px;
  color: #777777;
  margin-bottom: 0.25rem;
}

.metric-value {
  font-size: 18px;
  font-weight: 600;
  color: var(--primary-color);
}

/* Modal Navigation */
.modal-navigation {
  display: flex;
  justify-content: space-between;
  padding: 1rem 2rem;
  background-color: #f8f8f8;
  border-top: 1px solid #eee;
  position: sticky;
  bottom: 0;
  width: 100%;
  z-index: 10;
  height: 80px;
}

.nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--accent-color);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: 50px;
  padding: 0.75rem 1.25rem;
  font-family: 'Figtree', sans-serif;
  font-size: 14px;
  cursor: pointer;
  transition: var(--transition);
  min-width: 120px;
}

.nav-btn:hover:not(.disabled) {
  background-color: var(--primary-color);
  color: var(--white);
}

.nav-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.nav-arrow {
  font-size: 24px;
  margin: 0 0.5rem;
  font-weight: bold;
}

/* Social Media Icons */
.icon-linkedin::before {
  content: "in";
  font-weight: bold;
  font-style: normal;
  font-family: 'Arial', sans-serif;
}

.icon-twitter::before {
  content: "𝕏";
  font-weight: bold;
  font-style: normal;
}

.icon-facebook::before {
  content: "f";
  font-weight: bold;
  font-style: normal;
  font-family: 'Arial', sans-serif;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .leaders-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .modal-grid {
    grid-template-columns: 1fr 1.5fr;
  }
}

@media (max-width: 768px) {
  .modal-grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }

  .modal-left-panel {
    order: 1;
  }

  .modal-right-panel {
    order: 2;
  }

  .modal-leader-image-container {
    max-width: 100%;
    width: 100%;
    max-height: 500px;
    overflow: hidden;
  }

  .modal-leader-image {
    width: 100%;
    max-height: 500px;
    object-fit: contain;
  }

  .modal-inner {
    padding: 2.5rem 1rem 0;
    max-height: calc(90vh - 60px);
  }

  .close-modal {
    top: 15px;
    right: 15px;
    font-size: 24px;
    opacity: 0.7;
  }

  .modal-leader-name {
    font-size: 28px;
    padding-right: 40px;
  }

  .modal-designation {
    font-size: 18px;
  }

  .modal-navigation {
    padding: 0.75rem 1rem;
    height: 60px;
  }

  .nav-btn {
    padding: 0.5rem;
    min-width: 80px;
  }

  .nav-text {
    display: none;
  }
}

@media (max-width: 576px) {
  .leaders-grid {
    grid-template-columns: 1fr;
  }

  .leader-card {
    min-height: 350px;
  }

  .modal-inner {
    padding: 2rem 0.75rem 0;
    max-height: calc(90vh - 50px);
  }

  .close-modal {
    top: 10px;
    right: 10px;
    font-size: 22px;
    opacity: 0.7;
  }

  .modal-leader-name {
    font-size: 22px;
    padding-right: 30px;
  }

  .modal-designation {
    font-size: 16px;
  }

  .modal-section h3 {
    font-size: 18px;
  }

  .modal-navigation {
    padding: 0.5rem 0.75rem;
    height: 50px;
  }

  .modal-leader-image-container {
    max-height: 350px;
  }

  .modal-leader-image {
    max-height: 350px;
  }
}
