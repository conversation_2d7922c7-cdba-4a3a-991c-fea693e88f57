/**
 * Scroll Fix Utility
 * Helps ensure consistent scrolling behavior across browsers and devices
 */

// Check if smooth scrolling is supported natively
if (!('scrollBehavior' in document.documentElement.style)) {
  // Simple polyfill for smooth scrolling
  window.smoothScroll = function(element, to, duration) {
    const start = element.scrollTop;
    const change = to - start;
    const startTime = performance.now();

    function animateScroll(currentTime) {
      const elapsedTime = currentTime - startTime;

      if (elapsedTime > duration) {
        element.scrollTop = to;
        return;
      }

      const progress = elapsedTime / duration;
      const easeInOutCubic = progress < 0.5
        ? 4 * progress * progress * progress
        : 1 - Math.pow(-2 * progress + 2, 3) / 2;

      element.scrollTop = start + change * easeInOutCubic;
      requestAnimationFrame(animateScroll);
    }

    requestAnimationFrame(animateScroll);
  };
}

// Fix for iOS Safari and other mobile browsers
document.addEventListener('DOMContentLoaded', function() {
  console.log('Scroll fix utility loaded');

  // Fix for main page scrolling
  document.addEventListener('touchstart', function() {}, { passive: true });
  document.addEventListener('touchmove', function() {}, { passive: true });

  // Add passive touch listeners to improve scrolling performance
  const modalInner = document.querySelector('.modal-inner');
  if (modalInner) {
    // These empty handlers with passive: true help iOS recognize the element as scrollable
    modalInner.addEventListener('touchstart', function() {}, { passive: true });
    modalInner.addEventListener('touchmove', function() {}, { passive: true });
  }

  // Fix for iOS momentum scrolling
  const modal = document.querySelector('.modal');
  if (modal) {
    modal.addEventListener('touchmove', function(e) {
      // Don't prevent default to allow natural scrolling
    }, { passive: true });
  }

  // Fix for touchpad scrolling in modal
  document.addEventListener('wheel', function(e) {
    const modalInner = document.querySelector('.modal-inner');
    const modal = document.querySelector('.modal');

    // If modal is open and the wheel event is inside the modal
    if (modal && modal.style.display === 'block' && modalInner) {
      // Let the browser handle the scrolling naturally
      console.log('Wheel event detected in modal');
    }
  }, { passive: true });

  // Ensure projects container is properly sized
  setTimeout(function() {
    const projectsContainer = document.getElementById('projects-container');
    if (projectsContainer) {
      console.log('Ensuring projects container is scrollable');
      // Force a reflow to ensure proper height calculation
      projectsContainer.style.minHeight = projectsContainer.scrollHeight + 'px';
    }
  }, 1500); // Wait a bit longer than the initial timeout in projects.js
});

// Detect touchpad vs mouse wheel
let isUsingTouchpad = false;
let eventCount = 0;
let eventTimeout = null;

function wheelEventHandler(e) {
  // Delta mode 0 is pixels, 1 is lines, 2 is pages
  if (e.deltaMode === 0) {
    eventCount++;

    // If we get multiple pixel-precise events, it's likely a touchpad
    if (eventCount > 5) {
      isUsingTouchpad = true;
      document.body.classList.add('using-touchpad');

      // Make sure the page is scrollable with touchpad
      document.documentElement.style.height = 'auto';
      document.body.style.height = 'auto';
      document.body.style.overflowY = 'visible';
    }

    clearTimeout(eventTimeout);
    eventTimeout = setTimeout(function() {
      eventCount = 0;
    }, 100);
  } else {
    // If delta mode is 1 or 2, it's likely a mouse wheel
    isUsingTouchpad = false;
    document.body.classList.remove('using-touchpad');
  }
}

// Add wheel event listener to detect touchpad
window.addEventListener('wheel', wheelEventHandler, { passive: true });

// Handle window resize events to fix scrolling issues
window.addEventListener('resize', function() {
  console.log('Window resized - updating document height');
  // Force document to recalculate its height
  document.body.style.height = 'auto';
  document.documentElement.style.height = 'auto';

  // Update projects container height if it exists
  const projectsContainer = document.getElementById('projects-container');
  if (projectsContainer) {
    projectsContainer.style.minHeight = 'auto';
    // Force a reflow
    void projectsContainer.offsetHeight;
    // Set min-height to ensure all content is visible
    projectsContainer.style.minHeight = projectsContainer.scrollHeight + 'px';
  }
});

// Export for debugging
window.isUsingTouchpad = function() {
  return isUsingTouchpad;
};
