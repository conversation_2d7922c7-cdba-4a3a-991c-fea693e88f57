 .u-section-1 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-1 .u-sheet-1 {
  min-height: 651px;
}

.u-section-1 .u-text-1 {
  margin: 20px 0 0;
}

.u-section-1 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: 0;
}

.u-section-1 .u-layout-cell-1 {
  min-height: 107px;
}

.u-section-1 .u-container-layout-1 {
  padding: 0;
}

.u-section-1 .u-text-2 {
  font-weight: 500;
  margin: 0;
}

.u-section-1 .u-layout-cell-2 {
  min-height: 504px;
}

.u-section-1 .u-container-layout-2 {
  padding: 30px;
}

.u-section-1 .u-image-1 {
  width: 570px;
  height: 444px;
  --radius: 20px;
  margin: 0 auto;
}

.u-section-1 .u-layout-cell-3 {
  min-height: 611px;
}

.u-section-1 .u-container-layout-3 {
  padding: 30px;
}

.u-section-1 .u-group-1 {
  --radius: 20px;
  min-height: 473px;
  height: auto;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-1 .u-container-layout-4 {
  padding: 20px;
}

.u-section-1 .u-image-2 {
  height: 356px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 20px;
  filter: brightness(0.75);
}

.u-section-1 .u-text-3 {
  margin-top: 20px;
  margin-bottom: 0;
  font-weight: 400;
  font-size: 1.5rem;
}

.u-section-1 .u-btn-1 {
  margin: 11px auto 0 0;
  padding: 0;
}

@media (max-width: 1199px) {
  .u-section-1 .u-sheet-1 {
    min-height: 549px;
  }

  .u-section-1 .u-layout-wrap-1 {
    position: relative;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 89px;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 374px;
  }

  .u-section-1 .u-image-1 {
    width: 557px;
    height: 434px;
  }

  .u-section-1 .u-layout-cell-3 {
    min-height: 509px;
  }

  .u-section-1 .u-group-1 {
    height: auto;
  }

  .u-section-1 .u-image-2 {
    transition-duration: 0.5s;
    height: 291px;
  }
}

@media (max-width: 991px) {
  .u-section-1 .u-sheet-1 {
    min-height: 669px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 450px;
  }

  .u-section-1 .u-layout-cell-3 {
    min-height: 100px;
  }

  .u-section-1 .u-image-2 {
    height: 627px;
  }
}

@media (max-width: 767px) {
  .u-section-1 .u-sheet-1 {
    min-height: 537px;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 312px;
  }

  .u-section-1 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-image-1 {
    width: 520px;
    height: 405px;
  }

  .u-section-1 .u-layout-cell-3 {
    min-height: 578px;
  }

  .u-section-1 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-group-1 {
    min-height: 518px;
  }

  .u-section-1 .u-container-layout-4 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-image-2 {
    height: 441px;
  }
}

@media (max-width: 575px) {
  .u-section-1 .u-sheet-1 {
    min-height: 427px;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 196px;
  }

  .u-section-1 .u-image-1 {
    width: 320px;
    height: 249px;
  }

  .u-section-1 .u-layout-cell-3 {
    min-height: 100px;
  }

  .u-section-1 .u-group-1 {
    min-height: 387px;
  }

  .u-section-1 .u-container-layout-4 {
    padding-top: 10px;
  }

  .u-section-1 .u-image-2 {
    height: 271px;
  }
} .u-section-2 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-2 .u-sheet-1 {
  min-height: 594px;
}

.u-section-2 .u-list-1 {
  margin-bottom: 13px;
  margin-top: 10px;
}

.u-section-2 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 13.3333px);
  grid-template-columns: repeat(3, calc(33.3333% - 13.3333px));
  min-height: 564px;
  grid-gap: 20px;
}

.u-section-2 .u-list-item-1 {
  --radius: 20px;
}

.u-section-2 .u-container-layout-1 {
  padding: 30px;
}

.u-section-2 .u-image-1 {
  height: 300px;
  --radius: 20px;
  width: 100%;
  margin: 0;
}

.u-section-2 .u-text-1 {
  margin: 20px 0 0;
}

.u-section-2 .u-list-item-2 {
  --radius: 20px;
}

.u-section-2 .u-container-layout-2 {
  padding: 30px;
}

.u-section-2 .u-image-2 {
  height: 300px;
  --radius: 20px;
  width: 100%;
  margin: 0;
}

.u-section-2 .u-text-2 {
  margin: 20px 0 0;
}

.u-section-2 .u-list-item-3 {
  --radius: 20px;
}

.u-section-2 .u-container-layout-3 {
  padding: 30px;
}

.u-section-2 .u-image-3 {
  height: 300px;
  --radius: 20px;
  width: 100%;
  margin: 0;
}

.u-section-2 .u-text-3 {
  margin: 20px 0 0;
}

.u-section-2 .u-list-item-4 {
  --radius: 20px;
}

.u-section-2 .u-container-layout-4 {
  padding: 30px;
}

.u-section-2 .u-image-4 {
  height: 300px;
  --radius: 20px;
  width: 100%;
  margin: 0;
}

.u-section-2 .u-text-4 {
  margin: 20px 0 0;
}

.u-section-2 .u-list-item-5 {
  --radius: 20px;
}

.u-section-2 .u-container-layout-5 {
  padding: 30px;
}

.u-section-2 .u-image-5 {
  height: 300px;
  --radius: 20px;
  width: 100%;
  margin: 0;
}

.u-section-2 .u-text-5 {
  margin: 20px 0 0;
}

.u-section-2 .u-list-item-6 {
  --radius: 20px;
}

.u-section-2 .u-container-layout-6 {
  padding: 30px;
}

.u-section-2 .u-image-6 {
  height: 300px;
  --radius: 20px;
  width: 100%;
  margin: 0;
}

.u-section-2 .u-text-6 {
  margin: 20px 0 0;
}

@media (max-width: 1199px) {
  .u-section-2 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 13.3333px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 13.3333px));
    min-height: 470px;
  }
}

@media (max-width: 991px) {
  .u-section-2 .u-repeater-1 {
    grid-auto-columns: calc(50% - 9.999975px);
    grid-template-columns: repeat(2, calc(50% - 9.999975px));
    min-height: 842px;
  }
}

@media (max-width: 767px) {
  .u-section-2 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-2 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-2 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-2 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-2 .u-container-layout-4 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-2 .u-container-layout-5 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-2 .u-container-layout-6 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-2 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
} .u-section-3 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-3 .u-sheet-1 {
  min-height: 660px;
}

.u-section-3 .u-layout-wrap-1 {
  margin-top: 60px;
  margin-bottom: 60px;
}

.u-section-3 .u-layout-cell-1 {
  min-height: 642px;
}

.u-section-3 .u-container-layout-1 {
  padding: 0;
}

.u-section-3 .u-image-1 {
  height: 392px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 10px;
  filter: brightness(0.75);
}

.u-section-3 .u-line-1 {
  transform-origin: left center;
  border-style: solid;
  margin: 20px 0 0;
}

.u-section-3 .u-text-1 {
  margin: 20px auto 0 0;
}

.u-section-3 .u-text-2 {
  margin: 10px auto 0 0;
}

.u-section-3 .u-btn-1 {
  margin: 20px 0 0;
  padding: 0;
}

.u-section-3 .u-btn-2 {
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-3 .u-layout-cell-2 {
  min-height: 335px;
}

.u-section-3 .u-container-layout-2 {
  padding: 0;
}

.u-section-3 .u-image-2 {
  height: 172px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 10px;
}

.u-section-3 .u-text-3 {
  font-size: 1.25rem;
  margin: 20px 0 0;
}

.u-section-3 .u-text-4 {
  font-size: 1rem;
  margin: 10px 0 0;
}

.u-section-3 .u-layout-cell-3 {
  min-height: 335px;
}

.u-section-3 .u-container-layout-3 {
  padding: 0;
}

.u-section-3 .u-image-3 {
  height: 172px;
  --radius: 10px;
  margin: 0 auto 0 20px;
}

.u-section-3 .u-text-5 {
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-3 .u-text-6 {
  font-size: 1rem;
  margin: 10px 10px 0 auto;
}

.u-section-3 .u-layout-cell-4 {
  min-height: 307px;
}

.u-section-3 .u-container-layout-4 {
  padding: 0;
}

.u-section-3 .u-image-4 {
  height: 172px;
  --radius: 10px;
  margin: 0 auto 0 20px;
}

.u-section-3 .u-text-7 {
  font-size: 1.25rem;
  margin: 20px 20px 0 0;
}

.u-section-3 .u-text-8 {
  font-size: 1rem;
  margin: 10px 0 0;
}

.u-section-3 .u-layout-cell-5 {
  min-height: 225px;
}

.u-section-3 .u-container-layout-5 {
  padding: 0;
}

.u-section-3 .u-image-5 {
  height: 172px;
  --radius: 10px;
  margin: 0 auto 0 20px;
}

.u-section-3 .u-text-9 {
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-3 .u-text-10 {
  font-size: 1rem;
  margin: 10px 0 0;
}

@media (max-width: 1199px) {
  .u-section-3 .u-sheet-1 {
    min-height: 594px;
  }

  .u-section-3 .u-layout-wrap-1 {
    position: relative;
  }

  .u-section-3 .u-layout-cell-1 {
    min-height: 535px;
  }

  .u-section-3 .u-image-1 {
    height: 327px;
  }

  .u-section-3 .u-line-1 {
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-3 .u-layout-cell-2 {
    min-height: 279px;
  }

  .u-section-3 .u-image-2 {
    height: 143px;
  }

  .u-section-3 .u-layout-cell-3 {
    min-height: 279px;
  }

  .u-section-3 .u-image-3 {
    height: 143px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-3 .u-layout-cell-4 {
    min-height: 256px;
  }

  .u-section-3 .u-image-4 {
    height: 143px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-3 .u-text-7 {
    margin-right: 0;
  }

  .u-section-3 .u-layout-cell-5 {
    min-height: 188px;
  }

  .u-section-3 .u-image-5 {
    height: 143px;
    margin-right: initial;
    margin-left: initial;
  }
}

@media (max-width: 991px) {
  .u-section-3 .u-sheet-1 {
    min-height: 1301px;
  }

  .u-section-3 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-3 .u-image-1 {
    height: 510px;
  }

  .u-section-3 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-3 .u-image-2 {
    height: 223px;
  }

  .u-section-3 .u-layout-cell-3 {
    min-height: 100px;
  }

  .u-section-3 .u-image-3 {
    height: 223px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-3 .u-layout-cell-4 {
    min-height: 100px;
  }

  .u-section-3 .u-image-4 {
    height: 223px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-3 .u-layout-cell-5 {
    min-height: 100px;
  }

  .u-section-3 .u-image-5 {
    height: 223px;
    margin-right: initial;
    margin-left: initial;
  }
}

@media (max-width: 767px) {
  .u-section-3 .u-sheet-1 {
    min-height: 2063px;
  }

  .u-section-3 .u-image-1 {
    height: 353px;
  }

  .u-section-3 .u-image-2 {
    height: 309px;
  }

  .u-section-3 .u-image-3 {
    height: 309px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-3 .u-image-4 {
    height: 309px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-3 .u-image-5 {
    height: 309px;
    margin-right: initial;
    margin-left: initial;
  }
}

@media (max-width: 575px) {
  .u-section-3 .u-sheet-1 {
    min-height: 1394px;
  }

  .u-section-3 .u-image-1 {
    height: 222px;
  }

  .u-section-3 .u-image-2 {
    height: 195px;
  }

  .u-section-3 .u-image-3 {
    height: 195px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-3 .u-image-4 {
    height: 195px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-3 .u-image-5 {
    height: 195px;
    margin-right: initial;
    margin-left: initial;
  }
}