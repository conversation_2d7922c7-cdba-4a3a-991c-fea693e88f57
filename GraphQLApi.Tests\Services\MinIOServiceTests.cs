using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using Xunit;
using GraphQLApi.Services;
using GraphQLApi.Data;
using Shared.Configuration;
using Shared.Enums;
using System.Text;
using Task = System.Threading.Tasks.Task;

namespace GraphQLApi.Tests.Services
{
    /// <summary>
    /// Unit tests for MinIO service configuration and validation logic
    /// Note: These tests focus on business logic rather than MinIO client integration
    /// </summary>
    public class MinIOServiceTests : IDisposable
    {
        private readonly Mock<ILogger<MinioService>> _mockLogger;
        private readonly Mock<IOptions<MinIOConfiguration>> _mockConfig;
        private readonly Mock<IDbContextFactory<AppDbContext>> _mockContextFactory;
        private readonly AppDbContext _context;
        private readonly MinIOConfiguration _configuration;

        public MinIOServiceTests()
        {
            _mockLogger = new Mock<ILogger<MinioService>>();
            _mockConfig = new Mock<IOptions<MinIOConfiguration>>();
            _mockContextFactory = new Mock<IDbContextFactory<AppDbContext>>();

            // Setup in-memory database
            var options = new DbContextOptionsBuilder<AppDbContext>()
                .UseInMemoryDatabase(databaseName: Guid.NewGuid().ToString())
                .Options;
            _context = new AppDbContext(options);

            // Setup configuration
            _configuration = new MinIOConfiguration
            {
                Endpoint = "localhost:9000",
                AccessKey = "test",
                SecretKey = "test",
                UseSSL = false,
                Region = "us-east-1",
                Buckets = new BucketConfiguration
                {
                    ProfilePicture = "profile-picture",
                    Certification = "certification",
                    Signatures = "signatures",
                    Temp = "temp",
                    Docs = "docs"
                },
                Settings = new MinIOSettings
                {
                    MaxFileSize = 50 * 1024 * 1024,
                    DefaultExpiration = 7,
                    EnableVersioning = true,
                    AutoCreateBuckets = true
                }
            };

            _mockConfig.Setup(x => x.Value).Returns(_configuration);
            _mockContextFactory.Setup(x => x.CreateDbContextAsync(It.IsAny<CancellationToken>()))
                .ReturnsAsync(_context);
        }

        [Fact]
        public void Configuration_ShouldHaveCorrectBucketNames()
        {
            // Assert
            Assert.Equal("profile-picture", _configuration.Buckets.ProfilePicture);
            Assert.Equal("certification", _configuration.Buckets.Certification);
            Assert.Equal("signatures", _configuration.Buckets.Signatures);
            Assert.Equal("temp", _configuration.Buckets.Temp);
            Assert.Equal("docs", _configuration.Buckets.Docs);
        }

        [Fact]
        public void Configuration_ShouldHaveCorrectSettings()
        {
            // Assert
            Assert.Equal(50 * 1024 * 1024, _configuration.Settings.MaxFileSize);
            Assert.Equal(7, _configuration.Settings.DefaultExpiration);
            Assert.True(_configuration.Settings.EnableVersioning);
            Assert.True(_configuration.Settings.AutoCreateBuckets);
        }

        [Theory]
        [InlineData("test.jpg", "image/jpeg", true)]
        [InlineData("test.png", "image/png", true)]
        [InlineData("test.pdf", "application/pdf", true)]
        [InlineData("test.docx", "application/vnd.openxmlformats-officedocument.wordprocessingml.document", true)]
        [InlineData("test.xlsx", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", true)]
        [InlineData("test.exe", "application/x-executable", false)]
        [InlineData("test.bat", "application/x-bat", false)]
        public void ValidateFileType_ShouldReturnCorrectResult(string fileName, string contentType, bool expectedValid)
        {
            // Act
            var isValid = IsValidFileType(fileName, contentType);

            // Assert
            Assert.Equal(expectedValid, isValid);
        }

        [Theory]
        [InlineData(1024, true)] // 1KB
        [InlineData(1024 * 1024, true)] // 1MB
        [InlineData(50 * 1024 * 1024, true)] // 50MB (at limit)
        [InlineData(51 * 1024 * 1024, false)] // 51MB (exceeds limit)
        [InlineData(100 * 1024 * 1024, false)] // 100MB (exceeds limit)
        public void ValidateFileSize_ShouldReturnCorrectResult(long fileSize, bool expectedValid)
        {
            // Act
            var isValid = fileSize <= _configuration.Settings.MaxFileSize;

            // Assert
            Assert.Equal(expectedValid, isValid);
        }

        [Fact]
        public void BucketConfiguration_ShouldProvideAllRequiredBuckets()
        {
            // Act
            var allBuckets = _configuration.Buckets.GetAllBuckets();

            // Assert
            Assert.Contains("profile-picture", allBuckets);
            Assert.Contains("certification", allBuckets);
            Assert.Contains("signatures", allBuckets);
            Assert.Contains("temp", allBuckets);
            Assert.Contains("docs", allBuckets);
            Assert.Equal(5, allBuckets.Count());
        }

        private static bool IsValidFileType(string fileName, string contentType)
        {
            var extension = Path.GetExtension(fileName).ToLowerInvariant();

            return extension switch
            {
                ".jpg" or ".jpeg" or ".png" or ".gif" or ".bmp" or ".webp" => true,
                ".pdf" => true,
                ".doc" or ".docx" => true,
                ".xls" or ".xlsx" => true,
                ".csv" or ".txt" => true,
                _ => false
            };
        }

        public void Dispose()
        {
            _context?.Dispose();
        }
    }
}
