// Google Sheets API Configuration
// For security, these values should be loaded from environment variables in production
// For this demo, we're using the values directly
const SHEET_ID = '11hpXocHeQao_oz5qiVSAUjZ6CaO6R9i3WTgQ8vTtOiI';
const API_KEY = 'AIzaSyDt2IRrlkD83KjoDZXybaLV0HagalnhT3k';
const SHEET_NAME = 'team'; // Name of the sheet
const SHEET_RANGE = 'A2:N'; // Range within the sheet (A2:N)
// Construct the API URL exactly like in projects.js
const API_URL = `https://sheets.googleapis.com/v4/spreadsheets/${SHEET_ID}/values/${SHEET_NAME}!${SHEET_RANGE}?key=${API_KEY}`;

// Add cache control for performance
let cachedLeaders = null;
const CACHE_DURATION = 3600000; // 1 hour in milliseconds
let lastFetchTime = 0;

// Global variables
let allLeaders = [];
let currentLeaderIndex = 0;
let modal = null;
let closeModal = null;
let prevLeaderBtn = null;
let nextLeaderBtn = null;

// DOM elements
document.addEventListener('DOMContentLoaded', () => {
  // Get DOM elements
  modal = document.getElementById('leader-modal');
  closeModal = document.querySelector('.close-modal');
  prevLeaderBtn = document.getElementById('prev-leader');
  nextLeaderBtn = document.getElementById('next-leader');

  fetchLeaders();
  setupEventListeners();

  // Force a reflow after timeout to ensure proper height calculation
  setTimeout(() => {
    const leadersContainer = document.getElementById('leaders-container');
    if (leadersContainer) {
      // Ensure container has proper height
      leadersContainer.style.minHeight = 'auto';
    }
  }, 1000);
});

// Fetch leaders from Google Sheets
async function fetchLeaders() {
  showLoading();
  hideError();

  // Check if we have cached data that's still valid
  const now = Date.now();
  if (cachedLeaders && (now - lastFetchTime < CACHE_DURATION)) {
    console.log('Using cached leadership data');
    allLeaders = cachedLeaders;
    renderLeaders(allLeaders);
    hideLoading();
    return;
  }

  try {
    // Use the simple fetch approach that works in projects.js
    const response = await fetch(API_URL);

    if (!response.ok) {
      throw new Error(`Failed to fetch data from Google Sheets: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();

    if (!data.values || data.values.length === 0) {
      throw new Error('No data found in the spreadsheet');
    }

    processLeaderData(data.values);

    // Cache the processed data
    cachedLeaders = [...allLeaders];
    lastFetchTime = now;

    renderLeaders(allLeaders);
    hideLoading();

    // Add structured data for leaders dynamically
    addLeaderStructuredData(allLeaders);

  } catch (error) {
    console.error('Error fetching leaders:', error);
    hideLoading();
    showError();
  }
}

// Add structured data for leaders
function addLeaderStructuredData(leaders) {
  // Create structured data for the leadership team
  const leadershipTeam = {
    '@context': 'http://schema.org',
    '@type': 'Organization',
    'name': 'Laxmi Group Leadership Team',
    'member': leaders.map(leader => ({
      '@type': 'Person',
      'name': leader.full_name,
      'jobTitle': leader.designation,
      'description': leader.bio,
      'worksFor': {
        '@type': 'Organization',
        'name': leader.subsidiary_company || 'Laxmi Group'
      },
      'knowsAbout': leader.skills
    }))
  };

  // Add the structured data to the page
  const script = document.createElement('script');
  script.type = 'application/ld+json';
  script.textContent = JSON.stringify(leadershipTeam);
  document.head.appendChild(script);
}

// Process the raw data from Google Sheets
function processLeaderData(rows) {
  // Create leaders array from the rows
  const leaders = rows.map((row, index) => {
    // Handle missing data gracefully
    const [
      full_name,
      designation,
      level,
      subsidiary_company,
      bio,
      image_url,
      social_links,
      department,
      skills,
      languages,
      certifications,
      years_of_experience,
      education,
      awards
    ] = row;

    // Only include leaders with level="Leader"
    if (level !== 'Leader') {
      return null;
    }

    // Process social links into an array
    const socialLinksArray = social_links ? social_links.split(',').map(url => url.trim()) : ['#', '#', '#'];

    // Ensure we have exactly 3 social links (LinkedIn, X.com, Facebook)
    while (socialLinksArray.length < 3) {
      socialLinksArray.push('#');
    }

    // Process skills, languages, certifications, education, awards into arrays
    const skillsArray = skills ? skills.split(',').map(item => item.trim()) : [];
    const languagesArray = languages ? languages.split(',').map(item => item.trim()) : [];
    const certificationsArray = certifications ? certifications.split(',').map(item => item.trim()) : [];
    const educationArray = education ? education.split(',').map(item => item.trim()) : [];
    const awardsArray = awards ? awards.split(',').map(item => item.trim()) : [];

    return {
      id: index,
      full_name: full_name || 'Unnamed Leader',
      designation: designation || 'Unknown Designation',
      level: level || 'Unknown Level',
      subsidiary_company: subsidiary_company || '',
      bio: bio || 'No biography available.',
      image_url: image_url || 'https://via.placeholder.com/500x500?text=No+Image+Available',
      social_links: socialLinksArray,
      department: department || '',
      skills: skillsArray,
      languages: languagesArray,
      certifications: certificationsArray,
      years_of_experience: years_of_experience || '',
      education: educationArray,
      awards: awardsArray
    };
  }).filter(leader => leader !== null); // Remove null entries (non-leaders)

  // Store the leaders
  allLeaders = leaders;
}

// Render leaders grid with improved semantics and SEO
function renderLeaders(leaders) {
  const leadersContainer = document.getElementById('leaders-container');

  if (!leadersContainer) {
    console.error('Leaders container not found');
    return;
  }

  leadersContainer.innerHTML = '';

  // Add a descriptive heading for screen readers
  const srHeading = document.createElement('h2');
  srHeading.className = 'sr-only'; // Visually hidden but available to screen readers
  srHeading.textContent = 'Laxmi Group Leadership Team Members';
  leadersContainer.appendChild(srHeading);

  leaders.forEach(leader => {
    const leaderCard = document.createElement('article'); // Using article for semantic meaning
    leaderCard.className = 'leader-card';
    leaderCard.dataset.id = leader.id;

    // Add microdata for better SEO
    leaderCard.setAttribute('itemscope', '');
    leaderCard.setAttribute('itemtype', 'http://schema.org/Person');

    // Create image container
    const imageContainer = document.createElement('div');
    imageContainer.className = 'leader-image-container';

    // Create and optimize image
    const image = document.createElement('img');
    image.src = leader.image_url;
    image.alt = `${leader.full_name}, ${leader.designation} at Laxmi Group`;
    image.className = 'leader-image';
    image.setAttribute('itemprop', 'image');
    image.loading = 'lazy'; // Lazy load images for performance

    // Create name heading
    const nameHeading = document.createElement('h3');
    nameHeading.className = 'leader-name';
    nameHeading.textContent = leader.full_name;
    nameHeading.setAttribute('itemprop', 'name');

    // Create designation paragraph
    const designationPara = document.createElement('p');
    designationPara.className = 'leader-designation';
    designationPara.textContent = leader.designation;
    designationPara.setAttribute('itemprop', 'jobTitle');

    // Create CTA button
    const ctaButton = document.createElement('button');
    ctaButton.className = 'leader-cta';
    ctaButton.setAttribute('aria-label', `View ${leader.full_name}'s profile`);

    // Create SVG icon for the button instead of text
    const svgIcon = document.createElementNS('http://www.w3.org/2000/svg', 'svg');
    svgIcon.setAttribute('width', '16');
    svgIcon.setAttribute('height', '16');
    svgIcon.setAttribute('viewBox', '0 0 24 24');
    svgIcon.setAttribute('fill', 'none');
    svgIcon.setAttribute('stroke', 'currentColor');
    svgIcon.setAttribute('stroke-width', '2');
    svgIcon.setAttribute('stroke-linecap', 'round');
    svgIcon.setAttribute('stroke-linejoin', 'round');

    // Create path for the icon (info icon)
    const path1 = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    path1.setAttribute('d', 'M12 16v-4');
    const path2 = document.createElementNS('http://www.w3.org/2000/svg', 'path');
    path2.setAttribute('d', 'M12 8h.01');
    const circle = document.createElementNS('http://www.w3.org/2000/svg', 'circle');
    circle.setAttribute('cx', '12');
    circle.setAttribute('cy', '12');
    circle.setAttribute('r', '10');

    // Append paths to SVG
    svgIcon.appendChild(circle);
    svgIcon.appendChild(path1);
    svgIcon.appendChild(path2);

    // Append SVG to button
    ctaButton.appendChild(svgIcon);

    // Add hidden metadata for SEO
    const orgMeta = document.createElement('meta');
    orgMeta.setAttribute('itemprop', 'worksFor');
    orgMeta.content = leader.subsidiary_company || 'Laxmi Group';

    // Assemble the card
    imageContainer.appendChild(image);
    leaderCard.appendChild(imageContainer);
    leaderCard.appendChild(nameHeading);
    leaderCard.appendChild(designationPara);
    leaderCard.appendChild(ctaButton);
    leaderCard.appendChild(orgMeta);

    // Make the entire card clickable
    leaderCard.addEventListener('click', () => {
      openLeaderModal(leader.id);
    });

    // Also keep the CTA button clickable for accessibility
    ctaButton.addEventListener('click', (e) => {
      e.stopPropagation(); // Prevent double triggering
      openLeaderModal(leader.id);
    });

    leadersContainer.appendChild(leaderCard);
  });

  // Set a timeout to ensure proper height calculation after images load
  setTimeout(() => {
    // Force a reflow to ensure proper height calculation
    const totalHeight = Math.ceil(leaders.length / 3) * 400; // Approximate height based on card height
    const minHeight = Math.max(totalHeight, 300); // At least 300px or calculated height

    // Set the container's min-height to ensure all content is visible
    leadersContainer.style.minHeight = minHeight + 'px';

    // Force document to recalculate its height
    document.body.style.height = 'auto';
    document.documentElement.style.height = 'auto';

    // Add a hidden link for screen readers to skip to content
    if (!document.getElementById('skip-to-leaders')) {
      const skipLink = document.createElement('a');
      skipLink.id = 'skip-to-leaders';
      skipLink.className = 'sr-only sr-only-focusable';
      skipLink.href = '#leaders-container';
      skipLink.textContent = 'Skip to leadership team';
      document.body.insertBefore(skipLink, document.body.firstChild);
    }
  }, 500);
}

// Open leader modal with details
function openLeaderModal(leaderId) {
  const leaderIndex = allLeaders.findIndex(l => l.id === parseInt(leaderId));

  if (leaderIndex === -1) {
    return;
  }

  currentLeaderIndex = leaderIndex;
  const leader = allLeaders[currentLeaderIndex];

  // Populate modal content
  document.getElementById('modal-leader-name').textContent = leader.full_name;
  document.getElementById('modal-designation').textContent = leader.designation;
  document.getElementById('modal-bio').textContent = leader.bio;
  const leaderImage = document.getElementById('modal-leader-image');
  leaderImage.src = leader.image_url;
  leaderImage.alt = leader.full_name;

  // Ensure image loads properly
  leaderImage.onload = function() {
    // Force reflow of container after image loads
    const container = document.querySelector('.modal-leader-image-container');
    if (container) {
      container.style.height = 'auto';
    }
  };

  // Set years of experience
  const yearsElement = document.getElementById('modal-years');
  if (leader.years_of_experience) {
    yearsElement.textContent = leader.years_of_experience;
    document.getElementById('years-section').style.display = 'block';
  } else {
    document.getElementById('years-section').style.display = 'none';
  }

  // Populate social links
  const socialLinksContainer = document.getElementById('modal-social-links');
  socialLinksContainer.innerHTML = '';

  const socialIcons = ['linkedin', 'twitter', 'facebook'];
  const socialNames = ['LinkedIn', 'X', 'Facebook'];
  leader.social_links.forEach((link, index) => {
    if (link && link !== '#') {
      const socialLink = document.createElement('a');
      socialLink.href = link;
      socialLink.target = '_blank';
      socialLink.rel = 'noopener noreferrer';
      socialLink.className = 'social-icon';
      socialLink.setAttribute('aria-label', `${leader.full_name} on ${socialNames[index]}`);
      socialLink.setAttribute('tabindex', '0');
      socialLink.innerHTML = `<i class="icon-${socialIcons[index]}"></i>`;
      socialLinksContainer.appendChild(socialLink);
    }
  });

  // Populate skills section
  const skillsList = document.getElementById('modal-skills');
  skillsList.innerHTML = '';

  if (leader.skills.length > 0) {
    leader.skills.forEach(skill => {
      const li = document.createElement('li');
      li.textContent = skill;
      skillsList.appendChild(li);
    });
    document.getElementById('skills-section').style.display = 'block';
  } else {
    document.getElementById('skills-section').style.display = 'none';
  }

  // Populate languages section
  const languagesList = document.getElementById('modal-languages');
  languagesList.innerHTML = '';

  if (leader.languages.length > 0) {
    leader.languages.forEach(language => {
      const li = document.createElement('li');
      li.textContent = language;
      languagesList.appendChild(li);
    });
    document.getElementById('languages-section').style.display = 'block';
  } else {
    document.getElementById('languages-section').style.display = 'none';
  }

  // Populate certifications section
  const certificationsList = document.getElementById('modal-certifications');
  certificationsList.innerHTML = '';

  if (leader.certifications.length > 0) {
    leader.certifications.forEach(certification => {
      const li = document.createElement('li');
      li.textContent = certification;
      certificationsList.appendChild(li);
    });
    document.getElementById('certifications-section').style.display = 'block';
  } else {
    document.getElementById('certifications-section').style.display = 'none';
  }

  // Populate education section
  const educationList = document.getElementById('modal-education');
  educationList.innerHTML = '';

  if (leader.education.length > 0) {
    leader.education.forEach(edu => {
      const li = document.createElement('li');
      li.textContent = edu;
      educationList.appendChild(li);
    });
    document.getElementById('education-section').style.display = 'block';
  } else {
    document.getElementById('education-section').style.display = 'none';
  }

  // Populate awards section
  const awardsList = document.getElementById('modal-awards');
  awardsList.innerHTML = '';

  if (leader.awards.length > 0) {
    leader.awards.forEach(award => {
      const li = document.createElement('li');
      li.textContent = award;
      awardsList.appendChild(li);
    });
    document.getElementById('awards-section').style.display = 'block';
  } else {
    document.getElementById('awards-section').style.display = 'none';
  }

  // Update navigation buttons
  updateNavigationButtons();

  // Store the current scroll position before showing modal
  const scrollY = window.scrollY;
  modal.dataset.scrollY = scrollY;

  // Add a class to the body to prevent background scrolling
  document.body.classList.add('modal-open');

  // Show modal
  modal.style.display = 'block';

  // Ensure modal inner is scrollable
  const modalInner = document.querySelector('.modal-inner');
  if (modalInner) {
    modalInner.style.overflowY = 'auto';
    modalInner.style.webkitOverflowScrolling = 'touch';
  }

  // Set focus to modal content for keyboard navigation
  setTimeout(() => {
    const modalContent = document.querySelector('.modal-content');
    if (modalContent) {
      modalContent.setAttribute('tabindex', '-1');
      modalContent.focus();
      // Focus set to modal content
    }

    // Force a reflow to ensure scrolling works
    if (modalInner) {
      modalInner.style.display = 'none';
      void modalInner.offsetHeight; // Force reflow
      modalInner.style.display = 'block';

      // Reset overflow after reflow
      setTimeout(() => {
        modalInner.style.overflowY = 'auto';
        modalInner.style.webkitOverflowScrolling = 'touch';
      }, 50);
    }
  }, 100);
}

// Close the modal
function closeLeaderModal() {
  // Reset any overflow styles that might have been set
  const modalInner = document.querySelector('.modal-inner');
  if (modalInner) {
    // Allow any ongoing scrolling to complete
    setTimeout(() => {
      // Hide modal
      modal.style.display = 'none';

      // Remove modal-open class from body to restore scrolling
      document.body.classList.remove('modal-open');

      // Restore scroll position
      const scrollY = parseInt(modal.dataset.scrollY || '0');
      window.scrollTo(0, scrollY);
    }, 50);
  } else {
    // If modalInner not found, just close immediately
    modal.style.display = 'none';
    document.body.classList.remove('modal-open');
    const scrollY = parseInt(modal.dataset.scrollY || '0');
    window.scrollTo(0, scrollY);
  }
}

// Navigate to previous leader
function navigateToPrevLeader() {
  if (currentLeaderIndex > 0) {
    currentLeaderIndex--;
    openLeaderModal(allLeaders[currentLeaderIndex].id);
  }
}

// Navigate to next leader
function navigateToNextLeader() {
  if (currentLeaderIndex < allLeaders.length - 1) {
    currentLeaderIndex++;
    openLeaderModal(allLeaders[currentLeaderIndex].id);
  }
}

// Update navigation buttons based on current leader
function updateNavigationButtons() {
  // Disable/enable previous button
  if (currentLeaderIndex === 0) {
    prevLeaderBtn.disabled = true;
    prevLeaderBtn.classList.add('disabled');
  } else {
    prevLeaderBtn.disabled = false;
    prevLeaderBtn.classList.remove('disabled');
  }

  // Disable/enable next button
  if (currentLeaderIndex === allLeaders.length - 1) {
    nextLeaderBtn.disabled = true;
    nextLeaderBtn.classList.add('disabled');
  } else {
    nextLeaderBtn.disabled = false;
    nextLeaderBtn.classList.remove('disabled');
  }
}

// Setup event listeners
function setupEventListeners() {
  // Close modal
  if (closeModal) {
    closeModal.addEventListener('click', closeLeaderModal);
    // Add keyboard support for the close button
    closeModal.addEventListener('keydown', function(event) {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault();
        closeLeaderModal();
      }
    });
  }

  // Close modal when clicking outside
  window.addEventListener('click', (event) => {
    if (event.target === modal) {
      closeLeaderModal();
    }
  });

  // Navigation buttons
  if (prevLeaderBtn) {
    prevLeaderBtn.addEventListener('click', navigateToPrevLeader);
  }

  if (nextLeaderBtn) {
    nextLeaderBtn.addEventListener('click', navigateToNextLeader);
  }

  // Retry button for error message
  const retryButton = document.getElementById('retry-button');
  if (retryButton) {
    retryButton.addEventListener('click', fetchLeaders);
  }

  // Keyboard navigation
  document.addEventListener('keydown', (event) => {
    if (modal && modal.style.display === 'block') {
      if (event.key === 'Escape') {
        closeLeaderModal();
      } else if (event.key === 'ArrowLeft') {
        navigateToPrevLeader();
      } else if (event.key === 'ArrowRight') {
        navigateToNextLeader();
      }
    }
  });
}

// Show loading indicator
function showLoading() {
  const loading = document.getElementById('loading');
  if (loading) {
    loading.style.display = 'flex';
  }
}

// Hide loading indicator
function hideLoading() {
  const loading = document.getElementById('loading');
  if (loading) {
    loading.style.display = 'none';
  }
}

// Show error message
function showError() {
  const error = document.getElementById('error-message');
  if (error) {
    error.style.display = 'block';
  }
}

// Hide error message
function hideError() {
  const error = document.getElementById('error-message');
  if (error) {
    error.style.display = 'none';
  }
}

// Lazy load images (optional enhancement)
function lazyLoadImages() {
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src;
          img.classList.remove('lazy');
          imageObserver.unobserve(img);
        }
      });
    });

    const lazyImages = document.querySelectorAll('img.lazy');
    lazyImages.forEach(img => {
      imageObserver.observe(img);
    });
  } else {
    // Fallback for browsers that don't support IntersectionObserver
    const lazyImages = document.querySelectorAll('img.lazy');
    lazyImages.forEach(img => {
      img.src = img.dataset.src;
      img.classList.remove('lazy');
    });
  }
}
