import { NavigationContext } from "../types";

/**
 * Determines the navigation context based on the current pathname
 */
export const getNavigationContext = (pathname: string): NavigationContext => {
	// Check if we're in a site-specific route
	const siteRouteMatch = pathname.match(/^\/sites\/([^\/]+)/);

	if (siteRouteMatch) {
		const siteId = siteRouteMatch[1];
		return {
			isCompanyLevel: false,
			isSiteLevel: true,
			siteId,
			siteName: undefined, // Will be populated by the hook
		};
	}

	// Default to company level
	return {
		isCompanyLevel: true,
		isSiteLevel: false,
		siteId: undefined,
		siteName: undefined,
	};
};

/**
 * Checks if a given path is active based on current location
 */
export const isPathActive = (
	currentPath: string,
	targetPath: string,
): boolean => {
	// Handle hash-based navigation
	if (targetPath.includes("#")) {
		const currentFullPath = currentPath + window.location.hash;
		return currentFullPath === targetPath;
	}

	// Exact match for root path
	if (targetPath === "/" && currentPath === "/") {
		return true;
	}

	// For non-root paths, check if current path starts with target path
	if (targetPath !== "/" && currentPath.startsWith(targetPath)) {
		return true;
	}

	return false;
};

/**
 * Checks if any submenu item is active
 */
export const isSubmenuActive = (
	currentPath: string,
	submenuItems: { path: string }[],
): boolean => {
	return submenuItems.some((item) => isPathActive(currentPath, item.path));
};

/**
 * Generates site-specific path
 */
export const getSitePath = (siteId: string, path: string): string => {
	return `/sites/${siteId}${path}`;
};

/**
 * Extracts site ID from current path
 */
export const extractSiteId = (pathname: string): string | null => {
	const match = pathname.match(/^\/sites\/([^\/]+)/);
	return match ? match[1] : null;
};

/**
 * Generates equivalent path for a different site
 * Maps current site-specific path to equivalent path in target site
 */
export const getEquivalentSitePath = (
	currentPath: string,
	targetSiteId: string,
): string => {
	// Extract the current site ID and the rest of the path
	const siteRouteMatch = currentPath.match(/^\/sites\/([^\/]+)(.*)$/);

	if (!siteRouteMatch) {
		// If not a site route, default to site dashboard
		return `/sites/${targetSiteId}/dashboard`;
	}

	const [, /*currentSiteId*/, restOfPath] = siteRouteMatch;

	// If no additional path, go to dashboard
	if (!restOfPath || restOfPath === "/dashboard") {
		return `/sites/${targetSiteId}/dashboard`;
	}

	// Map the rest of the path to the new site
	return `/sites/${targetSiteId}${restOfPath}`;
};

/**
 * Checks if a breadcrumb item is the site name (second item in site breadcrumbs)
 */
export const isSiteBreadcrumb = (
	breadcrumbs: { name: string; path: string }[],
	index: number,
): boolean => {
	// Site name is typically the second breadcrumb (index 1) in site views
	// Pattern: Dashboard / [Site Name] / ...
	return (
		index === 1 &&
		breadcrumbs.length >= 2 &&
		breadcrumbs[0].name === "Dashboard"
	);
};
