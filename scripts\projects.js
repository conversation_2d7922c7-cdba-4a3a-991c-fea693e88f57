// Google Sheets API Configuration
// For security, these values should be loaded from environment variables in production
// For this demo, we're using the values directly
const SHEET_ID = '11hpXocHeQao_oz5qiVSAUjZ6CaO6R9i3WTgQ8vTtOiI';
const API_KEY = 'AIzaSyDt2IRrlkD83KjoDZXybaLV0HagalnhT3k';
const SHEET_RANGE = 'projects!A2:H'; // Assuming data starts from row 2 with headers in row 1
const API_URL = `https://sheets.googleapis.com/v4/spreadsheets/${SHEET_ID}/values/${SHEET_RANGE}?key=${API_KEY}`;

// Note: In a production environment, these API keys should be secured
// and not exposed in client-side code. Consider using a server-side proxy
// or environment variables with a build process.

// DOM Elements
const projectsContainer = document.getElementById('projects-container');
const loadingElement = document.getElementById('loading');
const errorElement = document.getElementById('error-message');
const retryButton = document.getElementById('retry-button');
const filterButtons = document.querySelectorAll('.filter-btn');
const filterDropdown = document.getElementById('filter-dropdown');
const modal = document.getElementById('project-modal');
const closeModal = document.querySelector('.close-modal');
const prevProjectBtn = document.getElementById('prev-project');
const nextProjectBtn = document.getElementById('next-project');

// Global Variables
let allProjects = [];
let filteredProjects = [];
let currentProjectIndex = 0;
let currentFilter = 'all';

// Initialize the application
document.addEventListener('DOMContentLoaded', () => {
  console.log('DOM fully loaded');
  console.log('Window dimensions:', window.innerWidth, 'x', window.innerHeight);
  console.log('Document height:', document.documentElement.scrollHeight);

  // Ensure the document is scrollable
  document.documentElement.style.height = 'auto';
  document.body.style.height = 'auto';
  document.body.style.overflowY = 'visible';

  // Log page dimensions after a short delay to ensure all content is rendered
  setTimeout(() => {
    console.log('After timeout - Document height:', document.documentElement.scrollHeight);
    console.log('After timeout - Viewport height:', window.innerHeight);
    console.log('After timeout - Projects container height:', projectsContainer.offsetHeight);

    // Force a reflow to ensure proper height calculation
    if (projectsContainer) {
      projectsContainer.style.minHeight = 'auto';
      void projectsContainer.offsetHeight; // Force reflow
    }
  }, 1000);

  // Add scroll event listener to document
  document.addEventListener('scroll', () => {
    console.log('Page scroll event:',
      'scrollY:', window.scrollY,
      'document height:', document.documentElement.scrollHeight,
      'viewport height:', window.innerHeight,
      'Percentage scrolled:', Math.round((window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100) + '%'
    );
  });

  fetchProjects();
  setupEventListeners();
});

// Fetch projects from Google Sheets
async function fetchProjects() {
  showLoading();
  hideError();

  try {
    const response = await fetch(API_URL);

    if (!response.ok) {
      throw new Error('Failed to fetch data from Google Sheets');
    }

    const data = await response.json();

    if (!data.values || data.values.length === 0) {
      throw new Error('No data found in the spreadsheet');
    }

    processProjectData(data.values);
    renderProjects(allProjects);
    hideLoading();
  } catch (error) {
    console.error('Error fetching projects:', error);
    hideLoading();
    showError();
  }
}

// Process the raw data from Google Sheets
function processProjectData(rows) {
  // Create projects array from the rows
  const projects = rows.map((row, index) => {
    // Handle missing data gracefully
    const [name, sector, client, location, description, delivered, mainImage, additionalImages] = row;

    // Process "What We Delivered" into an array of items
    const deliveredItems = delivered ? delivered.split(',').map(item => item.trim()) : [];

    // Process additional images into an array
    const additionalImageArray = additionalImages ? additionalImages.split(',').map(url => url.trim()) : [];

    return {
      id: index, // Keep original index for reference
      name: name || 'Unnamed Project',
      sector: sector || 'Uncategorized',
      client: client || 'Unknown Client',
      location: location || 'Unknown Location',
      description: description || 'No description available.',
      deliveredItems: deliveredItems,
      mainImage: mainImage || 'https://via.placeholder.com/800x600?text=No+Image+Available',
      additionalImages: additionalImageArray,
    };
  });

  // Reverse the array to show newest projects first (last in the sheet)
  allProjects = projects.reverse();

  // Initialize filtered projects with all projects
  filteredProjects = [...allProjects];
}

// Render projects to the DOM
function renderProjects(projects) {
  console.log('Rendering', projects.length, 'projects');
  projectsContainer.innerHTML = '';

  if (projects.length === 0) {
    console.log('No projects to render');
    projectsContainer.innerHTML = '<div class="no-projects">No projects found for this category.</div>';
    return;
  }

  projects.forEach(project => {
    const projectCard = document.createElement('div');
    projectCard.className = 'project-card';
    projectCard.dataset.id = project.id;

    projectCard.innerHTML = `
      <img src="${project.mainImage}" alt="${project.name}" class="project-image">
      <div class="project-overlay">
        <h3 class="project-title">${project.name}</h3>
        <div class="project-info">
          <p>${project.client} | ${project.sector}</p>
        </div>
        <button class="view-details-btn">View Details</button>
      </div>
    `;

    projectCard.querySelector('.view-details-btn').addEventListener('click', () => {
      openProjectModal(project.id);
    });

    projectsContainer.appendChild(projectCard);
  });

  // Log container dimensions after rendering
  console.log('After rendering - Projects container dimensions:',
    'width:', projectsContainer.offsetWidth,
    'height:', projectsContainer.offsetHeight,
    'scroll height:', projectsContainer.scrollHeight
  );

  // Log document dimensions after rendering
  console.log('After rendering - Document dimensions:',
    'height:', document.documentElement.scrollHeight,
    'client height:', document.documentElement.clientHeight
  );

  // Check if container is too small
  if (projectsContainer.offsetHeight < 500 && projects.length > 0) {
    console.warn('Warning: Projects container height is less than 500px with', projects.length, 'projects');
  }

  // Set a timeout to ensure proper height calculation after images load
  setTimeout(() => {
    // Force a reflow to ensure proper height calculation
    const totalHeight = projects.length * 300 / 3 + (Math.ceil(projects.length / 3) - 1) * 32; // Approximate height based on card height and gap
    const minHeight = Math.max(totalHeight, 300); // At least 300px or calculated height

    // Set the container's min-height to ensure all content is visible
    projectsContainer.style.minHeight = minHeight + 'px';

    // Force document to recalculate its height
    document.body.style.height = 'auto';
    document.documentElement.style.height = 'auto';

    console.log('Updated projects container min-height to:', minHeight + 'px');
    console.log('Updated document dimensions:',
      'height:', document.documentElement.scrollHeight,
      'client height:', document.documentElement.clientHeight
    );
  }, 500);
}

// Filter projects by sector
function filterProjects(sector) {
  currentFilter = sector;

  if (sector === 'all') {
    filteredProjects = [...allProjects];
  } else {
    filteredProjects = allProjects.filter(project =>
      project.sector.toLowerCase() === sector.toLowerCase()
    );
  }

  renderProjects(filteredProjects);
}

// Open project modal with details
function openProjectModal(projectId) {
  console.log('Opening modal for project ID:', projectId);
  console.log('Window dimensions:', window.innerWidth, 'x', window.innerHeight);
  console.log('Current scroll position:', window.scrollX, window.scrollY);

  const projectIndex = filteredProjects.findIndex(p => p.id === parseInt(projectId));

  if (projectIndex === -1) {
    console.error('Project not found in filtered projects');
    return;
  }

  currentProjectIndex = projectIndex;
  const project = filteredProjects[currentProjectIndex];
  console.log('Found project:', project.name);

  // Populate modal content
  document.getElementById('modal-project-title').textContent = project.name;
  document.getElementById('modal-client').textContent = project.client;
  document.getElementById('modal-sector').textContent = project.sector;
  document.getElementById('modal-location').textContent = project.location;
  document.getElementById('modal-description').textContent = project.description;
  document.getElementById('modal-main-image').src = project.mainImage;
  document.getElementById('modal-main-image').alt = project.name;

  // Populate delivered items list
  const deliveredList = document.getElementById('modal-delivered-list');
  deliveredList.innerHTML = '';

  if (project.deliveredItems.length > 0) {
    console.log('Adding', project.deliveredItems.length, 'delivered items');
    project.deliveredItems.forEach(item => {
      const li = document.createElement('li');
      li.textContent = item;
      deliveredList.appendChild(li);
    });
  } else {
    console.log('No delivered items found');
    const li = document.createElement('li');
    li.textContent = 'Information not available';
    deliveredList.appendChild(li);
  }

  // Populate gallery
  const galleryContainer = document.getElementById('modal-gallery');
  galleryContainer.innerHTML = '';

  if (project.additionalImages.length > 0) {
    // Limit to 6 images as per requirements
    const imagesToShow = project.additionalImages.slice(0, 6);
    console.log('Adding', imagesToShow.length, 'gallery images');

    imagesToShow.forEach(imageUrl => {
      const imgContainer = document.createElement('div');
      imgContainer.className = 'gallery-item';

      const img = document.createElement('img');
      img.src = imageUrl;
      img.alt = `${project.name} - Additional Image`;
      img.className = 'gallery-image';

      // Add click event to show full-size image (optional enhancement)
      img.addEventListener('click', () => {
        document.getElementById('modal-main-image').src = imageUrl;
      });

      imgContainer.appendChild(img);
      galleryContainer.appendChild(imgContainer);
    });

    galleryContainer.style.display = 'grid';
  } else {
    console.log('No additional images found');
    galleryContainer.style.display = 'none';
  }

  // Update navigation buttons
  updateNavigationButtons();

  // Store the current scroll position before showing modal
  const scrollY = window.scrollY;
  modal.dataset.scrollY = scrollY;
  console.log('Stored scroll position:', scrollY);

  // Add a class to the body to prevent background scrolling
  document.body.classList.add('modal-open');
  console.log('Added modal-open class to body');

  // Show modal
  console.log('Displaying modal');
  modal.style.display = 'block';

  // Log modal dimensions
  const modalContent = document.querySelector('.modal-content');
  const modalInner = document.querySelector('.modal-inner');
  console.log('Modal content dimensions:', modalContent.offsetWidth, 'x', modalContent.offsetHeight);
  console.log('Modal inner dimensions:', modalInner.offsetWidth, 'x', modalInner.offsetHeight);
  console.log('Modal inner scroll height:', modalInner.scrollHeight);

  // Check if modal should be scrollable
  const isScrollable = modalInner.scrollHeight > modalInner.clientHeight;
  console.log('Modal should be scrollable:', isScrollable,
    'scrollHeight:', modalInner.scrollHeight,
    'clientHeight:', modalInner.clientHeight,
    'difference:', modalInner.scrollHeight - modalInner.clientHeight
  );

  // Check if modal is actually scrollable
  const computedStyle = window.getComputedStyle(modalInner);
  console.log('Modal computed overflow-y:', computedStyle.overflowY);
  console.log('Modal computed max-height:', computedStyle.maxHeight);

  // Ensure the modal inner content is scrolled to the top when opened
  modalInner.scrollTop = 0;
  console.log('Reset modal inner scrollTop to 0');

  // Set tabindex to make the modal inner focusable
  modalInner.setAttribute('tabindex', '0');

  // Focus the modal inner content to enable keyboard and touchpad scrolling
  setTimeout(() => {
    modalInner.focus();
    console.log('Focused modal inner content');
  }, 100);

  // Add event listener for keyboard navigation
  document.addEventListener('keydown', handleModalKeyPress);
  console.log('Added keyboard event listener');

  // Add scroll event listener to modal inner
  modalInner.addEventListener('scroll', logModalScroll);

  // Add wheel event listener to ensure mouse wheel scrolling works
  modalInner.addEventListener('wheel', handleModalWheel, { passive: false });
}

// Update navigation buttons in modal
function updateNavigationButtons() {
  const prevProject = getPrevProject();
  const nextProject = getNextProject();

  // Update previous button
  if (prevProject) {
    prevProjectBtn.style.visibility = 'visible';
    document.getElementById('prev-title').textContent = truncateTitle(prevProject.name);
  } else {
    prevProjectBtn.style.visibility = 'hidden';
  }

  // Update next button
  if (nextProject) {
    nextProjectBtn.style.visibility = 'visible';
    document.getElementById('next-title').textContent = truncateTitle(nextProject.name);
  } else {
    nextProjectBtn.style.visibility = 'hidden';
  }
}

// Get previous project in filtered list
function getPrevProject() {
  if (currentProjectIndex > 0) {
    return filteredProjects[currentProjectIndex - 1];
  }
  return null;
}

// Get next project in filtered list
function getNextProject() {
  if (currentProjectIndex < filteredProjects.length - 1) {
    return filteredProjects[currentProjectIndex + 1];
  }
  return null;
}

// Navigate to previous project
function navigateToPrevProject() {
  if (currentProjectIndex > 0) {
    currentProjectIndex--;
    openProjectModal(filteredProjects[currentProjectIndex].id);
  }
}

// Navigate to next project
function navigateToNextProject() {
  if (currentProjectIndex < filteredProjects.length - 1) {
    currentProjectIndex++;
    openProjectModal(filteredProjects[currentProjectIndex].id);
  }
}

// Truncate title to first 2 words or hide completely for simplified navigation
function truncateTitle(title) {
  // For simplified navigation, we can return an empty string
  // or just return the first 2 words for minimal context
  const words = title.split(' ');
  if (words.length <= 2) return title;
  return words.slice(0, 2).join(' ') + '...';
}

// Handle keyboard navigation in modal
function handleModalKeyPress(event) {
  if (modal.style.display === 'block') {
    console.log('Key pressed in modal:', event.key);

    // Get the modal inner element
    const modalInner = document.querySelector('.modal-inner');

    if (event.key === 'Escape') {
      console.log('Escape key pressed - closing modal');
      closeProjectModal();
      event.preventDefault();
    } else if (event.key === 'ArrowLeft') {
      console.log('Left arrow pressed - navigating to previous project');
      navigateToPrevProject();
      event.preventDefault();
    } else if (event.key === 'ArrowRight') {
      console.log('Right arrow pressed - navigating to next project');
      navigateToNextProject();
      event.preventDefault();
    } else if (event.key === 'ArrowUp') {
      // Scroll up
      modalInner.scrollTop -= 40; // Scroll by 40px
      console.log('Up arrow pressed - scrolling up to:', modalInner.scrollTop);
      event.preventDefault();
    } else if (event.key === 'ArrowDown') {
      // Scroll down
      modalInner.scrollTop += 40; // Scroll by 40px
      console.log('Down arrow pressed - scrolling down to:', modalInner.scrollTop);
      event.preventDefault();
    } else if (event.key === 'PageUp') {
      // Scroll up by a larger amount
      modalInner.scrollTop -= modalInner.clientHeight * 0.8;
      console.log('PageUp pressed - scrolling up to:', modalInner.scrollTop);
      event.preventDefault();
    } else if (event.key === 'PageDown') {
      // Scroll down by a larger amount
      modalInner.scrollTop += modalInner.clientHeight * 0.8;
      console.log('PageDown pressed - scrolling down to:', modalInner.scrollTop);
      event.preventDefault();
    } else if (event.key === 'Home') {
      // Scroll to top
      modalInner.scrollTop = 0;
      console.log('Home pressed - scrolling to top');
      event.preventDefault();
    } else if (event.key === 'End') {
      // Scroll to bottom
      modalInner.scrollTop = modalInner.scrollHeight;
      console.log('End pressed - scrolling to bottom');
      event.preventDefault();
    }
  }
}

// Log modal scroll events
function logModalScroll(event) {
  const modalInner = event.target;
  console.log('Modal scroll event:',
    'scrollTop:', modalInner.scrollTop,
    'scrollHeight:', modalInner.scrollHeight,
    'clientHeight:', modalInner.clientHeight,
    'Percentage scrolled:', Math.round((modalInner.scrollTop / (modalInner.scrollHeight - modalInner.clientHeight)) * 100) + '%'
  );
}

// Handle wheel events on modal - only prevent propagation
function handleModalWheel(event) {
  // Only prevent propagation to avoid scrolling the body
  event.stopPropagation();

  // Let the browser handle the scrolling naturally
  // No need to call preventDefault() or manually adjust scrollTop

  // Log for debugging
  console.log('Wheel event on modal:',
    'deltaY:', event.deltaY,
    'scrollTop:', event.currentTarget.scrollTop
  );
}

// Close the modal
function closeProjectModal() {
  console.log('Closing modal');
  modal.style.display = 'none';

  // Get the scroll position before removing classes
  const scrollY = modal.dataset.scrollY ? parseInt(modal.dataset.scrollY) : 0;
  console.log('Retrieved scroll position:', scrollY);

  // Remove the modal-open class from body
  document.body.classList.remove('modal-open');
  console.log('Removed modal-open class from body');

  // Restore the scroll position
  window.scrollTo(0, scrollY);
  console.log('Restored scroll position to:', scrollY);

  // Remove event listeners
  document.removeEventListener('keydown', handleModalKeyPress);
  const modalInner = document.querySelector('.modal-inner');
  if (modalInner) {
    modalInner.removeEventListener('scroll', logModalScroll);
    modalInner.removeEventListener('wheel', handleModalWheel);
    modalInner.removeAttribute('tabindex'); // Remove tabindex attribute
  }
  console.log('Removed event listeners');

  // Log window dimensions and scroll after closing
  setTimeout(() => {
    console.log('After closing - Window scroll position:', window.scrollX, window.scrollY);
    console.log('After closing - Document height:', document.documentElement.scrollHeight);
    console.log('After closing - Viewport height:', window.innerHeight);
  }, 100);
}

// Show loading indicator
function showLoading() {
  loadingElement.style.display = 'flex';
  projectsContainer.style.display = 'none';
}

// Hide loading indicator
function hideLoading() {
  loadingElement.style.display = 'none';
  projectsContainer.style.display = 'grid';
}

// Show error message
function showError() {
  errorElement.style.display = 'block';
}

// Hide error message
function hideError() {
  errorElement.style.display = 'none';
}

// Set up event listeners
function setupEventListeners() {
  // Filter buttons
  filterButtons.forEach(button => {
    button.addEventListener('click', () => {
      // Update active state
      filterButtons.forEach(btn => btn.classList.remove('active'));
      button.classList.add('active');

      // Apply filter
      const filter = button.dataset.filter;
      filterProjects(filter);

      // Update dropdown for mobile
      filterDropdown.value = filter;
    });
  });

  // Filter dropdown (mobile)
  filterDropdown.addEventListener('change', () => {
    const filter = filterDropdown.value;
    filterProjects(filter);

    // Update buttons for desktop
    filterButtons.forEach(btn => {
      if (btn.dataset.filter === filter) {
        btn.classList.add('active');
      } else {
        btn.classList.remove('active');
      }
    });
  });

  // Close modal
  closeModal.addEventListener('click', closeProjectModal);

  // Add keyboard support for the close button
  closeModal.addEventListener('keydown', function(event) {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      closeProjectModal();
    }
  });

  // Close modal when clicking outside
  window.addEventListener('click', (event) => {
    if (event.target === modal) {
      console.log('Click detected on modal background');
      console.log('Click target:', event.target);
      console.log('Modal element:', modal);
      closeProjectModal();
    }
  });

  // Navigation buttons
  prevProjectBtn.addEventListener('click', navigateToPrevProject);
  nextProjectBtn.addEventListener('click', navigateToNextProject);

  // Retry button
  retryButton.addEventListener('click', fetchProjects);
}

// Lazy load images (optional enhancement)
function lazyLoadImages() {
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target;
          img.src = img.dataset.src;
          img.classList.remove('lazy');
          imageObserver.unobserve(img);
        }
      });
    });

    const lazyImages = document.querySelectorAll('img.lazy');
    lazyImages.forEach(img => {
      imageObserver.observe(img);
    });
  } else {
    // Fallback for browsers that don't support IntersectionObserver
    const lazyImages = document.querySelectorAll('img.lazy');
    lazyImages.forEach(img => {
      img.src = img.dataset.src;
      img.classList.remove('lazy');
    });
  }
}
