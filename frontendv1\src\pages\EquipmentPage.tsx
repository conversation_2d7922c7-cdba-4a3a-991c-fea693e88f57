import { useState, useEffect } from "react";
import { useParams, useLocation } from "react-router-dom";
import {
	<PERSON><PERSON>,
	Clipboard<PERSON>heck,
	BarChart3,
	Package } from "lucide-react";
import FloatingCard from "../components/layout/FloatingCard";
import TabContainer, { Tab } from "../components/data/shared/TabContainer";
import EquipmentDashboard from "../components/equipment/EquipmentDashboard";
import GeneralEquipment from "../components/equipment/GeneralEquipment";
import EquipmentInspections from "../components/equipment/EquipmentInspections";
import EquipmentMaintenance from "../components/equipment/EquipmentMaintenance";
import EquipmentAnalytics from "../components/equipment/EquipmentAnalytics";

const EquipmentPage = () => {
	const { siteId } = useParams<{ siteId: string }>();
	const location = useLocation();
	const [activeTab, setActiveTab] = useState("dashboard");

	// Handle hash navigation
	useEffect(() => {
		const hash = location.hash.replace("#", "");
		if (
			hash &&
			[
				"dashboard",
				"general-equipment",
				"inspections",
				"maintenance",
				"analytics",
			].includes(hash)
		) {
			setActiveTab(hash);
		}
	}, [location.hash]);

	// Update URL hash when tab changes
	const handleTabChange = (tabId: string) => {
		setActiveTab(tabId);
		window.location.hash = tabId;
	};

	const tabs: Tab[] = [
		{
			id: "dashboard",
			label: "Dashboard",
			icon: <BarChart3 className="h-4 w-4" />,
			content: (
				<EquipmentDashboard
					siteId={siteId!}
					onNavigateToTab={handleTabChange}
				/>
			) },
		{
			id: "general-equipment",
			label: "General Equipment",
			icon: <Wrench className="h-4 w-4" />,
			content: <GeneralEquipment siteId={siteId!} /> },
		{
			id: "inspections",
			label: "Inspections",
			icon: <ClipboardCheck className="h-4 w-4" />,
			content: <EquipmentInspections siteId={siteId!} /> },
		{
			id: "maintenance",
			label: "Maintenance",
			icon: <Package className="h-4 w-4" />,
			content: <EquipmentMaintenance siteId={siteId!} /> },
		{
			id: "analytics",
			label: "Analytics",
			icon: <BarChart3 className="h-4 w-4" />,
			content: <EquipmentAnalytics siteId={siteId!} /> },
	];

	const breadcrumbs = [
		{ name: "Sites", path: "/" },
		{ name: "Site Dashboard", path: `/sites/${siteId}/dashboard` },
		{ name: "Equipment Management", path: `/sites/${siteId}/equipment` },
	];

	return (
		<FloatingCard title="Equipment Management" breadcrumbs={breadcrumbs}>
			<TabContainer
				tabs={tabs}
				activeTab={activeTab}
				onTabChange={handleTabChange}
			/>
		</FloatingCard>
	);
};

export default EquipmentPage;
