 .u-section-1 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-1 .u-sheet-1 {
  min-height: 247px;
}

.u-section-1 .u-text-1 {
  margin: 20px 0 0;
}

.u-section-1 .u-text-2 {
  font-weight: 400;
  margin: 20px 0;
}

@media (max-width: 1199px) {
  .u-section-1 .u-text-1 {
    margin-top: -37px;
  }

  .u-section-1 .u-text-2 {
    margin-bottom: -37px;
  }
}

@media (max-width: 991px) {
  .u-section-1 .u-sheet-1 {
    min-height: 300px;
  }

  .u-section-1 .u-text-1 {
    margin-top: -117px;
  }

  .u-section-1 .u-text-2 {
    margin-bottom: -117px;
  }
}

@media (max-width: 767px) {
  .u-section-1 .u-sheet-1 {
    min-height: 244px;
  }

  .u-section-1 .u-text-1 {
    margin-top: -455px;
  }

  .u-section-1 .u-text-2 {
    margin-bottom: -455px;
  }
}

@media (max-width: 575px) {
  .u-section-1 .u-sheet-1 {
    min-height: 284px;
  }

  .u-section-1 .u-text-1 {
    margin-top: -640px;
  }

  .u-section-1 .u-text-2 {
    margin-bottom: -640px;
  }
} .u-section-2 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-2 .u-sheet-1 {
  min-height: 415px;
}

.u-section-2 .u-list-1 {
  margin-bottom: 20px;
  margin-top: 20px;
}

.u-section-2 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 375px;
  --gap: 10px;
}

.u-section-2 .u-list-item-1 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-container-layout-1 {
  padding: 10px;
}

.u-section-2 .u-image-1 {
  height: 232px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 20px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-btn-1 {
  border-style: solid;
  font-size: 1.5rem;
  transition-duration: 0.5s;
  margin: 10px auto 0 0;
  padding: 0;
}

.u-section-2 .u-text-1 {
  margin: 20px auto 0 0;
}

.u-section-2 .u-list-item-2 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-container-layout-2 {
  padding: 10px;
}

.u-section-2 .u-image-2 {
  height: 232px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 20px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-btn-2 {
  border-style: solid;
  font-size: 1.5rem;
  transition-duration: 0.5s;
  margin: 10px auto 0 0;
  padding: 0;
}

.u-section-2 .u-text-2 {
  margin: 20px auto 0 0;
}

.u-section-2 .u-list-item-3 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-container-layout-3 {
  padding: 10px;
}

.u-section-2 .u-image-3 {
  height: 232px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 20px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-btn-3 {
  border-style: solid;
  font-size: 1.5rem;
  transition-duration: 0.5s;
  margin: 10px auto 0 0;
  padding: 0;
}

.u-section-2 .u-text-3 {
  margin: 20px auto 0 0;
}

@media (max-width: 1199px) {
  .u-section-2 .u-sheet-1 {
    min-height: 373px;
  }

  .u-section-2 .u-list-1 {
    margin-bottom: -22px;
  }

  .u-section-2 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 313px;
    grid-gap: 10px;
  }

  .u-section-2 .u-image-1 {
    height: 191px;
  }

  .u-section-2 .u-image-2 {
    height: 191px;
  }

  .u-section-2 .u-image-3 {
    height: 191px;
  }
}

@media (max-width: 991px) {
  .u-section-2 .u-sheet-1 {
    min-height: 415px;
  }

  .u-section-2 .u-list-1 {
    margin-bottom: 20px;
  }

  .u-section-2 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 732px;
  }

  .u-section-2 .u-image-1 {
    height: 226px;
  }

  .u-section-2 .u-image-2 {
    height: 226px;
  }

  .u-section-2 .u-image-3 {
    height: 226px;
  }
}

@media (max-width: 767px) {
  .u-section-2 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-2 .u-image-1 {
    height: 318px;
  }

  .u-section-2 .u-image-2 {
    height: 318px;
  }

  .u-section-2 .u-image-3 {
    height: 318px;
  }
}

@media (max-width: 575px) {
  .u-section-2 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-2 .u-image-1 {
    height: 196px;
  }

  .u-section-2 .u-image-2 {
    height: 196px;
  }

  .u-section-2 .u-image-3 {
    height: 196px;
  }
}

.u-section-2 .u-btn-1,
.u-section-2 .u-btn-1:before,
.u-section-2 .u-btn-1 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 .u-btn-1 {
  border-color: transparent !important;
}

.u-section-2 :hover > .u-container-layout .u-btn-1 {
  border-color: #000000 !important;
}

.u-section-2 .u-btn-1 {
  border-color: transparent !important;
}

.u-section-2 .hover > .u-container-layout .u-btn-1 {
  border-color: #000000 !important;
}

.u-section-2 .u-btn-3,
.u-section-2 .u-btn-3:before,
.u-section-2 .u-btn-3 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 .u-btn-3 {
  border-color: transparent !important;
}

.u-section-2 :hover > .u-container-layout .u-btn-3 {
  border-color: #000000 !important;
}

.u-section-2 .u-btn-3 {
  border-color: transparent !important;
}

.u-section-2 .hover > .u-container-layout .u-btn-3 {
  border-color: #000000 !important;
}

.u-section-2 .u-btn-2,
.u-section-2 .u-btn-2:before,
.u-section-2 .u-btn-2 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 .u-btn-2 {
  border-color: transparent !important;
}

.u-section-2 :hover > .u-container-layout .u-btn-2 {
  border-color: #000000 !important;
}

.u-section-2 .u-btn-2 {
  border-color: transparent !important;
}

.u-section-2 .hover > .u-container-layout .u-btn-2 {
  border-color: #000000 !important;
}

.u-section-2 .u-image-3,
.u-section-2 .u-image-3:before,
.u-section-2 .u-image-3 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-3 {
  border-radius: 30px !important;
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-3 {
  border-radius: 30px !important;
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-1,
.u-section-2 .u-image-1:before,
.u-section-2 .u-image-1 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-1 {
  border-radius: 30px !important;
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-1 {
  border-radius: 30px !important;
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-2,
.u-section-2 .u-image-2:before,
.u-section-2 .u-image-2 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-2 {
  border-radius: 30px !important;
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-2 {
  border-radius: 30px !important;
  transform: translateX(0px) translateY(-5px) !important;
} .u-section-3 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-3 .u-sheet-1 {
  min-height: 374px;
}

.u-section-3 .u-shape-1 {
  --radius: 20px;
  height: 248px;
  margin-top: 106px;
  margin-bottom: 0;
}

.u-section-3 .u-group-1 {
  --radius: 30px;
  width: 489px;
  min-height: 60px;
  height: auto;
  margin: -278px auto 0;
}

.u-section-3 .u-container-layout-1 {
  padding-left: 30px;
  padding-right: 30px;
}

.u-section-3 .u-text-1 {
  font-size: 1.5rem;
  font-weight: 400;
  margin: 13px 37px 0;
}

.u-section-3 .u-list-1 {
  width: 1001px;
  margin: 21px auto 60px;
}

.u-section-3 .u-repeater-1 {
  grid-auto-columns: calc(50% - 5px);
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 147px;
  --gap: 10px;
}

.u-section-3 .u-list-item-1 {
  box-shadow: 5px 5px 8px 0 rgba(224,229,235,0.4);
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.5));
  background-size: cover;
  --radius: 20px;
}

.u-section-3 .u-container-layout-2 {
  padding: 10px;
}

.u-section-3 .u-group-2 {
  --radius: 20px;
  width: 120px;
  min-height: 120px;
  height: auto;
  margin: 3px auto 0 5px;
}

.u-section-3 .u-container-layout-3 {
  padding-bottom: 0;
  padding-top: 0;
}

.u-section-3 .u-image-1 {
  height: 99px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-3 .u-text-2 {
  font-size: 1.5rem;
  margin: -108px 1px 0 141px;
}

.u-section-3 .u-btn-1 {
  margin: 15px auto 0 141px;
  padding: 0;
}

.u-section-3 .u-list-item-2 {
  box-shadow: 5px 5px 8px 0 rgba(224,229,235,0.4);
  transition-duration: 0.5s;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0.5));
  background-size: cover;
  --radius: 20px;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-3 .u-container-layout-4 {
  padding: 10px;
}

.u-section-3 .u-group-3 {
  --radius: 20px;
  width: 120px;
  min-height: 120px;
  height: auto;
  margin: 3px auto 0 5px;
}

.u-section-3 .u-container-layout-5 {
  padding-bottom: 0;
  padding-top: 0;
}

.u-section-3 .u-image-2 {
  height: 99px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-3 .u-text-3 {
  font-size: 1.5rem;
  margin: -108px 1px 0 141px;
}

.u-section-3 .u-btn-2 {
  margin: 15px auto 0 141px;
  padding: 0;
}

@media (max-width: 1199px) {
  .u-section-3 .u-sheet-1 {
    min-height: 368px;
  }

  .u-section-3 .u-shape-1 {
    height: 260px;
    margin-top: 68px;
  }

  .u-section-3 .u-group-1 {
    min-height: 56px;
    margin-top: -288px;
    height: auto;
  }

  .u-section-3 .u-text-1 {
    width: auto;
    margin-top: 11px;
  }

  .u-section-3 .u-list-1 {
    width: 911px;
    margin-bottom: -479px;
  }

  .u-section-3 .u-repeater-1 {
    min-height: 162px;
    grid-gap: 10px;
  }

  .u-section-3 .u-group-2 {
    height: auto;
  }

  .u-section-3 .u-text-2 {
    margin-right: 0;
  }

  .u-section-3 .u-btn-1 {
    margin-left: 140px;
  }

  .u-section-3 .u-group-3 {
    height: auto;
  }

  .u-section-3 .u-text-3 {
    margin-right: 0;
  }

  .u-section-3 .u-btn-2 {
    margin-left: 140px;
  }
}

@media (max-width: 991px) {
  .u-section-3 .u-sheet-1 {
    min-height: 331px;
  }

  .u-section-3 .u-shape-1 {
    height: 233px;
  }

  .u-section-3 .u-group-1 {
    width: 454px;
    min-height: 60px;
    margin-top: -265px;
  }

  .u-section-3 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-3 .u-text-1 {
    margin-top: 13px;
    margin-left: 40px;
    margin-right: 40px;
  }

  .u-section-3 .u-list-1 {
    width: 740px;
    margin-top: 15px;
    margin-bottom: -3px;
  }

  .u-section-3 .u-repeater-1 {
    min-height: 147px;
  }

  .u-section-3 .u-text-2 {
    width: auto;
    margin-top: -119px;
    margin-left: 139px;
  }

  .u-section-3 .u-btn-1 {
    margin-right: 98px;
    margin-left: auto;
  }

  .u-section-3 .u-text-3 {
    width: auto;
    margin-top: -119px;
    margin-left: 139px;
  }

  .u-section-3 .u-btn-2 {
    margin-right: 98px;
    margin-left: auto;
  }
}

@media (max-width: 767px) {
  .u-section-3 .u-sheet-1 {
    min-height: 423px;
  }

  .u-section-3 .u-shape-1 {
    height: 348px;
    margin-top: 45px;
  }

  .u-section-3 .u-group-1 {
    width: 385px;
    min-height: 53px;
    margin-top: -363px;
  }

  .u-section-3 .u-text-1 {
    margin-top: 12px;
    margin-left: 24px;
    margin-right: 24px;
    font-size: 1.25rem;
  }

  .u-section-3 .u-list-1 {
    width: 500px;
    margin-bottom: -908px;
  }

  .u-section-3 .u-repeater-1 {
    grid-auto-columns: calc(100% + 0px);
    grid-template-columns: 100%;
    min-height: 265px;
  }

  .u-section-3 .u-container-layout-2 {
    padding-top: 0;
    padding-bottom: 19px;
  }

  .u-section-3 .u-group-2 {
    width: 93px;
    min-height: 93px;
    margin-top: 13px;
  }

  .u-section-3 .u-image-1 {
    height: 60px;
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-3 .u-text-2 {
    margin-top: -84px;
    margin-right: 33px;
    margin-left: 118px;
  }

  .u-section-3 .u-btn-1 {
    margin-top: 9px;
    margin-right: auto;
    margin-left: 118px;
  }

  .u-section-3 .u-container-layout-4 {
    padding-top: 0;
    padding-bottom: 19px;
  }

  .u-section-3 .u-group-3 {
    width: 93px;
    min-height: 93px;
    margin-top: 13px;
  }

  .u-section-3 .u-image-2 {
    height: 60px;
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-3 .u-text-3 {
    margin-top: -84px;
    margin-right: 33px;
    margin-left: 118px;
  }

  .u-section-3 .u-btn-2 {
    margin-top: 9px;
    margin-right: auto;
    margin-left: 118px;
  }
}

@media (max-width: 575px) {
  .u-section-3 .u-sheet-1 {
    min-height: 378px;
  }

  .u-section-3 .u-shape-1 {
    height: 291px;
    margin-top: 57px;
  }

  .u-section-3 .u-group-1 {
    min-height: 55px;
    width: 250px;
    margin-top: -319px;
  }

  .u-section-3 .u-container-layout-1 {
    padding-left: 0;
    padding-right: 0;
  }

  .u-section-3 .u-text-1 {
    font-size: 1rem;
    margin-top: 0;
    margin-left: 19px;
    margin-right: 19px;
  }

  .u-section-3 .u-list-1 {
    width: 300px;
    margin-bottom: -222px;
  }

  .u-section-3 .u-repeater-1 {
    grid-auto-columns: 100%;
    min-height: 218px;
  }

  .u-section-3 .u-container-layout-2 {
    padding-top: 10px;
    padding-bottom: 10px;
  }

  .u-section-3 .u-group-2 {
    width: 60px;
    min-height: 60px;
  }

  .u-section-3 .u-image-1 {
    height: 40px;
    margin-top: 10px;
  }

  .u-section-3 .u-text-2 {
    font-size: 1.25rem;
    margin-top: -55px;
    margin-right: 0;
    margin-left: 80px;
  }

  .u-section-3 .u-btn-1 {
    margin-top: 13px;
    margin-left: 80px;
  }

  .u-section-3 .u-container-layout-4 {
    padding-top: 10px;
    padding-bottom: 10px;
  }

  .u-section-3 .u-group-3 {
    width: 60px;
    min-height: 60px;
  }

  .u-section-3 .u-image-2 {
    height: 40px;
    margin-top: 10px;
  }

  .u-section-3 .u-text-3 {
    font-size: 1.25rem;
    margin-top: -55px;
    margin-right: 0;
    margin-left: 80px;
  }

  .u-section-3 .u-btn-2 {
    margin-top: 13px;
    margin-left: 80px;
  }
}

.u-block-6dc9-13:not([data-block-selected]):not([data-cell-selected]),
.u-block-6dc9-13:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-6dc9-13:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-6dc9-13.u-block-6dc9-13.u-block-6dc9-13:hover:not([data-block-selected]):not([data-cell-selected]) {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-block-6dc9-13.u-block-6dc9-13.u-block-6dc9-13.hover {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-3 .u-list-item-1,
.u-section-3 .u-list-item-1:before,
.u-section-3 .u-list-item-1 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 .u-list-item-1.u-list-item-1.u-list-item-1:hover {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-3 .u-list-item-1.u-list-item-1.u-list-item-1.hover {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-3 .u-list-item-2,
.u-section-3 .u-list-item-2:before,
.u-section-3 .u-list-item-2 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 .u-list-item-2.u-list-item-2.u-list-item-2:hover {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-3 .u-list-item-2.u-list-item-2.u-list-item-2.hover {
  transform: translateX(0px) translateY(-5px) !important;
} .u-section-4 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-4 .u-sheet-1 {
  min-height: 437px;
  padding: 30px 0;
}

.u-section-4 .u-group-1 {
  --radius: 20px;
  min-height: 329px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
}

.u-section-4 .u-container-layout-1 {
  padding: 0 20px;
}

.u-section-4 .u-text-1 {
  letter-spacing: 2px;
  font-weight: 400;
  margin: 28px 855px 0 20px;
}

.u-section-4 .u-list-1 {
  width: 514px;
  margin: 30px auto 0 10px;
}

.u-section-4 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 226px;
  --gap: 10px;
}

.u-section-4 .u-container-layout-2 {
  padding: 10px;
}

.u-section-4 .u-text-2 {
  margin-bottom: 0;
  margin-top: 0;
  font-weight: 300;
  font-size: 1.125rem;
}

.u-section-4 .u-container-layout-3 {
  padding: 10px;
}

.u-section-4 .u-text-3 {
  margin-bottom: 0;
  margin-top: 0;
  font-weight: 300;
  font-size: 1.125rem;
}

.u-section-4 .u-container-layout-4 {
  padding: 10px;
}

.u-section-4 .u-text-4 {
  margin-bottom: 0;
  margin-top: 0;
  font-weight: 300;
  font-size: 1.125rem;
}

.u-section-4 .u-container-layout-5 {
  padding: 10px;
}

.u-section-4 .u-text-5 {
  margin-bottom: 0;
  margin-top: 0;
  font-weight: 300;
  font-size: 1.125rem;
}

.u-section-4 .u-gallery-nav-1 {
  position: absolute;
  left: auto;
  width: 40px;
  height: 40px;
  top: 167px;
  right: 100px;
}

.u-section-4 .u-gallery-nav-2 {
  position: absolute;
  width: 40px;
  height: 40px;
  left: auto;
  top: 167px;
  right: 41px;
}

.u-section-4 .u-image-1 {
  width: 459px;
  height: 297px;
  --radius: 20px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  margin: -250px 86px -343px auto;
}

@media (max-width: 1199px) {
  .u-section-4 .u-sheet-1 {
    min-height: 436px;
  }

  .u-section-4 .u-group-1 {
    margin-top: -172px;
    height: auto;
  }

  .u-section-4 .u-text-1 {
    margin-right: 675px;
    margin-left: 0;
  }

  .u-section-4 .u-list-1 {
    width: 465px;
  }

  .u-section-4 .u-repeater-1 {
    grid-template-columns: 100%;
    grid-gap: 10px;
  }

  .u-section-4 .u-image-1 {
    margin-right: 40px;
    margin-bottom: -172px;
  }
}

@media (max-width: 991px) {
  .u-section-4 .u-sheet-1 {
    padding-left: 30px;
    padding-right: 30px;
  }

  .u-section-4 .u-group-1 {
    margin-top: 0;
  }

  .u-section-4 .u-text-1 {
    margin-right: 455px;
  }

  .u-section-4 .u-list-1 {
    width: 369px;
  }

  .u-section-4 .u-image-1 {
    width: 275px;
    height: 239px;
    margin-top: -192px;
    margin-bottom: 0;
  }
}

@media (max-width: 767px) {
  .u-section-4 .u-sheet-1 {
    min-height: 462px;
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-text-1 {
    width: auto;
    margin-top: 30px;
    margin-right: 235px;
  }

  .u-section-4 .u-list-1 {
    margin-top: 0;
    margin-right: initial;
    margin-left: initial;
    width: auto;
  }

  .u-section-4 .u-text-2 {
    font-size: 1rem;
  }

  .u-section-4 .u-text-3 {
    font-size: 1rem;
  }

  .u-section-4 .u-text-4 {
    font-size: 1rem;
  }

  .u-section-4 .u-text-5 {
    font-size: 1rem;
  }

  .u-section-4 .u-image-1 {
    height: 163px;
    margin: -89px auto -73px 20px;
  }
}

@media (max-width: 575px) {
  .u-section-4 .u-sheet-1 {
    min-height: 454px;
    padding-left: 0;
    padding-right: 0;
  }

  .u-section-4 .u-group-1 {
    min-height: 364px;
  }

  .u-section-4 .u-text-1 {
    margin-right: 35px;
  }

  .u-section-4 .u-list-1 {
    margin-top: 10px;
    width: auto;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-4 .u-container-layout-2 {
    padding: 0;
  }

  .u-section-4 .u-container-layout-3 {
    padding: 0;
  }

  .u-section-4 .u-container-layout-4 {
    padding: 0;
  }

  .u-section-4 .u-container-layout-5 {
    padding: 0;
  }

  .u-section-4 .u-gallery-nav-1 {
    top: 200px;
  }

  .u-section-4 .u-gallery-nav-2 {
    top: 200px;
    right: 45px;
  }

  .u-section-4 .u-image-1 {
    width: 182px;
    height: 97px;
    margin-top: -67px;
    margin-bottom: -11px;
    margin-left: 30px;
  }
}

.u-section-4 .u-image-1,
.u-section-4 .u-image-1:before,
.u-section-4 .u-image-1 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-4 .u-image-1.u-image-1.u-image-1:hover {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-4 .u-image-1.u-image-1.u-image-1.hover {
  transform: translateX(0px) translateY(-5px) !important;
} .u-section-5 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-5 .u-sheet-1 {
  min-height: 413px;
  padding: 30px 0;
}

.u-section-5 .u-group-1 {
  --radius: 20px;
  min-height: 280px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
}

.u-section-5 .u-container-layout-1 {
  padding: 0 20px;
}

.u-section-5 .u-text-1 {
  letter-spacing: 2px;
  font-weight: 400;
  margin: 24px 296px 0 579px;
}

.u-section-5 .u-text-2 {
  font-weight: 300;
  margin: 24px 0 0 579px;
}

.u-section-5 .u-image-1 {
  width: 459px;
  height: 297px;
  --radius: 20px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  margin: -224px auto -79px 86px;
}

@media (max-width: 1199px) {
  .u-section-5 .u-sheet-1 {
    min-height: 412px;
  }

  .u-section-5 .u-group-1 {
    margin-top: -40px;
    height: auto;
  }

  .u-section-5 .u-text-1 {
    width: auto;
    margin-right: 171px;
    margin-left: 505px;
  }

  .u-section-5 .u-text-2 {
    font-size: 1.125rem;
    width: auto;
    margin-right: 40px;
    margin-left: 505px;
  }

  .u-section-5 .u-image-1 {
    margin-bottom: -40px;
    margin-left: 40px;
  }
}

@media (max-width: 991px) {
  .u-section-5 .u-sheet-1 {
    min-height: 350px;
    padding-left: 30px;
    padding-right: 30px;
  }

  .u-section-5 .u-group-1 {
    min-height: 242px;
    margin-top: 0;
  }

  .u-section-5 .u-text-1 {
    margin-right: 61px;
    margin-left: 395px;
  }

  .u-section-5 .u-text-2 {
    margin-right: 30px;
    margin-left: 395px;
  }

  .u-section-5 .u-image-1 {
    width: 275px;
    height: 239px;
    margin-top: -191px;
    margin-bottom: -53px;
  }
}

@media (max-width: 767px) {
  .u-section-5 .u-sheet-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-5 .u-group-1 {
    min-height: 210px;
  }

  .u-section-5 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-5 .u-text-1 {
    margin-top: 30px;
    margin-right: 236px;
    margin-left: 0;
  }

  .u-section-5 .u-text-2 {
    font-size: 1rem;
    margin-top: 0;
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-5 .u-image-1 {
    height: 162px;
    margin: -82px 20px 0 auto;
  }
}

@media (max-width: 575px) {
  .u-section-5 .u-sheet-1 {
    min-height: 317px;
  }

  .u-section-5 .u-text-1 {
    margin-right: 36px;
  }

  .u-section-5 .u-text-2 {
    margin-top: 10px;
  }

  .u-section-5 .u-image-1 {
    width: 182px;
    height: 97px;
    margin-top: -49px;
    margin-right: 27px;
  }
}

.u-section-5 .u-image-1,
.u-section-5 .u-image-1:before,
.u-section-5 .u-image-1 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-5 .u-image-1.u-image-1.u-image-1:hover {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-5 .u-image-1.u-image-1.u-image-1.hover {
  transform: translateX(0px) translateY(-5px) !important;
} .u-section-6 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-6 .u-sheet-1 {
  min-height: 622px;
}

.u-section-6 .u-text-1 {
  letter-spacing: 2px;
  font-weight: 400;
  margin: 30px 20px 0 0;
}

.u-section-6 .u-accordion-1 {
  margin-top: 30px;
  margin-bottom: 20px;
}

.u-section-6 .u-accordion-link-1 {
  border-style: solid;
  --radius: 20px;
  font-family: Figtree;
  font-size: 1.5rem;
  padding: 20px;
}

.u-section-6 .u-icon-1 {
  height: 20px;
  width: 20px;
  background-image: none;
  color: rgb(255, 255, 255);
  padding: 4px;
}

.u-section-6 .u-accordion-pane-1 {
  min-height: 150px;
}

.u-section-6 .u-container-layout-1 {
  padding: 20px;
}

.u-section-6 .u-accordion-item-2 {
  margin-bottom: 0;
}

.u-section-6 .u-accordion-link-2 {
  border-style: solid;
  --radius: 20px;
  font-family: Figtree;
  font-size: 1.5rem;
  padding: 20px;
}

.u-section-6 .u-icon-2 {
  height: 20px;
  width: 20px;
  background-image: none;
  color: rgb(255, 255, 255);
  padding: 4px;
}

.u-section-6 .u-accordion-pane-2 {
  min-height: 150px;
}

.u-section-6 .u-container-layout-2 {
  padding: 20px;
}

.u-section-6 .u-accordion-item-3 {
  margin-bottom: 0;
}

.u-section-6 .u-accordion-link-3 {
  border-style: solid;
  --radius: 20px;
  font-family: Figtree;
  font-size: 1.5rem;
  padding: 20px;
}

.u-section-6 .u-icon-3 {
  height: 20px;
  width: 20px;
  background-image: none;
  color: rgb(255, 255, 255);
  padding: 4px;
}

.u-section-6 .u-accordion-pane-3 {
  min-height: 150px;
}

.u-section-6 .u-container-layout-3 {
  padding: 20px;
}

.u-section-6 .u-accordion-item-4 {
  margin-bottom: 0;
}

.u-section-6 .u-accordion-link-4 {
  border-style: solid;
  --radius: 20px;
  font-family: Figtree;
  font-size: 1.5rem;
  padding: 20px;
}

.u-section-6 .u-icon-4 {
  height: 20px;
  width: 20px;
  background-image: none;
  color: rgb(255, 255, 255);
  padding: 4px;
}

.u-section-6 .u-accordion-pane-4 {
  min-height: 150px;
}

.u-section-6 .u-container-layout-4 {
  padding: 20px;
}

.u-section-6 .u-accordion-link-5 {
  border-style: solid;
  --radius: 20px;
  font-family: Figtree;
  font-size: 1.5rem;
  padding: 20px;
}

.u-section-6 .u-icon-5 {
  height: 20px;
  width: 20px;
  background-image: none;
  color: rgb(255, 255, 255);
  padding: 4px;
}

.u-section-6 .u-accordion-pane-5 {
  min-height: 150px;
}

.u-section-6 .u-container-layout-5 {
  padding: 20px;
}

@media (max-width: 1199px) {
  .u-section-6 .u-text-1 {
    margin-right: 0;
  }
}

@media (max-width: 767px) {
  .u-section-6 .u-accordion-link-1 {
    font-size: 1.3333333333333333rem;
  }

  .u-section-6 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-6 .u-accordion-link-2 {
    font-size: 1.3333333333333333rem;
  }

  .u-section-6 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-6 .u-accordion-link-3 {
    font-size: 1.3333333333333333rem;
  }

  .u-section-6 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-6 .u-accordion-link-4 {
    font-size: 1.3333333333333333rem;
  }

  .u-section-6 .u-container-layout-4 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-6 .u-accordion-link-5 {
    font-size: 1.3333333333333333rem;
  }

  .u-section-6 .u-container-layout-5 {
    padding-left: 10px;
    padding-right: 10px;
  }
} .u-section-7 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-7 .u-sheet-1 {
  min-height: 495px;
}

.u-section-7 .u-text-1 {
  margin: 10px 0 0;
}

.u-section-7 .u-layout-wrap-1 {
  margin-top: 10px;
  margin-bottom: 57px;
}

.u-section-7 .u-layout-cell-1 {
  min-height: 409px;
  --radius: 30px;
}

.u-section-7 .u-container-layout-1 {
  padding: 30px 29px;
}

.u-section-7 .u-text-2 {
  margin: 0 auto 0 1px;
}

.u-section-7 .u-text-3 {
  font-size: 1.125rem;
  font-weight: 300;
  margin: 271px 0 0;
}

.u-section-7 .u-layout-cell-2 {
  min-height: 409px;
}

.u-section-7 .u-container-layout-2 {
  padding: 0;
}

.u-section-7 .u-blog-1 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-7 .u-repeater-1 {
  grid-template-columns: repeat(2, calc(50% - 11px));
  min-height: 180px;
  grid-auto-columns: calc(50% - 11px);
  --gap: 22px;
}

.u-section-7 .u-repeater-item-1 {
  --radius: 20px;
}

.u-section-7 .u-container-layout-3 {
  padding: 30px 0;
}

.u-section-7 .u-image-1 {
  height: 249px;
  margin-top: -30px;
  margin-bottom: 0;
  --top-left-radius: 20px;
  --top-right-radius: 20px;
}

.u-section-7 .u-text-4 {
  font-size: 1.125rem;
  margin: 20px 20px 0;
}

.u-section-7 .u-btn-1 {
  background-image: none;
  border-style: solid;
  margin: 20px auto 0 20px;
  padding: 0;
}

.u-section-7 .u-repeater-item-2 {
  --radius: 20px;
}

.u-section-7 .u-container-layout-4 {
  padding: 30px 0;
}

.u-section-7 .u-image-2 {
  height: 249px;
  margin-top: -30px;
  margin-bottom: 0;
  --top-left-radius: 20px;
  --top-right-radius: 20px;
}

.u-section-7 .u-text-5 {
  font-size: 1.125rem;
  margin: 20px 20px 0;
}

.u-section-7 .u-btn-2 {
  background-image: none;
  border-style: solid;
  margin: 20px auto 0 20px;
  padding: 0;
}

.u-section-7 .u-gallery-nav-1 {
  position: absolute;
  left: 10px;
  width: 40px;
  height: 40px;
}

.u-section-7 .u-gallery-nav-2 {
  position: absolute;
  right: 10px;
  width: 40px;
  height: 40px;
}

@media (max-width: 1199px) {
  .u-section-7 .u-sheet-1 {
    min-height: 490px;
  }

  .u-section-7 .u-layout-wrap-1 {
    margin-bottom: -11px;
  }

  .u-section-7 .u-layout-cell-1 {
    min-height: 361px;
  }

  .u-section-7 .u-text-3 {
    width: auto;
    font-size: 1rem;
    margin-top: 69px;
  }

  .u-section-7 .u-layout-cell-2 {
    min-height: 351px;
  }

  .u-section-7 .u-repeater-1 {
    min-height: 341px;
    grid-gap: 22px;
  }

  .u-section-7 .u-image-1 {
    height: 207px;
  }

  .u-section-7 .u-text-4 {
    width: auto;
    margin-right: 28px;
  }

  .u-section-7 .u-container-layout-4 {
    padding-bottom: 24px;
  }

  .u-section-7 .u-image-2 {
    height: 207px;
  }

  .u-section-7 .u-text-5 {
    width: auto;
    margin-right: 28px;
  }

  .u-section-7 .u-gallery-nav-1 {
    left: 3px;
    top: 159px;
  }

  .u-section-7 .u-gallery-nav-2 {
    left: auto;
    top: 157px;
    right: 5px;
  }
}

@media (max-width: 991px) {
  .u-section-7 .u-sheet-1 {
    min-height: 479px;
  }

  .u-section-7 .u-layout-wrap-1 {
    margin-bottom: 60px;
  }

  .u-section-7 .u-layout-cell-1 {
    min-height: 385px;
  }

  .u-section-7 .u-text-2 {
    width: auto;
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-7 .u-text-3 {
    margin-top: 144px;
  }

  .u-section-7 .u-layout-cell-2 {
    min-height: 385px;
  }

  .u-section-7 .u-repeater-1 {
    grid-template-columns: 100%;
    min-height: 367px;
    grid-auto-columns: calc(100% + 0px);
  }

  .u-section-7 .u-container-layout-3 {
    padding-top: 0;
    padding-bottom: 0;
  }

  .u-section-7 .u-image-1 {
    height: 250px;
    margin-top: 0;
  }

  .u-section-7 .u-text-4 {
    margin-right: 20px;
  }

  .u-section-7 .u-container-layout-4 {
    padding-top: 0;
    padding-bottom: 0;
  }

  .u-section-7 .u-image-2 {
    height: 250px;
    margin-top: 0;
  }

  .u-section-7 .u-text-5 {
    margin-right: 20px;
  }
}

@media (max-width: 767px) {
  .u-section-7 .u-sheet-1 {
    min-height: 579px;
  }

  .u-section-7 .u-layout-wrap-1 {
    position: relative;
  }

  .u-section-7 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-7 .u-container-layout-1 {
    padding-left: 30px;
    padding-right: 30px;
  }

  .u-section-7 .u-layout-cell-2 {
    min-height: 386px;
  }

  .u-section-7 .u-repeater-1 {
    min-height: 366px;
    grid-auto-columns: 100%;
  }

  .u-section-7 .u-image-1 {
    height: 253px;
  }

  .u-section-7 .u-image-2 {
    height: 253px;
  }
}

@media (max-width: 575px) {
  .u-section-7 .u-container-layout-1 {
    padding: 20px;
  }

  .u-section-7 .u-layout-cell-2 {
    min-height: 297px;
  }

  .u-section-7 .u-blog-1 {
    min-height: 277px;
  }

  .u-section-7 .u-repeater-1 {
    min-height: 277px;
  }

  .u-section-7 .u-image-1 {
    height: 159px;
  }

  .u-section-7 .u-text-4 {
    margin-right: 30px;
  }

  .u-section-7 .u-btn-1 {
    margin-top: 19px;
  }

  .u-section-7 .u-image-2 {
    height: 159px;
  }

  .u-section-7 .u-text-5 {
    margin-right: 30px;
  }

  .u-section-7 .u-btn-2 {
    margin-top: 19px;
  }

  .u-section-7 .u-gallery-nav-1 {
    top: 119px;
  }

  .u-section-7 .u-gallery-nav-2 {
    top: 119px;
    right: 6px;
  }
}