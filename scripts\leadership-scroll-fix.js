/**
 * Leadership Page Scroll Fix
 * This script fixes scrolling issues on the leadership page, especially for touchpad users
 */

document.addEventListener('DOMContentLoaded', function() {
  console.log('Leadership scroll fix utility loaded');

  // Fix for main page scrolling - these empty handlers help with touch devices
  document.addEventListener('touchstart', function() {}, { passive: true });
  document.addEventListener('touchmove', function() {}, { passive: true });

  // Ensure leaders container is properly sized
  setTimeout(function() {
    const leadersContainer = document.getElementById('leaders-container');
    if (leadersContainer) {
      console.log('Ensuring leaders container is scrollable');
      // Force a reflow to ensure proper height calculation
      leadersContainer.style.minHeight = leadersContainer.scrollHeight + 'px';
    }
  }, 1500);

  // Detect touchpad vs mouse wheel
  window.addEventListener('wheel', wheelEventHandler, { passive: true });
});

// Detect touchpad vs mouse wheel
function wheelEventHandler(e) {
  // When touchpad is detected
  if (e.deltaMode === 0) {
    // Make sure the page is scrollable with touchpad
    document.documentElement.style.height = 'auto';
    document.body.style.height = 'auto';
    document.body.style.overflowY = 'visible';
    document.body.classList.add('using-touchpad');
  }
}

// Handle window resize events to fix scrolling issues
window.addEventListener('resize', function() {
  console.log('Window resized - updating document height');
  // Force document to recalculate its height
  document.body.style.height = 'auto';
  document.documentElement.style.height = 'auto';

  // Update leaders container height
  const leadersContainer = document.getElementById('leaders-container');
  if (leadersContainer) {
    leadersContainer.style.minHeight = 'auto';
    // Force a reflow
    void leadersContainer.offsetHeight;
    // Set min-height to ensure all content is visible
    leadersContainer.style.minHeight = leadersContainer.scrollHeight + 'px';
  }
});

// Fix for modal scrolling issues
document.addEventListener('DOMContentLoaded', function() {
  const modal = document.getElementById('leader-modal');
  const modalInner = modal ? modal.querySelector('.modal-inner') : null;
  const modalContent = modal ? modal.querySelector('.modal-content') : null;

  if (modal && modalInner) {
    // Prevent touchmove events from being blocked
    modalInner.addEventListener('touchmove', function(e) {
      // Don't prevent default to allow natural scrolling
      e.stopPropagation();
    }, { passive: true });

    // Handle wheel events in modal
    modalInner.addEventListener('wheel', function(e) {
      // Let the browser handle the scrolling naturally
      console.log('Wheel event detected in modal inner');
      e.stopPropagation();
    }, { passive: true });

    // Ensure modal content is scrollable
    if (modalContent) {
      modalContent.addEventListener('touchmove', function(e) {
        e.stopPropagation();
      }, { passive: true });

      modalContent.addEventListener('wheel', function(e) {
        e.stopPropagation();
      }, { passive: true });
    }

    // When modal opens, ensure body doesn't scroll
    const observer = new MutationObserver(function(mutations) {
      mutations.forEach(function(mutation) {
        if (mutation.attributeName === 'style') {
          const displayStyle = modal.style.display;
          if (displayStyle === 'block') {
            document.body.classList.add('modal-open');
            // Force a reflow to ensure modal is scrollable
            setTimeout(function() {
              if (modalInner) {
                modalInner.style.overflowY = 'auto';
                modalInner.style.webkitOverflowScrolling = 'touch';
              }
            }, 100);
          } else {
            document.body.classList.remove('modal-open');
          }
        }
      });
    });

    observer.observe(modal, { attributes: true });

    // Add additional event listeners to ensure scrolling works
    document.addEventListener('touchmove', function(e) {
      if (modal.style.display === 'block') {
        if (!modalInner.contains(e.target) && !e.target.classList.contains('modal-inner')) {
          e.preventDefault();
        }
      }
    }, { passive: false });
  }

  // Fix for iOS momentum scrolling
  if (modal) {
    modal.addEventListener('touchstart', function(e) {
      if (modalInner && modalInner.contains(e.target)) {
        modalInner.style.overflowY = 'auto';
      }
    }, { passive: true });
  }
});
