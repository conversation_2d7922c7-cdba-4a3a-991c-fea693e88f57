interface ApprovalField {
  name: string;
  type: 'text' | 'signature' | 'time' | 'date';
  required?: boolean;
}

interface ApprovalData {
  [key: string]: any;
}

interface ValidationResult {
  isValid: boolean;
  errors: string[];
  missingApprovals: string[];
}

/**
 * Validates approval data to ensure required fields are filled
 */
export const validateApprovalData = (
  data: ApprovalData[],
  tableHeaders: ApprovalField[],
  sectionName: string = 'Approval'
): ValidationResult => {
  const errors: string[] = [];
  const missingApprovals: string[] = [];

  // Check if at least one row has all required fields filled
  const hasValidApprover = data.some(row => {
    return tableHeaders.filter(header => header.required).every(header => {
      const value = row[header.name];
      return value && value.toString().trim() !== '';
    });
  });

  if (!hasValidApprover) {
    errors.push(`${sectionName} section requires at least one complete approval`);
    missingApprovals.push(sectionName);
  }

  // Check for incomplete rows that have some data but are missing required fields
  data.forEach((row, index) => {
    const hasAnyData = Object.values(row).some(value => value && value.toString().trim() !== '');
    if (hasAnyData) {
      const missingFields = tableHeaders
        .filter(header => header.required)
        .filter(header => !row[header.name] || row[header.name].toString().trim() === '')
        .map(header => header.name);
      
      if (missingFields.length > 0) {
        errors.push(`${sectionName} row ${index + 1} is missing: ${missingFields.join(', ')}`);
      }
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    missingApprovals
  };
};

/**
 * Validates additional approval fields (like observer information)
 */
export const validateAdditionalFields = (
  data: { [key: string]: any },
  fields: ApprovalField[],
  sectionName: string = 'Additional Fields'
): ValidationResult => {
  const errors: string[] = [];
  const missingApprovals: string[] = [];

  fields.filter(field => field.required).forEach(field => {
    const value = data[field.name];
    if (!value || value.toString().trim() === '') {
      errors.push(`${field.name} is required in ${sectionName}`);
      missingApprovals.push(field.name);
    }
  });

  return {
    isValid: errors.length === 0,
    errors,
    missingApprovals
  };
};

/**
 * Validates all approval sections in a form
 */
export const validateAllApprovals = (
  approvalSections: Array<{
    name: string;
    data: ApprovalData[];
    headers: ApprovalField[];
    additionalFields?: ApprovalField[];
    additionalData?: { [key: string]: any };
  }>
): ValidationResult => {
  const allErrors: string[] = [];
  const allMissingApprovals: string[] = [];

  approvalSections.forEach(section => {
    // Validate main approval data
    const mainValidation = validateApprovalData(section.data, section.headers, section.name);
    allErrors.push(...mainValidation.errors);
    allMissingApprovals.push(...mainValidation.missingApprovals);

    // Validate additional fields if present
    if (section.additionalFields && section.additionalData) {
      const additionalValidation = validateAdditionalFields(
        section.additionalData,
        section.additionalFields,
        section.name
      );
      allErrors.push(...additionalValidation.errors);
      allMissingApprovals.push(...additionalValidation.missingApprovals);
    }
  });

  return {
    isValid: allErrors.length === 0,
    errors: allErrors,
    missingApprovals: allMissingApprovals
  };
};

/**
 * Creates a user-friendly error message for form submission
 */
export const createSubmissionErrorMessage = (validationResult: ValidationResult): string => {
  if (validationResult.isValid) {
    return '';
  }

  const { errors, missingApprovals } = validationResult;
  
  let message = 'Cannot submit form due to missing approvals:\n\n';
  
  if (missingApprovals.length > 0) {
    message += `Missing approvals in: ${missingApprovals.join(', ')}\n\n`;
  }
  
  message += 'Details:\n';
  errors.forEach(error => {
    message += `• ${error}\n`;
  });
  
  message += '\nPlease complete all required approval fields before submitting.';
  
  return message;
};

/**
 * Checks if a form can be submitted based on approval validation
 */
export const canSubmitForm = (
  approvalSections: Array<{
    name: string;
    data: ApprovalData[];
    headers: ApprovalField[];
    additionalFields?: ApprovalField[];
    additionalData?: { [key: string]: any };
  }>
): { canSubmit: boolean; errorMessage: string } => {
  const validationResult = validateAllApprovals(approvalSections);
  
  return {
    canSubmit: validationResult.isValid,
    errorMessage: validationResult.isValid ? '' : createSubmissionErrorMessage(validationResult)
  };
};
