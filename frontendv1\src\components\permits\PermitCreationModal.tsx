import React, { useState} from 'react';
import {
  X,
  Search,
  AlertTriangle,
  Clock,
  Users,
  Shield,
  ChevronRight,
  CheckCircle
} from 'lucide-react';
import { TaskTemplate, PREDEFINED_TASK_TEMPLATES, TASK_TEMPLATE_CATEGORIES } from '../../types/taskTemplates';

interface PermitCreationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (permitData: any) => void;
  siteId: string;
}

interface PermitFormData {
  // Task Selection
  selectedTaskTemplate?: TaskTemplate;
  
  // Basic Information
  title: string;
  description: string;
  location: string;
  priority: 'low' | 'medium' | 'high' | 'critical';
  
  // Timing
  requestedDate: string;
  plannedStartDate: string;
  plannedEndDate: string;
  
  // Workers
  assignedWorkers: string[];
  supervisorId: string;
  
  // Permit Type Selection
  selecteds: string[];
  
  // Risk Assessment
  identifiedHazards: string[];
  controlMeasures: string[];
  
  // Additional Requirements
  specialRequirements: string[];
  attachments: File[];
}



const mockWorkers = [
  { id: 'worker-1', name: '<PERSON>', trade: 'Welder' },
  { id: 'worker-2', name: '<PERSON>', trade: 'Electrician' },
  { id: 'worker-3', name: 'Peter Ochieng', trade: 'Supervisor' },
  { id: 'worker-4', name: 'Grace Muthoni', trade: 'Safety Officer' }
];

const PermitCreationModal: React.FC<PermitCreationModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  siteId
}) => {
  const [currentStep, setCurrentStep] = useState(1);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [formData, setFormData] = useState<PermitFormData>({
    title: '',
    description: '',
    location: '',
    priority: 'medium',
    requestedDate: new Date().toISOString().split('T')[0],
    plannedStartDate: '',
    plannedEndDate: '',
    assignedWorkers: [],
    supervisorId: '',
    selecteds: [],
    identifiedHazards: [],
    controlMeasures: [],
    specialRequirements: [],
    attachments: []
  });

  const filteredTemplates = PREDEFINED_TASK_TEMPLATES.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || template.category === selectedCategory;
    return matchesSearch && matchesCategory && template.isActive;
  });

  const handleTaskTemplateSelect = (template: TaskTemplate) => {
    setFormData(prev => ({
      ...prev,
      selectedTaskTemplate: template,
      title: template.name,
      description: template.description,
      selecteds: template.requiredPermitTypes,
      specialRequirements: [...template.safetyRequirements]
    }));
    setCurrentStep(2);
  };

  const handleSubmit = () => {
    const permitData = {
      ...formData,
      siteId,
      taskTemplate: formData.selectedTaskTemplate,
      createdAt: new Date(),
      status: 'draft'
    };
    onSubmit(permitData);
    onClose();
  };

  const steps = [
    { id: 1, title: 'Select Work Activity', description: 'Choose the type of work requiring a permit' },
    { id: 2, title: 'Permit Details', description: 'Provide specific details for the permit' },
    { id: 3, title: 'Risk Assessment', description: 'Identify hazards and control measures' },
    { id: 4, title: 'Review & Submit', description: 'Review all information before submission' }
  ];

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Create New Permit</h2>
            <p className="text-sm text-gray-500 mt-1">
              Step {currentStep} of {steps.length}: {steps[currentStep - 1]?.description}
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="h-6 w-6" />
          </button>
        </div>

        {/* Progress Steps */}
        <div className="px-6 py-4 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            {steps.map((step, index) => (
              <div key={step.id} className="flex items-center">
                <div className={`flex items-center justify-center w-8 h-8 rounded-full text-sm font-medium ${
                  currentStep > step.id
                    ? 'bg-green-600 text-white'
                    : currentStep === step.id
                    ? 'bg-blue-600 text-white'
                    : 'bg-gray-200 text-gray-600'
                }`}>
                  {currentStep > step.id ? <CheckCircle className="h-4 w-4" /> : step.id}
                </div>
                <span className={`ml-2 text-sm font-medium ${
                  currentStep >= step.id ? 'text-gray-900' : 'text-gray-500'
                }`}>
                  {step.title}
                </span>
                {index < steps.length - 1 && (
                  <ChevronRight className="h-4 w-4 text-gray-400 ml-4" />
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[60vh]">
          {/* Step 1: Task Template Selection */}
          {currentStep === 1 && (
            <div className="space-y-6">
              {/* Search and Filter */}
              <div className="flex space-x-4">
                <div className="flex-1 relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search work activities..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-4 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="all">All Categories</option>
                  {TASK_TEMPLATE_CATEGORIES.map(category => (
                    <option key={category.id} value={category.id}>{category.name}</option>
                  ))}
                </select>
              </div>

              {/* Task Templates Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {filteredTemplates.map((template) => (
                  <div
                    key={template.id}
                    onClick={() => handleTaskTemplateSelect(template)}
                    className="border border-gray-200 rounded-lg p-4 hover:border-blue-500 hover:bg-blue-50 cursor-pointer transition-colors"
                  >
                    <div className="flex items-start justify-between mb-3">
                      <h3 className="font-medium text-gray-900">{template.name}</h3>
                      <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                        template.riskLevel === 'critical' ? 'bg-red-100 text-red-800' :
                        template.riskLevel === 'high' ? 'bg-orange-100 text-orange-800' :
                        template.riskLevel === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-green-100 text-green-800'
                      }`}>
                        {template.riskLevel.toUpperCase()}
                      </span>
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-3">{template.description}</p>
                    
                    <div className="space-y-2">
                      <div className="flex items-center text-xs text-gray-500">
                        <Clock className="h-3 w-3 mr-1" />
                        <span>{template.estimatedDuration} hours</span>
                        <Users className="h-3 w-3 ml-3 mr-1" />
                        <span>{template.minimumWorkers}-{template.maximumWorkers} workers</span>
                      </div>
                      
                      {template.requiredPermitTypes.length > 0 && (
                        <div className="flex items-center text-xs text-amber-600">
                          <Shield className="h-3 w-3 mr-1" />
                          <span>{template.requiredPermitTypes.length} permit(s) required</span>
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Step 2: Permit Details */}
          {currentStep === 2 && formData.selectedTaskTemplate && (
            <div className="space-y-6">
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h3 className="font-medium text-blue-900 mb-2">Selected Work Activity</h3>
                <p className="text-blue-800">{formData.selectedTaskTemplate.name}</p>
                <p className="text-sm text-blue-600 mt-1">{formData.selectedTaskTemplate.description}</p>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Permit Title *
                  </label>
                  <input
                    type="text"
                    value={formData.title}
                    onChange={(e) => setFormData(prev => ({ ...prev, title: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Priority *
                  </label>
                  <select
                    value={formData.priority}
                    onChange={(e) => setFormData(prev => ({ ...prev, priority: e.target.value as any }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="low">Low</option>
                    <option value="medium">Medium</option>
                    <option value="high">High</option>
                    <option value="critical">Critical</option>
                  </select>
                </div>

                <div className="md:col-span-2">
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Work Location *
                  </label>
                  <input
                    type="text"
                    value={formData.location}
                    onChange={(e) => setFormData(prev => ({ ...prev, location: e.target.value }))}
                    placeholder="Specify exact location of work"
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Planned Start Date *
                  </label>
                  <input
                    type="date"
                    value={formData.plannedStartDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, plannedStartDate: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Planned End Date *
                  </label>
                  <input
                    type="date"
                    value={formData.plannedEndDate}
                    onChange={(e) => setFormData(prev => ({ ...prev, plannedEndDate: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
              </div>

              {/* Worker Assignment */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Assign Workers
                </label>
                <div className="border border-gray-300 rounded-md p-4 max-h-48 overflow-y-auto">
                  {mockWorkers.map((worker) => (
                    <div key={worker.id} className="flex items-center space-x-3 py-2">
                      <input
                        type="checkbox"
                        id={`worker-${worker.id}`}
                        checked={formData.assignedWorkers.includes(worker.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setFormData(prev => ({
                              ...prev,
                              assignedWorkers: [...prev.assignedWorkers, worker.id]
                            }));
                          } else {
                            setFormData(prev => ({
                              ...prev,
                              assignedWorkers: prev.assignedWorkers.filter(id => id !== worker.id)
                            }));
                          }
                        }}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <label htmlFor={`worker-${worker.id}`} className="text-sm text-gray-900 cursor-pointer">
                        {worker.name} - {worker.trade}
                      </label>
                    </div>
                  ))}
                </div>
              </div>

              {/* Required Permit Types */}
              {formData.selectedTaskTemplate.requiredPermitTypes.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Required Permit Types
                  </label>
                  <div className="space-y-2">
                    {formData.selectedTaskTemplate.requiredPermitTypes.map((permitType: string) => (
                      <div key={permitType} className="flex items-center p-3 bg-amber-50 border border-amber-200 rounded-md">
                        <Shield className="h-4 w-4 text-amber-600 mr-2" />
                        <span className="text-amber-800 font-medium">{permitType}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Step 3: Risk Assessment */}
          {currentStep === 3 && formData.selectedTaskTemplate && (
            <div className="space-y-6">
              <div className="bg-orange-50 border border-orange-200 rounded-lg p-4">
                <div className="flex items-center mb-2">
                  <AlertTriangle className="h-5 w-5 text-orange-600 mr-2" />
                  <h3 className="font-medium text-orange-900">Risk Assessment Required</h3>
                </div>
                <p className="text-orange-800 text-sm">
                  Risk Level: <span className="font-medium">{formData.selectedTaskTemplate.riskLevel.toUpperCase()}</span>
                </p>
              </div>

              {/* Pre-identified Safety Requirements */}
              {formData.selectedTaskTemplate.safetyRequirements.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Standard Safety Requirements
                  </label>
                  <div className="space-y-2">
                    {formData.selectedTaskTemplate.safetyRequirements.map((requirement, index) => (
                      <div key={index} className="flex items-center p-3 bg-green-50 border border-green-200 rounded-md">
                        <CheckCircle className="h-4 w-4 text-green-600 mr-2" />
                        <span className="text-green-800">{requirement}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Additional Hazards */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Additional Hazards Identified
                </label>
                <textarea
                  value={formData.identifiedHazards.join('\n')}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    identifiedHazards: e.target.value.split('\n').filter(h => h.trim())
                  }))}
                  placeholder="List any additional hazards specific to this work location or conditions..."
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Control Measures */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Additional Control Measures
                </label>
                <textarea
                  value={formData.controlMeasures.join('\n')}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    controlMeasures: e.target.value.split('\n').filter(m => m.trim())
                  }))}
                  placeholder="Describe additional control measures to mitigate identified hazards..."
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Required PPE */}
              {formData.selectedTaskTemplate.requiredPPE.length > 0 && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Required Personal Protective Equipment
                  </label>
                  <div className="grid grid-cols-2 md:grid-cols-3 gap-2">
                    {formData.selectedTaskTemplate.requiredPPE.map((ppe, index) => (
                      <div key={index} className="flex items-center p-2 bg-blue-50 border border-blue-200 rounded-md">
                        <Shield className="h-3 w-3 text-blue-600 mr-2" />
                        <span className="text-blue-800 text-sm">{ppe}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}
            </div>
          )}

          {/* Step 4: Review & Submit */}
          {currentStep === 4 && formData.selectedTaskTemplate && (
            <div className="space-y-6">
              <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                <h3 className="font-medium text-gray-900 mb-4">Permit Summary</h3>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="font-medium text-gray-700">Work Activity:</span>
                    <p className="text-gray-900">{formData.selectedTaskTemplate.name}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Priority:</span>
                    <p className="text-gray-900 capitalize">{formData.priority}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Location:</span>
                    <p className="text-gray-900">{formData.location}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Duration:</span>
                    <p className="text-gray-900">{formData.plannedStartDate} to {formData.plannedEndDate}</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Workers Assigned:</span>
                    <p className="text-gray-900">{formData.assignedWorkers.length} workers</p>
                  </div>
                  <div>
                    <span className="font-medium text-gray-700">Risk Level:</span>
                    <p className="text-gray-900 capitalize">{formData.selectedTaskTemplate.riskLevel}</p>
                  </div>
                </div>
              </div>

              <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                <h4 className="font-medium text-yellow-900 mb-2">Important Notes</h4>
                <ul className="text-sm text-yellow-800 space-y-1">
                  <li>• This permit will be submitted for approval</li>
                  <li>• Work cannot commence until permit is approved</li>
                  <li>• All assigned workers must acknowledge the permit</li>
                  <li>• Daily safety checks may be required for high-risk activities</li>
                </ul>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <button
            onClick={() => currentStep > 1 ? setCurrentStep(currentStep - 1) : onClose()}
            className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
          >
            {currentStep > 1 ? 'Previous' : 'Cancel'}
          </button>
          
          <div className="flex space-x-3">
            {currentStep < steps.length ? (
              <button
                onClick={() => setCurrentStep(currentStep + 1)}
                disabled={currentStep === 1 && !formData.selectedTaskTemplate}
                className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                Next
              </button>
            ) : (
              <button
                onClick={handleSubmit}
                className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700"
              >
                Create Permit
              </button>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default PermitCreationModal;
