import { useState, useEffect } from "react";
import { useLocation } from "react-router-dom";
import {
	LayoutDashboard,
	GraduationCap,
	HardHat,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON>lipboard<PERSON>ist,
	<PERSON><PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>,
} from "lucide-react";
import FloatingCard from "../components/layout/FloatingCard";
import TabContainer, { Tab } from "../components/data/shared/TabContainer";
import DataDashboard from "../components/data/DataDashboard";
import TrainingPrograms from "../components/data/TrainingPrograms";
import PPECatalog from "../components/data/PPECatalog";
import FormTemplates from "../components/data/FormTemplates";
import PermitTypes from "../components/data/PermitTypes";
import IncidentTypes from "../components/data/IncidentTypes";
import TradesSkills from "../components/data/TradesSkills";

const DataPage = () => {
	const location = useLocation();
	const [activeTab, setActiveTab] = useState("overview");

	const validTabs = [
		"overview",
		"training-programs",
		"ppe-catalog",
		"form-templates",
		"permit-types",
		"incident-types",
		"trades-skills",
	];

	// Handle URL hash navigation - this effect runs whenever the location changes
	useEffect(() => {
		const hash = location.hash.replace("#", "");
		if (hash && validTabs.includes(hash)) {
			setActiveTab(hash);
		} else if (!hash) {
			setActiveTab("overview");
		}
	}, [location.hash]);

	// Also listen for direct hash changes (for browser back/forward)
	useEffect(() => {
		const handleHashChange = () => {
			const newHash = window.location.hash.replace("#", "");
			if (newHash && validTabs.includes(newHash)) {
				setActiveTab(newHash);
			} else if (!newHash) {
				setActiveTab("overview");
			}
		};

		window.addEventListener("hashchange", handleHashChange);
		return () => window.removeEventListener("hashchange", handleHashChange);
	}, []);

	const handleNavigateToTab = (tabId: string) => {
		setActiveTab(tabId);
		// Update URL hash without triggering a page reload
		window.history.replaceState(null, "", `#${tabId}`);
	};

	const tabs: Tab[] = [
		{
			id: "overview",
			label: "Overview",
			icon: <LayoutDashboard className="h-4 w-4" />,
			content: <DataDashboard onNavigateToTab={handleNavigateToTab} />,
		},
		{
			id: "training-programs",
			label: "Training Programs",
			icon: <GraduationCap className="h-4 w-4" />,
			content: <TrainingPrograms />,
		},
		{
			id: "ppe-catalog",
			label: "PPE Catalog",
			icon: <HardHat className="h-4 w-4" />,
			content: <PPECatalog />,
		},
		{
			id: "form-templates",
			label: "Form Templates",
			icon: <ClipboardList className="h-4 w-4" />,
			content: <FormTemplates />,
		},
		{
			id: "permit-types",
			label: "Permit Types",
			icon: <FileCheck className="h-4 w-4" />,
			content: <PermitTypes />,
		},
		{
			id: "incident-types",
			label: "Incident Types",
			icon: <AlertTriangle className="h-4 w-4" />,
			content: <IncidentTypes />,
		},
		{
			id: "trades-skills",
			label: "Trades & Skills",
			icon: <Wrench className="h-4 w-4" />,
			content: <TradesSkills />,
		},
	];

	return (
		<FloatingCard title="Data">
			<TabContainer
				tabs={tabs}
				activeTab={activeTab}
				onTabChange={handleNavigateToTab}
			/>
		</FloatingCard>
	);
};

export default DataPage;
