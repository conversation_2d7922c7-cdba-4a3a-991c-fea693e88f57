import { useParams } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { AlertTriangle, Calendar, ClipboardCheck, Clock, FileSpreadsheet, HardHat, ShieldCheck, Users, Timer, Wrench, Truck } from 'lucide-react';
import FloatingCard from '../components/layout/FloatingCard';
import KPICard from '../components/dashboard/KPICard';
import { SiteInfo,  } from '../types';
import { <PERSON><PERSON><PERSON>, Pie, Cell, ResponsiveContainer, Legend, Tooltip, BarChart, Bar, XAxis, YAxis, CartesianGrid } from 'recharts';

// Mock data
const mockSite: SiteInfo = {
  id: "site1",
  name: "Westlands Construction Site",
  healthStatus: "green",
  workersOnSite: 42,
  activePermits: 8,
  openIncidents: 0,
  projectManager: "<PERSON>",
  location: "Waiyaki Way, Westlands, Nairobi",
  timeline: "Jan 2025 - Dec 2026",
  currentPhase: "Foundation",
  progressPercentage: 25,
  tenantId: '',
  status: 'active',
  createdAt: new Date()
};

// Mock data for trades pie chart - ensuring it matches the site.workersOnSite
const mockTradesData = [
  { name: 'Electricians', value: 7, color: '#3B82F6' },
  { name: 'Plumbers', value: 5, color: '#10B981' },
  { name: 'Carpenters', value: 10, color: '#F59E0B' },
  { name: 'Masons', value: 8, color: '#EF4444' },
  { name: 'Painters', value: 4, color: '#8B5CF6' },
  { name: 'Welders', value: 3, color: '#06B6D4' },
  { name: 'General Labor', value: 5, color: '#84CC16' },
];

// This should equal mockSite.workersOnSite (42)
const totalTradesCount = mockTradesData.reduce((sum, trade) => sum + trade.value, 0);
const mockIncidents = [
  { id: 'inc1', date: '2025-05-15', type: 'Near Miss', severity: 'low' as const, location: 'Foundation Area', status: 'resolved' as const },
  { id: 'inc2', date: '2025-05-21', type: 'First Aid', severity: 'medium' as const, location: 'Materials Storage', status: 'closed' as const },
  { id: 'inc3', date: '2025-05-10', type: 'Equipment faliure', severity: 'high' as const, location: 'Equipment Yard', status: 'closed' as const },
];

// Mock data for visualization tabs
// const mockTimeTrackingData = [
//   { worker: 'John Doe', trade: 'Electrician', hoursToday: 8, hoursWeek: 40, status: 'On-site', clockIn: '07:30', clockOut: null },
//   { worker: 'Jane Smith', trade: 'Plumber', hoursToday: 7.5, hoursWeek: 37.5, status: 'On-site', clockIn: '08:00', clockOut: null },
//   { worker: 'Mike Johnson', trade: 'Carpenter', hoursToday: 8, hoursWeek: 42, status: 'On-site', clockIn: '07:45', clockOut: null },
//   { worker: 'Sarah Wilson', trade: 'Mason', hoursToday: 6, hoursWeek: 35, status: 'Break', clockIn: '08:15', clockOut: null },
//   { worker: 'David Brown', trade: 'Welder', hoursToday: 7, hoursWeek: 38, status: 'On-site', clockIn: '07:30', clockOut: null },
// ];

const mockPermitsData = [
  { id: 'P001', type: 'Hot Work', status: 'Active', validUntil: '2025-01-15 18:00', location: 'Section A', assignedWorkers: 3 },
  { id: 'P002', type: 'Confined Space', status: 'Pending', validUntil: '2025-01-16 17:00', location: 'Tank Area', assignedWorkers: 2 },
  { id: 'P003', type: 'Working at Height', status: 'Active', validUntil: '2025-01-15 16:30', location: 'Building B', assignedWorkers: 5 },
  { id: 'P004', type: 'Electrical Work', status: 'Expired', validUntil: '2025-01-14 17:00', location: 'Main Panel', assignedWorkers: 1 },
];

const mockEquipmentData = [
  { id: 'E001', name: 'Tower Crane TC-1', type: 'Heavy Equipment', status: 'Operational', lastInspection: '2025-01-10', nextMaintenance: '2025-01-20' },
  { id: 'E002', name: 'Excavator CAT-320', type: 'Heavy Equipment', status: 'Maintenance Required', lastInspection: '2025-01-08', nextMaintenance: '2025-01-15' },
  { id: 'E003', name: 'Concrete Mixer CM-5', type: 'Construction Equipment', status: 'Operational', lastInspection: '2025-01-12', nextMaintenance: '2025-01-25' },
  { id: 'E004', name: 'Safety Harnesses (10x)', type: 'PPE', status: 'Available', lastInspection: '2025-01-11', nextMaintenance: '2025-02-11' },
  { id: 'E005', name: 'Hard Hats (25x)', type: 'PPE', status: 'Low Stock', lastInspection: '2025-01-09', nextMaintenance: 'N/A' },
];

// Data for visualization charts
const mockTimeChartData = [
  { day: 'Monday', workers: 38, dayShort: 'Mon' },
  { day: 'Tuesday', workers: 42, dayShort: 'Tue' },
  { day: 'Wednesday', workers: 40, dayShort: 'Wed' },
  { day: 'Thursday', workers: 41, dayShort: 'Thu' },
  { day: 'Friday', workers: 39, dayShort: 'Fri' },
  { day: 'Saturday', workers: 25, dayShort: 'Sat' }, // Weekend reduced staff
  { day: 'Sunday', workers: 12, dayShort: 'Sun' }, // Minimal weekend staff
];

const mockPermitChartData = mockPermitsData.reduce((acc, permit) => {
  const existing = acc.find(item => item.name === permit.type);
  if (existing) {
    existing.value += 1;
  } else {
    acc.push({ name: permit.type, value: 1, color: getPermitColor(permit.type) });
  }
  return acc;
}, [] as { name: string; value: number; color: string }[]);

const mockEquipmentChartData = mockEquipmentData.reduce((acc, equipment) => {
  const existing = acc.find(item => item.name === equipment.status);
  if (existing) {
    existing.value += 1;
  } else {
    acc.push({ name: equipment.status, value: 1, color: getEquipmentColor(equipment.status) });
  }
  return acc;
}, [] as { name: string; value: number; color: string }[]);

function getPermitColor(type: string): string {
  const colors: Record<string, string> = {
    'Hot Work': '#EF4444',
    'Confined Space': '#F59E0B',
    'Working at Height': '#3B82F6',
    'Electrical Work': '#8B5CF6',
  };
  return colors[type] || '#6B7280';
}

function getEquipmentColor(status: string): string {
  const colors: Record<string, string> = {
    'Operational': '#10B981',
    'Available': '#3B82F6',
    'Maintenance Required': '#EF4444',
    'Low Stock': '#F59E0B',
  };
  return colors[status] || '#6B7280';
}

const mockAlerts = [
  { id: 'alt1', type: 'certification', message: '3 worker certifications expiring in the next 14 days', timestamp: '2 hours ago' },
  { id: 'alt2', type: 'equipment', message: 'Crane inspection due in 3 days', timestamp: '5 hours ago' },
  { id: 'alt3', type: 'permit', message: 'Hot work permit for Section B expires today', timestamp: '1 day ago' },
];

const mockNotifications = [
  { id: 'not1', type: 'info', message: 'Daily toolbox talk completed for all teams', timestamp: '1 hour ago' },
  { id: 'not2', type: 'success', message: 'Weekly safety inspection passed with no issues', timestamp: '3 hours ago' },
  { id: 'not3', type: 'info', message: 'New worker John Smith added to site roster', timestamp: '6 hours ago' },
  { id: 'not4', type: 'success', message: 'Equipment maintenance completed for Excavator CAT-320', timestamp: '1 day ago' },
];

const mockHoursData = [
  { name: 'Safe Hours', value: 1250, color: '#10B981' }, // Green
  { name: 'Incident Hours', value: 45, color: '#EF4444' }, // Red
  { name: 'Training Hours', value: 180, color: '#3B82F6' }, // Blue
  { name: 'Maintenance Hours', value: 95, color: '#F59E0B' }, // Amber
];

const totalHoursWorked = mockHoursData.reduce((sum, item) => sum + item.value, 0);

const SiteDashboard = () => {
  const { siteId } = useParams<{ siteId: string }>();
  const [site, _setSite] = useState<SiteInfo>(mockSite);
  const [activeVisualizationTab, setActiveVisualizationTab] = useState<'trades' | 'time' | 'permits' | 'equipment' | 'hours'>('trades');
  const [activeNotificationTab, setActiveNotificationTab] = useState<'alerts' | 'notifications'>('alerts');

  // In a real app, we would fetch the site data based on siteId
  useEffect(() => {
    // Simulating API fetch
    // setSite(fetchedSite);
  }, [siteId]);

	const breadcrumbs = [
		{ name: "Dashboard", path: "/" },
		{ name: site.name, path: `/sites/${siteId}/dashboard` },
	];

  return (
    <FloatingCard title={`${site.name} Dashboard`} breadcrumbs={breadcrumbs}>
      {/* KPI Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5 gap-4 mb-6">
        {/* Row 1 - Core Metrics */}
        <KPICard
          title="Workers On-Site (Today)"
          value={site.workersOnSite}
          icon={<Users className="h-6 w-6 text-blue-500" />}
          navigateTo={`/sites/${siteId}/workers`}
        />
        <KPICard
          title="Active Permits"
          value={site.activePermits}
          icon={<ClipboardCheck className="h-6 w-6 text-green-500" />}
          navigateTo={`/sites/${siteId}/permits`}
        />
        <KPICard
          title="Open Incidents"
          value={site.openIncidents}
          icon={<AlertTriangle className="h-6 w-6 text-red-500" />}
          navigateTo={`/sites/${siteId}/incidents`}
        />
        <KPICard
          title="Equipment On-Site"
          value={mockEquipmentData.filter(e => e.status === 'Operational' || e.status === 'Available').length}
          icon={<Truck className="h-6 w-6 text-cyan-500" />}
          navigateTo={`/sites/${siteId}/equipment`}
        />
        <KPICard
          title="Equipment Needing Maintenance"
          value={mockEquipmentData.filter(e => e.status === 'Maintenance Required').length}
          icon={<Wrench className="h-6 w-6 text-orange-500" />}
          navigateTo={`/sites/${siteId}/equipment?filter=maintenance`}
        />
      </div>

      {/* Secondary KPI Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
        <KPICard
          title="Overdue Trainings"
          value={3}
          icon={<HardHat className="h-6 w-6 text-orange-500" />}
          navigateTo={`/sites/${siteId}/training?filter=overdue`}
        />
        <KPICard
          title="Upcoming Inspections"
          value={2}
          icon={<FileSpreadsheet className="h-6 w-6 text-purple-500" />}
          navigateTo={`/sites/${siteId}/inspections?filter=upcoming`}
        />
        <KPICard
          title="Toolbox Talk Attendance %"
          value="92%"
          change={2}
          icon={<Clock className="h-6 w-6 text-indigo-500" />}
          navigateTo={`/sites/${siteId}/toolbox`}
        />
        <KPICard
          title="Safe Man Hours"
          value="336"
          icon={<Timer className="h-6 w-6 text-teal-500" />}
          navigateTo={`/sites/${siteId}/time#reports`}
        />
      </div>

      {/* Charts and Data Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
        {/* Interactive Visualization Card */}
        <div className="bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
          {/* Tab Navigation */}
          <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mb-4">
            <button
              onClick={() => setActiveVisualizationTab('trades')}
              className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                activeVisualizationTab === 'trades'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Trades
            </button>
            <button
              onClick={() => setActiveVisualizationTab('time')}
              className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                activeVisualizationTab === 'time'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Time
            </button>
            <button
              onClick={() => setActiveVisualizationTab('permits')}
              className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                activeVisualizationTab === 'permits'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Permits
            </button>
            <button
              onClick={() => setActiveVisualizationTab('equipment')}
              className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                activeVisualizationTab === 'equipment'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Equipment
            </button>
            <button
              onClick={() => setActiveVisualizationTab('hours')}
              className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                activeVisualizationTab === 'hours'
                  ? 'bg-white text-gray-900 shadow-sm'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Hours
            </button>
          </div>

          {/* Dynamic Content Based on Active Tab */}
          {activeVisualizationTab === 'trades' && (
            <>
              <div className="text-center mb-4">
                <p className="text-lg font-normal text-gray-600">{totalTradesCount} Workers on Site</p>
              </div>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={mockTradesData}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      paddingAngle={2}
                      dataKey="value"
                    >
                      {mockTradesData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value, name) => [value, name]} />
                    <Legend
                      verticalAlign="middle"
                      align="right"
                      layout="vertical"
                      iconType="circle"
                      wrapperStyle={{ paddingLeft: '20px', fontSize: '12px' }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </>
          )}

          {activeVisualizationTab === 'time' && (
            <>
              <div className="text-center mb-4">
                <p className="text-lg font-normal text-gray-600">Weekly Attendance Pattern</p>
              </div>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart data={mockTimeChartData} margin={{ top: 20, right: 30, left: 20, bottom: 5 }}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis
                      dataKey="dayShort"
                      tick={{ fontSize: 12 }}
                      axisLine={{ stroke: '#E5E7EB' }}
                      tickLine={{ stroke: '#E5E7EB' }}
                    />
                    <YAxis
                      tick={{ fontSize: 12 }}
                      axisLine={{ stroke: '#E5E7EB' }}
                      tickLine={{ stroke: '#E5E7EB' }}
                      label={{ value: 'Workers', angle: -90, position: 'insideLeft' }}
                    />
                    <Tooltip
                      formatter={(value) => [value, 'Workers']}
                      labelFormatter={(label) => {
                        const dayData = mockTimeChartData.find(d => d.dayShort === label);
                        return dayData ? dayData.day : label;
                      }}
                      contentStyle={{
                        backgroundColor: '#F9FAFB',
                        border: '1px solid #E5E7EB',
                        borderRadius: '6px'
                      }}
                    />
                    <Bar
                      dataKey="workers"
                      fill="#3B82F6"
                      radius={[4, 4, 0, 0]}
                      name="Workers"
                    />
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </>
          )}

          {activeVisualizationTab === 'permits' && (
            <>
              <div className="text-center mb-4">
                <p className="text-lg font-normal text-gray-600">{mockPermitsData.length} Active Permits</p>
              </div>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={mockPermitChartData}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      paddingAngle={2}
                      dataKey="value"
                    >
                      {mockPermitChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value, name) => [value, name]} />
                    <Legend
                      verticalAlign="middle"
                      align="right"
                      layout="vertical"
                      iconType="circle"
                      wrapperStyle={{ paddingLeft: '20px', fontSize: '12px' }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </>
          )}

          {activeVisualizationTab === 'equipment' && (
            <>
              <div className="text-center mb-4">
                <p className="text-lg font-normal text-gray-600">{mockEquipmentData.length} Equipment Items</p>
              </div>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={mockEquipmentChartData}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      paddingAngle={2}
                      dataKey="value"
                    >
                      {mockEquipmentChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value, name) => [value, name]} />
                    <Legend
                      verticalAlign="middle"
                      align="right"
                      layout="vertical"
                      iconType="circle"
                      wrapperStyle={{ paddingLeft: '20px', fontSize: '12px' }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </>
          )}

          {activeVisualizationTab === 'hours' && (
            <>
              <div className="text-center mb-4">
                <p className="text-lg font-normal text-gray-600">{totalHoursWorked} Total Hours Worked</p>
              </div>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={mockHoursData}
                      cx="50%"
                      cy="50%"
                      innerRadius={40}
                      outerRadius={80}
                      paddingAngle={2}
                      dataKey="value"
                    >
                      {mockHoursData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <Tooltip formatter={(value, name) => [`${value} hours`, name]} />
                    <Legend
                      verticalAlign="middle"
                      align="right"
                      layout="vertical"
                      iconType="circle"
                      wrapperStyle={{ paddingLeft: '20px', fontSize: '12px' }}
                    />
                  </PieChart>
                </ResponsiveContainer>
              </div>
            </>
          )}
        </div>

        {/* Alerts & Notifications */}
        <div className="bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
          <div className="mb-4">
            {/* Tab Navigation */}
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
              <button
                onClick={() => setActiveNotificationTab('alerts')}
                className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeNotificationTab === 'alerts'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Alerts
              </button>
              <button
                onClick={() => setActiveNotificationTab('notifications')}
                className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                  activeNotificationTab === 'notifications'
                    ? 'bg-white text-gray-900 shadow-sm'
                    : 'text-gray-500 hover:text-gray-700'
                }`}
              >
                Notifications
              </button>
            </div>
          </div>

          {/* Tab Content */}
          <div className="h-64 overflow-y-auto">
            {activeNotificationTab === 'alerts' && (
              <div className="space-y-4">
                {mockAlerts.map((alert) => (
                  <div key={alert.id} className="flex items-start border-b border-gray-100 pb-3">
                    <div className={`flex-shrink-0 rounded-full p-2 mr-3 ${
                      alert.type === 'certification' ? 'bg-blue-100 text-blue-500' :
                      alert.type === 'equipment' ? 'bg-yellow-100 text-yellow-500' :
                      'bg-red-100 text-red-500'
                    }`}>
                      {alert.type === 'certification' ? <HardHat className="h-5 w-5" /> :
                       alert.type === 'equipment' ? <ShieldCheck className="h-5 w-5" /> :
                       <ClipboardCheck className="h-5 w-5" />}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-800">{alert.message}</p>
                      <p className="text-xs text-gray-500 mt-1">{alert.timestamp}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {activeNotificationTab === 'notifications' && (
              <div className="space-y-4">
                {mockNotifications.map((notification) => (
                  <div key={notification.id} className="flex items-start border-b border-gray-100 pb-3">
                    <div className={`flex-shrink-0 rounded-full p-2 mr-3 ${
                      notification.type === 'success' ? 'bg-green-100 text-green-500' :
                      'bg-blue-100 text-blue-500'
                    }`}>
                      {notification.type === 'success' ? <ShieldCheck className="h-5 w-5" /> :
                       <ClipboardCheck className="h-5 w-5" />}
                    </div>
                    <div className="flex-1">
                      <p className="text-sm text-gray-800">{notification.message}</p>
                      <p className="text-xs text-gray-500 mt-1">{notification.timestamp}</p>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Quick Actions & Recent Incidents Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Quick Actions */}
        <div className="bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Quick Actions</h3>
          <div className="space-y-3">
            <button className="w-full flex items-center justify-between bg-green-50 hover:bg-green-100 text-green-700 font-medium py-2 px-4 rounded transition-colors">
              <span>Add Worker to Site</span>
              <Users className="h-4 w-4" />
            </button>
            <button className="w-full flex items-center justify-between bg-green-50 hover:bg-green-100 text-green-700 font-medium py-2 px-4 rounded transition-colors">
              <span>Create New Task</span>
              <ClipboardCheck className="h-4 w-4" />
            </button>
            <button className="w-full flex items-center justify-between bg-green-50 hover:bg-green-100 text-green-700 font-medium py-2 px-4 rounded transition-colors">
              <span>Report Incident</span>
              <AlertTriangle className="h-4 w-4" />
            </button>
            <button className="w-full flex items-center justify-between bg-green-50 hover:bg-green-100 text-green-700 font-medium py-2 px-4 rounded transition-colors">
              <span>Start Toolbox Talk</span>
              <Calendar className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Recent Incidents */}
        <div className="bg-white p-5 rounded-lg border border-gray-200 shadow-sm">
          <h3 className="text-lg font-semibold text-gray-800 mb-4">Recent Incidents</h3>
          {mockIncidents.length > 0 ? (
            <div className="space-y-3">
              {mockIncidents.map((incident) => (
                <div key={incident.id} className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center justify-between mb-2">
                    <span className="text-sm font-medium text-gray-900">{incident.type}</span>
                    <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                      incident.severity === 'low' ? 'bg-green-100 text-green-800' :
                      incident.severity === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                      incident.severity === 'high' ? 'bg-orange-100 text-orange-800' :
                      'bg-red-100 text-red-800'
                    }`}>
                      {incident.severity.charAt(0).toUpperCase() + incident.severity.slice(1)}
                    </span>
                  </div>
                  <p className="text-xs text-gray-500">{incident.date} • {incident.location}</p>
                  <p className="text-xs text-gray-600 mt-1">Status: {incident.status}</p>
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-4">
              <p className="text-gray-500">No incidents reported.</p>
            </div>
          )}
        </div>
      </div>
    </FloatingCard>
  );
};

export default SiteDashboard;
