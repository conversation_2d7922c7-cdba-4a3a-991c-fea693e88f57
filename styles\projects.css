/* Projects Portfolio CSS */

/* Base Styles */
:root {
  --primary-color: #84277F;
  --header-bg: #E8C7E5;
  --accent-color: #F9F0F8;
  --page-bg: #fcf7fb;
  --white: #ffffff;
  --gray: #f0f0f0;
  --dark-gray: #333333;
  --shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  --transition: all 0.3s ease;
}

html, body {
  font-family: 'Figtree', sans-serif;
  background-color: var(--page-bg);
  margin: 0;
  padding: 0;
  height: auto;
  min-height: 100%;
  overflow-y: visible;
}

/* Special styling for touchpad users */
body.using-touchpad {
  overflow-y: visible !important;
  height: auto !important;
}

/* When modal is open, prevent background scrolling */
body.modal-open {
  overflow: hidden;
}

.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0.5rem 1rem 2rem 1rem; /* Reduced top padding, kept other padding values */
}

/* Portfolio Section */
.portfolio-section {
  padding-top: 20px; /* Reduced from 40px to 20px to meet the 30px max requirement */
  padding-bottom: 2rem;
}

.portfolio-title {
  font-family: 'Figtree', sans-serif;
  font-size: 36px;
  color: var(--primary-color);
  text-align: center;
  margin-bottom: 1.5rem;
}

/* Filter Styles */
.filter-container {
  margin-top: 0; /* Ensure no top margin */
  margin-bottom: 1.5rem;
  width: 100%;
}

.filter-desktop {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  gap: 0.5rem;
  margin-bottom: 1rem; /* Reduced from 1.5rem to 1rem */
  width: 100%;
}

.filter-btn {
  background-color: var(--accent-color);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: 10px;
  padding: 0.5rem 1rem;
  font-family: 'Figtree', sans-serif;
  font-size: 14px;
  cursor: pointer;
  transition: var(--transition);
  flex: 1;
}

.filter-btn:hover {
  background-color: var(--header-bg);
}

.filter-btn.active {
  background-color: var(--primary-color);
  color: var(--white);
}

.filter-mobile {
  display: none;
  margin-bottom: 1rem; /* Reduced from 1.5rem to 1rem */
}

#filter-dropdown {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid var(--primary-color);
  border-radius: 10px;
  background-color: var(--white);
  font-family: 'Figtree', sans-serif;
  font-size: 16px;
  color: var(--primary-color);
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%2384277F' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1em;
}

/* Projects Grid */
.projects-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  min-height: 300px; /* Ensure there's enough content to enable scrolling */
  width: 100%;
  height: auto;
  overflow: visible;
}

.project-card {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--shadow);
  transition: var(--transition);
  height: 300px;
}

.project-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 20px rgba(0, 0, 0, 0.15);
}

.project-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: var(--transition);
}

.project-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.25); /* Changed to black with 25% transparency */
  color: var(--white);
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 1.5rem;
  opacity: 0;
  transition: var(--transition);
  text-align: center;
}

.project-card:hover .project-overlay {
  opacity: 1;
}

.project-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 0.5rem;
}

.project-info {
  margin-bottom: 1.5rem;
  font-size: 16px;
}

.view-details-btn {
  background-color: var(--white);
  color: var(--primary-color);
  border: none;
  border-radius: 30px;
  padding: 0.5rem 1.5rem;
  font-family: 'Figtree', sans-serif;
  font-weight: 500;
  cursor: pointer;
  transition: var(--transition);
}

.view-details-btn:hover {
  background-color: var(--accent-color);
  transform: scale(1.05);
}

/* Loading State styles moved to dots-loading.css */

/* Loading spinner styles moved to dots-loading.css */

/* Error Message */
.error-message {
  text-align: center;
  padding: 2rem;
  background-color: #fff0f0;
  border-radius: 8px;
  margin: 2rem 0;
  display: none;
}

.retry-btn {
  background-color: var(--primary-color);
  color: var(--white);
  border: none;
  border-radius: 5px;
  padding: 0.5rem 1.5rem;
  font-family: 'Figtree', sans-serif;
  cursor: pointer;
  transition: var(--transition);
}

.retry-btn:hover {
  background-color: #6b1f67;
}

/* Modal Styles */
.modal {
  display: none; /* Initially hidden */
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.7);
  z-index: 1000;
  overflow-y: auto; /* Enable scrolling for the modal itself */
}

.modal-content {
  background-color: var(--white);
  border-radius: 8px;
  width: 90%;
  max-width: 1200px;
  margin: 5vh auto; /* Center horizontally and add some space at top and bottom */
  display: flex;
  flex-direction: column;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  overflow: hidden; /* Hide overflow */
  max-height: 90vh; /* Maximum height */
  border: none; /* Explicitly remove any border */
  outline: none; /* Remove outline as well */
  position: relative;
}

.modal-inner {
  padding: 3rem 2rem 0;
  overflow-y: auto !important; /* Force overflow to be auto */
  flex-grow: 1;
  max-height: calc(90vh - 80px); /* Subtract footer height */
  -webkit-overflow-scrolling: touch !important; /* Force smooth scrolling on iOS */
  scroll-behavior: smooth; /* Smooth scrolling */
  outline: none; /* Remove outline when focused */
  position: relative; /* For absolute positioning of close button */
  height: auto;
}

.close-modal {
  position: absolute;
  top: 20px;
  right: 20px;
  font-size: 28px;
  font-weight: bold;
  color: var(--dark-gray);
  cursor: pointer;
  transition: transform 0.25s ease, opacity 0.25s ease;
  z-index: 1200;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  background: none;
  border: none;
  padding: 0;
  margin: 0;
  width: auto;
  height: auto;
  opacity: 0.7;
}

.close-modal:hover,
.close-modal:focus {
  color: #333333;
  transform: rotate(90deg);
  opacity: 1;
  outline: none;
}

.modal-grid {
  display: grid;
  grid-template-rows: auto auto auto;
  gap: 2rem;
  padding-top: 0.5rem; /* Reduced padding at the top since close button is now fixed */
  padding-bottom: 1rem; /* Add padding at the bottom for better spacing */
}

/* Modal Header */
.modal-header {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.modal-title h2 {
  font-family: 'Figtree', sans-serif;
  font-size: 36px;
  color: #000000; /* Changed to black */
  margin-top: 0;
  margin-bottom: 1rem;
}

/* Project Attributes Styling */
.modal-attributes {
  margin: 1.5rem 0;
  padding: 0.5rem 0; /* Reduced padding */
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.attribute-labels,
.attribute-values {
  display: flex;
  justify-content: space-between;
  width: 100%;
}

.attribute-labels {
  margin-bottom: 0.25rem;
}

.attribute-label {
  color: #777777; /* Lighter grey for labels */
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
  flex: 1;
  padding-right: 10px;
}

.attribute-value {
  color: var(--dark-gray); /* Darker color for values */
  font-size: 16px;
  font-weight: 600;
  flex: 1;
  padding-right: 10px;
}

.modal-description {
  font-size: 16px;
  line-height: 1.6;
}

/* Modal Details */
.modal-details {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
}

.modal-main-image img {
  width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: var(--shadow);
}

.modal-delivered h3 {
  font-family: 'Figtree', sans-serif;
  font-size: 24px;
  color: #000000; /* Changed to black */
  margin-top: 0;
}

.modal-delivered ul {
  padding-left: 0;
  list-style: none;
}

.modal-delivered li {
  margin-bottom: 0.5rem;
  line-height: 1.5;
  position: relative;
  padding-left: 1.8rem;
}

.modal-delivered li::before {
  content: '✓'; /* Checkmark symbol */
  position: absolute;
  left: 0;
  color: var(--primary-color); /* Keep the checkmark in the primary color */
  font-weight: bold;
  font-size: 1.1em;
}

/* Modal Gallery */
.modal-gallery {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
}

.gallery-image {
  width: 100%;
  height: 200px;
  object-fit: cover;
  border-radius: 8px;
  cursor: pointer;
  transition: var(--transition);
}

.gallery-image:hover {
  transform: scale(1.03);
  box-shadow: var(--shadow);
}

/* Modal Navigation */
.modal-navigation {
  display: flex;
  justify-content: space-between;
  padding: 1rem 2rem;
  background-color: #f8f8f8;
  border-top: 1px solid #eee;
  position: sticky;
  bottom: 0;
  width: 100%;
  z-index: 10;
  height: 80px; /* Fixed height for the footer */
}

.nav-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: var(--accent-color);
  color: var(--primary-color);
  border: 1px solid var(--primary-color);
  border-radius: 50px;
  padding: 0.75rem 1.25rem;
  font-family: 'Figtree', sans-serif;
  font-size: 14px;
  cursor: pointer;
  transition: var(--transition);
  min-width: 120px;
}

.nav-btn:hover {
  background-color: var(--primary-color);
  color: var(--white);
}

.nav-arrow {
  font-size: 24px;
  margin: 0 0.5rem;
  font-weight: bold;
}

.prev-btn .nav-arrow {
  margin-right: 0.5rem;
}

.next-btn .nav-arrow {
  margin-left: 0.5rem;
}

/* Responsive Styles */
@media (max-width: 1024px) {
  .projects-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .modal-gallery {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (max-width: 768px) {
  .filter-desktop {
    display: none;
  }

  .filter-mobile {
    display: block;
  }

  .modal-header,
  .modal-details {
    grid-template-columns: 1fr;
    gap: 1.25rem;
  }

  .modal-delivered h3 {
    font-size: 20px;
    margin-bottom: 0.5rem;
    color: #000000; /* Ensure black color is maintained on mobile */
  }

  .modal-delivered ul {
    padding-left: 0;
  }

  .modal-delivered li {
    margin-bottom: 0.25rem;
    padding-left: 1.5rem;
  }

  .modal-delivered li::before {
    font-size: 1em;
  }

  .modal-grid {
    gap: 1.25rem;
  }

  .modal-content {
    padding: 0;
    margin: 3vh auto; /* Smaller margin on mobile */
    width: 95%; /* Wider on mobile */
  }

  .modal-inner {
    padding: 2.5rem 1rem 0;
    max-height: calc(90vh - 60px); /* Adjust for smaller footer height */
  }

  .modal-title h2 {
    font-size: 28px;
    padding-right: 40px; /* Add padding to prevent overlap with close button */
    color: #000000; /* Ensure black color is maintained on mobile */
    margin-bottom: 0.5rem; /* Reduced margin for mobile */
  }

  /* Adjust close button position for mobile */
  .close-modal {
    top: 15px;
    right: 15px;
    font-size: 24px;
    opacity: 0.7;
  }

  /* Responsive attributes for mobile */
  .modal-attributes {
    gap: 0.5rem;
    margin: 0.75rem 0;
  }

  .attribute-label,
  .attribute-value {
    font-size: 13px;
    padding-right: 5px;
  }

  .nav-text {
    display: none;
  }

  .nav-btn {
    padding: 0.5rem;
    min-width: 80px;
  }

  .nav-arrow {
    margin: 0;
  }

  .modal-navigation {
    padding: 0.75rem 1rem;
    height: 60px; /* Reduced height for mobile */
  }
}

@media (max-width: 576px) {
  .projects-grid {
    grid-template-columns: 1fr;
  }

  .modal-gallery {
    grid-template-columns: 1fr;
  }

  .project-card {
    height: 250px;
  }

  .portfolio-title {
    font-size: 28px;
  }

  /* Further adjust close button for very small screens */
  .close-modal {
    top: 10px;
    right: 10px;
    font-size: 22px;
    opacity: 0.7;
  }

  .modal-inner {
    padding: 2rem 0.75rem 0;
    max-height: calc(90vh - 50px); /* Adjust for even smaller footer height */
  }

  .modal-navigation {
    padding: 0.5rem 0.75rem;
    height: 50px; /* Further reduced height for very small screens */
  }

  .modal-grid {
    gap: 1rem;
  }

  .modal-header,
  .modal-details {
    gap: 1rem;
  }

  .modal-title h2 {
    font-size: 22px;
    padding-right: 30px; /* Smaller padding on very small screens */
    color: #000000; /* Ensure black color is maintained on very small screens */
    margin-bottom: 0.25rem; /* Further reduced margin for very small screens */
  }

  /* Further adjust attributes for very small screens */
  .modal-attributes {
    gap: 0.25rem;
    margin: 0.5rem 0;
  }

  .attribute-label,
  .attribute-value {
    font-size: 12px;
    padding-right: 3px;
  }
}
