using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;
using HotChocolate;

namespace GraphQLApi.Services;

public class SkillService : ISkillService
{
    private readonly IDbContextFactory<AppDbContext> _contextFactory;
    private readonly ILogger<SkillService> _logger;

    public SkillService(
        IDbContextFactory<AppDbContext> contextFactory,
        ILogger<SkillService> logger)
    {
        _contextFactory = contextFactory;
        _logger = logger;
    }

    public async Task<IEnumerable<Skill>> GetAllSkillsAsync()
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        return await context.Skills
            .Include(s => s.Workers)
            .ToListAsync();
    }

    public async Task<Skill?> GetSkillByIdAsync(int id)
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        return await context.Skills
            .Include(s => s.Workers)
            .FirstOrDefaultAsync(s => s.Id == id);
    }

    public async Task<Skill> CreateSkillAsync(Skill skill)
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        
        // Check if skill with same name already exists (case-insensitive, culture-invariant)
        var existingSkill = await context.Skills
            .FirstOrDefaultAsync(s => EF.Functions.Like(s.Name, skill.Name));
        
        if (existingSkill != null)
        {
            throw new GraphQLException(new Error(
                "Validation",
                $"A skill with name '{skill.Name}' already exists.")
            );
        }

        context.Skills.Add(skill);
        await context.SaveChangesAsync();
        return skill;
    }

    public async Task<Skill?> UpdateSkillAsync(int id, Skill skill, string updatedBy)
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        var existingSkill = await context.Skills
            .Include(s => s.Workers)
            .FirstOrDefaultAsync(s => s.Id == id);

        if (existingSkill == null)
        {
            return null;
        }

        // Check if another skill with the same name exists (case-insensitive, culture-invariant)
        var duplicateSkill = await context.Skills
            .FirstOrDefaultAsync(s => s.Id != id && EF.Functions.Like(s.Name, skill.Name));

        if (duplicateSkill != null)
        {
            throw new GraphQLException(new Error(
                "Validation",
                $"A skill with name '{skill.Name}' already exists.")
            );
        }

        // Update properties
        existingSkill.Name = skill.Name;
        existingSkill.Description = skill.Description;

        // Set audit fields
        existingSkill.UpdatedAt = DateTime.UtcNow;
        existingSkill.UpdatedBy = updatedBy;

        await context.SaveChangesAsync();
        return existingSkill;
    }

    public async Task<bool> DeleteSkillAsync(int id)
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        var skill = await context.Skills.FindAsync(id);
        
        if (skill == null)
        {
            return false;
        }

        try
        {
            context.Skills.Remove(skill);
            await context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting skill with ID {SkillId}", id);
            return false;
        }
    }
}
