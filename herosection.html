<!DOCTYPE html>
<html style="font-size: 16px;" lang="en">
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta charset="utf-8">
        <meta name="keywords" content="">
        <meta name="description" content="">
        <title>home</title>
        <link rel="stylesheet" href="https://capp.nicepage.com/b5dd6bfc4fba04fb3005a1a716dd88658ae7d769/nicepage.css" media="screen">
        <link rel="stylesheet" href="https://laxmigroup.nicepage.io/nicepage-site.css" media="screen">
        <link rel="stylesheet" href="https://laxmigroup.nicepage.io/index.css" media="screen">
        <script class="u-script" type="text/javascript" src="https://capp.nicepage.com/assets/jquery-3.5.1.min.js" defer=""></script>
        <script class="u-script" type="text/javascript" src="https://capp.nicepage.com/b5dd6bfc4fba04fb3005a1a716dd88658ae7d769/nicepage.js" defer=""></script>
        <meta name="generator" content="atomio.tech">
        <meta name="referrer" content="origin">
        <link rel="icon" href="/favicon.png">
        <link id="u-theme-google-font" rel="stylesheet" href="https://fonts.googleapis.com/css?family=Open+Sans:300,300i,400,400i,500,500i,600,600i,700,700i,800,800i|Figtree:300,300i,400,400i,500,500i,600,600i,700,700i,800,800i,900,900i">
        <link id="u-page-google-font" rel="stylesheet" href="https://fonts.googleapis.com/css?family=Figtree:300,300i,400,400i,500,500i,600,600i,700,700i,800,800i,900,900i">
        <link id="u-header-footer-google-font" rel="stylesheet" href="https://fonts.googleapis.com/css?family=Playfair+Display:400,400i,500,500i,600,600i,700,700i,800,800i,900,900i|Figtree:300,300i,400,400i,500,500i,600,600i,700,700i,800,800i,900,900i">
        <script type="application/ld+json">
            {
                "@context": "http://schema.org",
                "@type": "Organization",
                "name": "laxmi",
                "url": "/",
                "logo": "https://assets.nicepagecdn.com/8242cc14/6447483/images/laxmi-log.png"
            }</script>
        <meta name="theme-color" content="#478ac9">
        <meta property="og:title" content="home">
        <meta property="og:description" content="">
        <meta property="og:type" content="website">
        <link rel="canonical" href="/">
    </head>
    <body data-home-page="https://laxmigroup.nicepage.io/home.html?version=003a3422-d58f-f6dd-8085-56987944d466" data-home-page-title="home" data-path-to-root="/" data-include-products="false" class="u-body u-xl-mode" data-lang="en">
        <header class=" u-border-no-bottom u-border-no-left u-border-no-right u-border-no-top u-clearfix u-header u-section-row-container" id="header" style="">
            <div class="u-section-rows" bis_skin_checked="1">
                <div class="u-palette-2-base u-section-row u-section-row-1">
                    <div class="u-clearfix u-sheet u-valign-middle-md u-sheet-1" bis_skin_checked="1">
                        <p class="u-align-center-md u-align-center-sm u-align-center-xs u-align-left-lg u-align-left-xl u-small-text u-text u-text-variant u-text-1" data-animation-out="1">Laxmi Group brings the region’s top builders under one vision. Learn more!</p>
                        <div class="u-expanded-width-xs u-list u-list-1" bis_skin_checked="1">
                            <div class="u-repeater u-repeater-1" bis_skin_checked="1">
                                <div class="u-align-center-xs u-container-style u-hover-feature u-list-item u-palette-4-light-2 u-radius u-repeater-item u-shading u-shape-round u-list-item-1" data-href="/company/lexis-international-limited.html" data-page-id="2602017343" title="lexis-international-limited" data-target="_blank">
                                    <div class="u-container-layout u-similar-container u-valign-middle u-container-layout-1" bis_skin_checked="1">
                                        <p class="u-align-center-xs u-text u-text-default u-text-white u-text-2">
                                            <span class="u-file-icon u-icon">
                                                <img src="https://assets.nicepagecdn.com/8242cc14/6447483/images/lexis-icon.png" alt="">
                                            </span>
                                             Lexis International Ltd

                                        </p>
                                    </div>
                                </div>
                                <div class="u-align-center-xs u-container-style u-hover-feature u-list-item u-palette-4-light-2 u-radius u-repeater-item u-shading u-shape-round u-list-item-2" data-href="https://eaconcontracting.co.ke/" data-target="_blank">
                                    <div class="u-container-layout u-similar-container u-valign-middle u-container-layout-2" bis_skin_checked="1">
                                        <p class="u-align-center-xs u-text u-text-default u-text-white u-text-3">
                                            <span class="u-file-icon u-icon">
                                                <img src="https://assets.nicepagecdn.com/8242cc14/6447483/images/eacon-icon.png" alt="">
                                            </span>
                                             ​Eacon Contracting Ltd

                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="u-palette-4-light-2 u-section-row u-sticky u-sticky-2f12" data-animation-name="" data-animation-duration="0" data-animation-delay="0" data-animation-direction="">
                    <div class="u-clearfix u-sheet u-sheet-2" bis_skin_checked="1">
                        <a href="/" data-page-id="735506366" class="u-image u-logo u-image-1" data-image-width="569" data-image-height="200" title="home">
                            <img src="https://assets.nicepagecdn.com/8242cc14/6447483/images/laxmi-log.png" class="u-logo-image u-logo-image-1">
                        </a>
                        <nav class="u-align-right u-menu u-menu-one-level u-menu-open-right u-offcanvas u-menu-1" role="navigation">
                            <div class="menu-collapse" style="font-size: 1rem; letter-spacing: 0px;" bis_skin_checked="1">
                                <a class="u-button-style u-custom-left-right-menu-spacing u-custom-padding-bottom u-custom-text-active-color u-custom-text-hover-color u-custom-top-bottom-menu-spacing u-hamburger-link u-nav-link u-text-active-palette-1-base u-text-hover-palette-2-base" href="#" tabindex="-1" aria-label="Open menu" aria-controls="05e4">
                                    <svg class="u-svg-link" viewBox="0 0 24 24">
                                        <use xlink:href="#menu-hamburger"></use>
                                    </svg>
                                    <svg class="u-svg-content" version="1.1" id="menu-hamburger" viewBox="0 0 16 16" x="0px" y="0px" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg">
                                        <g>
                                            <rect y="1" width="16" height="2"></rect>
                                            <rect y="7" width="16" height="2"></rect>
                                            <rect y="13" width="16" height="2"></rect>
                                        </g>
                                    </svg>
                                </a>
                            </div>
                            <div class="u-custom-menu u-nav-container" bis_skin_checked="1">
                                <ul class="u-nav u-unstyled u-nav-1" role="menubar">
                                    <li class="u-nav-item" role="none">
                                        <a class="u-button-style u-nav-link u-text-active-palette-2-base u-text-hover-palette-3-base" href="/" style="padding: 10px 20px;" role="menuitem">Home</a>
                                    </li>
                                    <li class="u-nav-item" role="none">
                                        <a class="u-button-style u-nav-link u-text-active-palette-2-base u-text-hover-palette-3-base" href="/about.html" style="padding: 10px 20px;" role="menuitem">About</a>
                                    </li>
                                    <li class="u-nav-item" role="none">
                                        <a class="u-button-style u-nav-link u-text-active-palette-2-base u-text-hover-palette-3-base" href="/services.html" style="padding: 10px 20px;" role="menuitem">Services</a>
                                    </li>
                                    <li class="u-nav-item" role="none">
                                        <a class="u-button-style u-nav-link u-text-active-palette-2-base u-text-hover-palette-3-base" href="/portfolio.html" style="padding: 10px 20px;" role="menuitem">Projects</a>
                                    </li>
                                    <li class="u-nav-item" role="none">
                                        <a class="u-button-style u-nav-link u-text-active-palette-2-base u-text-hover-palette-3-base" href="/health-and-safety.html" style="padding: 10px 20px;" role="menuitem">Safety</a>
                                    </li>
                                    <li class="u-nav-item" role="none">
                                        <a class="u-button-style u-nav-link u-text-active-palette-2-base u-text-hover-palette-3-base" href="/quality-control.html" style="padding: 10px 20px;" role="menuitem">Quality</a>
                                    </li>
                                    <li class="u-nav-item" role="none">
                                        <a class="u-button-style u-nav-link u-text-active-palette-2-base u-text-hover-palette-3-base" href="/contact.html" style="padding: 10px 20px;" role="menuitem">Contact</a>
                                    </li>
                                </ul>
                            </div>
                            <div class="u-custom-menu u-nav-container-collapse" id="05e4" role="region" aria-label="Menu panel" bis_skin_checked="1">
                                <div class="u-container-style u-inner-container-layout u-opacity u-opacity-95 u-palette-2-dark-3 u-sidenav" bis_skin_checked="1">
                                    <div class="u-inner-container-layout u-sidenav-overflow" bis_skin_checked="1">
                                        <div class="u-menu-close" tabindex="-1" aria-label="Close menu" bis_skin_checked="1"></div>
                                        <ul class="u-align-center u-nav u-popupmenu-items u-unstyled u-nav-2">
                                            <li class="u-nav-item">
                                                <a class="u-button-style u-nav-link" href="/">Home</a>
                                            </li>
                                            <li class="u-nav-item">
                                                <a class="u-button-style u-nav-link" href="/about.html">About</a>
                                            </li>
                                            <li class="u-nav-item">
                                                <a class="u-button-style u-nav-link" href="/services.html">Services</a>
                                            </li>
                                            <li class="u-nav-item">
                                                <a class="u-button-style u-nav-link" href="/portfolio.html">Projects</a>
                                            </li>
                                            <li class="u-nav-item">
                                                <a class="u-button-style u-nav-link" href="/health-and-safety.html">Safety</a>
                                            </li>
                                            <li class="u-nav-item">
                                                <a class="u-button-style u-nav-link" href="/quality-control.html">Quality</a>
                                            </li>
                                            <li class="u-nav-item">
                                                <a class="u-button-style u-nav-link" href="/contact.html">Contact</a>
                                            </li>
                                        </ul>
                                    </div>
                                </div>
                                <div class="u-menu-overlay u-opacity u-opacity-70 u-palette-4-dark-3" bis_skin_checked="1"></div>
                            </div>
                        </nav>
                    </div>
                </div>
            </div>
        </header>

        <!-- START OF REFACTORED HERO SECTION -->
        <section class="hero" aria-roledescription="carousel" aria-label="Featured Services">
            <div class="hero__background-slides">
                <!-- Slides will be populated by JavaScript -->
            </div>

            <div class="hero__overlay-container">
                <div class="hero__main-content-area">
                    <div class="hero__content-grid">
                        <div class="hero__content-left-column">
                            <div class="hero__content-left">
                                <h2 class="hero__title hero__title--intro">Introduction</h2>
                                <p class="hero__subtitle">Welcome to Laxmi Group your partner in End-to-End construction.</p>
                                <p class="hero__services-list">
                                    <strong>Project Management</strong> •
                                    <strong>Pre-Construction</strong> •
                                    <strong>EPC</strong> •
                                    <strong>General Contracting</strong> •
                                    <strong>Maintenance</strong>
                                </p>
                            </div>
                        </div>
                        <div class="hero__content-right-column">
                             <div class="hero__content-right">
                                <h3 class="hero__title hero__title--services">Our Services</h3>
                                <a id="hero__service-link" href="service/pre-feed.html" class="hero__service-box hero__service-box--clickable">
                                    <div class="hero__service-box-text-content">
                                        <h4 id="hero__service-name" class="hero__service-name">Service Name</h4>
                                    </div>
                                    <figure class="hero__service-box-image-figure">
                                        <img id="hero__service-image"
                                             src="images/services/prefeed1.webp"
                                             alt="Service illustration"
                                             class="hero__service-image">
                                        <figcaption class="hero--visually-hidden">Service illustration.</figcaption>
                                    </figure>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="hero__controls">
                <button type="button" class="hero__control-button hero__control-button--prev" aria-label="Previous slide">
                    <svg viewBox="0 0 31.494 31.494" focusable="false" aria-hidden="true"> <path d="M10.273,5.009c0.444-0.444,1.143-0.444,1.587,0c0.429,0.429,0.429,1.143,0,1.571l-8.047,8.047h26.554 c0.619,0,1.127,0.492,1.127,1.111c0,0.619-0.508,1.127-1.127,1.127H3.813l8.047,8.032c0.429,0.444,0.429,1.159,0,1.587 c-0.444,0.444-1.143,0.444-1.587,0l-9.952-9.952c-0.429-0.429-0.429-1.143,0-1.571L10.273,5.009z"></path> </svg>
                </button>
                <nav class="hero__dots-navigation" aria-label="Slide selection">
                    <!-- Dots will be populated by JavaScript -->
                </nav>
                <button type="button" class="hero__control-button hero__control-button--next" aria-label="Next slide">
                    <svg viewBox="0 0 31.49 31.49" focusable="false" aria-hidden="true"> <path d="M21.205,5.007c-0.429-0.444-1.143-0.444-1.587,0c-0.429,0.429-0.429,1.143,0,1.571l8.047,8.047H1.111 C0.492,14.626,0,15.118,0,15.737c0,0.619,0.492,1.127,1.111,1.127h26.554l-8.047,8.032c-0.429,0.444-0.429,1.159,0,1.587 c0.444,0.444,1.159,0.444,1.587,0l9.952-9.952c0.444-0.429,0.444-1.143,0-1.571L21.205,5.007z"></path> </svg>
                </button>
            </div>
        </section>

        <style>
            .hero {
                position: relative;
                width: 100%;
                height: 80vh; /* 80% of viewport height for desktop */
                min-height: 600px; /* Minimum height of 600px */
                overflow: hidden;
                display: flex;
                color: #ffffff;
                font-family: 'Figtree', sans-serif;
                z-index: 1; /* Set hero section to low z-index */
            }

            .hero__background-slides {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                z-index: 1; /* Low z-index within hero context */
            }

            .hero__background-slide {
                position: absolute;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-size: cover;
                background-position: center;
                opacity: 0;
                transition: opacity 0.8s ease-in-out;
                z-index: 1; /* Low z-index within hero context */
            }

            .hero__background-slide--active {
                opacity: 1;
                z-index: 2; /* Low z-index within hero context */
            }

            .hero__overlay-container {
                position: relative;
                z-index: 3; /* Low z-index within hero context */
                width: 100%;
                height: 100%;
                display: flex;
                flex-direction: column;
                justify-content: flex-end; /* Push content to bottom */
                padding: 0; /* Remove padding to allow full-width lines */
                box-sizing: border-box;
            }

            .hero__main-content-area {
                width: 100%;
                height: 50%; /* Take up the lower half of the hero */
                position: relative;
                padding: 1.5rem 2rem 0 2rem; /* Remove bottom padding */
                box-sizing: border-box;
            }

            /* Responsive content area adjustments */
            @media (max-width: 1200px) {
                .hero__main-content-area {
                    padding: 1.2rem 1.5rem 0 1.5rem;
                }
                .hero__title {
                    font-size: 1.1rem !important;
                }
                .hero__subtitle, .hero__services-list {
                    font-size: 0.95rem;
                }
                .hero__service-name {
                    font-size: 1.2rem;
                }
            }

            /* Full-width horizontal line at the vertical center */
            .hero__main-content-area::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 1px;
                background: #fcf7fb; /* Updated to new specified color */
                z-index: 4; /* Relative to hero context */
            }

            /* Full-height vertical line at the center */
            .hero__main-content-area::after {
                content: '';
                position: absolute;
                top: 0;
                bottom: 0;
                left: 50%;
                width: 1px;
                background: #fcf7fb; /* Updated to new specified color */
                transform: translateX(-50%);
                z-index: 4; /* Relative to hero context */
            }

            .hero__content-grid {
                display: grid;
                grid-template-columns: 1fr 1fr;
                width: 100%;
                height: 100%;
                position: relative;
                z-index: 5; /* Relative to hero context */
            }

            .hero__content-left-column {
                padding-right: 1.5rem; /* Space before vertical line */
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
            }

            .hero__content-right-column {
                padding-left: 1.5rem; /* Space after vertical line */
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
            }

            .hero__content-left,
            .hero__content-right {
                padding-bottom: 0; /* Remove bottom padding to align with hero bottom */
                height: 100%; /* Take full height of the grid area */
                display: flex;
                flex-direction: column;
                justify-content: flex-start;
            }

            .hero__content-divider {
                width: 100%;
                height: 1px;
                background: #fcf7fb; /* Updated to match new grid lines color */
                margin-bottom: 1rem;
            }

            .hero__title {
                font-size: 1.2rem !important; /* Consistent title size for both Introduction and Our Services */
                font-weight: 600 !important;
                margin-bottom: 0.75rem !important;
                color: rgba(255, 255, 255, 0.5) !important; /* Slightly more prominent for titles */
                margin-top: 0 !important; /* Reset any browser default margins */
                line-height: 1.2 !important; /* Consistent line height */
            }

            /* Ensure both h2 and h3 titles have identical styling */
            .hero h2.hero__title,
            .hero h3.hero__title {
                font-size: 1.2rem !important;
                font-weight: 600 !important;
                margin: 0 0 0.75rem 0 !important;
                padding: 0 !important;
                color: rgba(255, 255, 255, 0.9) !important;
                line-height: 1.2 !important;
            }

            .hero__subtitle {
                font-size: 1rem;
                font-weight: 400;
                margin-bottom: 1rem;
                line-height: 1.6;
                color: rgba(255, 255, 255, 0.9);
            }

            .hero__services-list {
                font-size: 1rem;
                font-weight: 400;
                line-height: 1.6;
                color: #ffffff;
            }

            .hero__services-list strong {
                font-weight: 700;
            }

            .hero__service-box {
                background-color: rgba(133, 38, 127, 0.25); /* Updated to #85267f with same transparency */
                backdrop-filter: blur(10px);
                border-radius: 12px;
                padding: 1.5rem;
                display: flex;
                align-items: center;
                gap: 1.5rem;
                min-height: 100px;
                transition: all 0.3s ease; /* Smooth transitions for responsive changes */
                text-decoration: none; /* Remove link underline */
                color: inherit; /* Inherit text color */
                margin-bottom: 10px; /* Gap between service box and controls */
            }

            .hero__service-box--clickable {
                cursor: pointer;
            }

            .hero__service-box--clickable:hover {
                background-color: rgba(133, 38, 127, 0.85); /* Slightly more opaque on hover */
                transform: translateY(-2px); /* Subtle lift effect */
                box-shadow: 0 4px 12px rgba(133, 38, 127, 0.3); /* Add shadow on hover */
            }

            .hero__service-box--clickable:active {
                transform: translateY(0); /* Reset on click */
            }

            /* Responsive service box adjustments */
            @media (max-width: 1200px) {
                .hero__service-box {
                    padding: 1.2rem;
                    gap: 1.2rem;
                    min-height: 90px;
                    flex-direction: row; /* Maintain horizontal layout */
                }
            }

            .hero__service-box-text-content {
                flex-grow: 1;
            }

            .hero__service-name {
                font-size: 1.3rem; /* Increased font size for better prominence */
                font-weight: 500; /* Slightly bolder for better readability */
                color: #ffffff;
                margin: 0;
                line-height: 1.4; /* Adjusted line-height for larger text */
            }

            .hero__service-box-image-figure {
                margin: 0;
                flex-shrink: 0;
            }

            .hero__service-image {
                width: 150px;
                height: auto;
                max-height: 70px; /* To ensure image doesn't get too tall if service name is short */
                object-fit: cover;
                border-radius: 8px;
                background-color: #f0f0f0;
                transition: all 0.3s ease; /* Smooth transitions */
            }

            /* Responsive service image adjustments */
            @media (max-width: 1200px) {
                .hero__service-image {
                    width: 130px;
                    max-height: 60px;
                }
            }

            .hero__controls {
                position: absolute;
                bottom: 2rem; /* Increased bottom spacing from section end */
                left: 75%; /* Center of right column (50% + 25% = 75%) */
                transform: translateX(-50%); /* Perfect centering under service box */
                z-index: 6; /* Relative to hero context */
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0.75rem; /* Reduced gap for inline layout */
                width: auto;
                box-sizing: border-box;
                margin-top: 10px; /* Gap between service box and controls */
            }

            .hero__control-button {
                background-color: rgba(255, 255, 255, 0.9);
                border: 1px solid rgba(255, 255, 255, 0.5);
                border-radius: 50%;
                width: 44px; /* Slightly larger for desktop */
                height: 44px;
                display: flex;
                align-items: center;
                justify-content: center;
                cursor: pointer;
                padding: 0;
                transition: background-color 0.3s ease, transform 0.2s ease;
            }

            .hero__control-button:hover {
                background-color: #ffffff;
                transform: scale(1.1);
            }

            .hero__control-button svg {
                width: 20px; /* Slightly larger for desktop */
                height: 20px;
                fill: #684278;
            }

            .hero__dots-navigation {
                display: flex;
                gap: 0.4rem; /* Reduced gap for inline layout */
            }

            .hero__dot {
                width: 12px; /* Slightly larger for desktop */
                height: 12px;
                border-radius: 50%;
                background-color: rgba(255, 255, 255, 0.5);
                border: none;
                padding: 0;
                cursor: pointer;
                transition: background-color 0.3s ease, transform 0.2s ease;
            }

            .hero__dot:hover {
                transform: scale(1.2);
            }

            .hero__dot--active {
                background-color: #85267f; /* Updated to match service box color */
            }

            .hero--visually-hidden {
                position: absolute;
                width: 1px;
                height: 1px;
                padding: 0;
                margin: -1px;
                overflow: hidden;
                clip: rect(0, 0, 0, 0);
                white-space: nowrap;
                border: 0;
            }

            /* Responsive Adjustments */
            @media (max-width: 992px) { /* Tablet */
                .hero {
                    height: 65vh; /* Adjusted responsive height for tablet */
                }
                .hero__overlay-container {
                    justify-content: flex-end; /* Move content to bottom on smaller screens */
                }
                .hero__main-content-area {
                    height: auto; /* Allow content to size naturally */
                    padding: 1rem 1.5rem 1.5rem 1.5rem;
                }
                /* Hide the cross lines on tablet and mobile */
                .hero__main-content-area::before,
                .hero__main-content-area::after {
                    display: none;
                }
                .hero__content-grid {
                    grid-template-columns: 1fr; /* Stack columns */
                    gap: 0; /* No gap needed when stacked, handled by column padding */
                }
                .hero__content-left-column {
                    border-right: none; /* Remove vertical line */
                    padding-right: 0;
                    padding-bottom: 1.5rem; /* Space between stacked introduction and services */
                }
                .hero__content-right-column {
                    padding-left: 0;
                }
                .hero__content-left, .hero__content-right {
                    text-align: center;
                }
                .hero__service-box {
                    flex-direction: column;
                    text-align: center;
                    padding: 1rem;
                }
                .hero__service-name {
                    font-size: 1.1rem; /* Increased for tablet to maintain prominence */
                }
                .hero__service-box {
                    flex-direction: row; /* Maintain horizontal layout on tablet */
                    gap: 1rem;
                    padding: 1rem;
                    min-height: 80px;
                }
                .hero__service-box-text-content {
                    flex: 1; /* Take available space */
                }
                .hero__service-image {
                    width: 120px;
                    max-height: 60px;
                }
                .hero__controls {
                    position: absolute;
                    bottom: 1.5rem; /* Increased bottom spacing for tablet */
                    left: 50%; /* Center of full width since layout is now single column */
                    transform: translateX(-50%); /* Perfect centering under full-width content */
                    justify-content: center;
                    width: auto;
                    gap: 0.6rem; /* Slightly reduced gap for tablet */
                }
                .hero__control-button {
                    width: 40px; /* Slightly smaller for tablet */
                    height: 40px;
                }
                .hero__control-button svg {
                    width: 18px;
                    height: 18px;
                }
                .hero__dot {
                    width: 10px;
                    height: 10px;
                }
            }

            @media (max-width: 768px) { /* Mobile Landscape / Large Mobile */
                 .hero {
                    height: 70vh; /* Adjusted responsive height for mobile landscape */
                }
                .hero__main-content-area {
                    padding: 0.5rem 1rem 1rem 1rem; /* Reduced top padding to create more space for controls */
                }
                 .hero__title {
                    font-size: 1rem;
                }
                .hero__subtitle, .hero__services-list {
                    font-size: 0.9rem;
                }
                .hero__service-name {
                    font-size: 1rem; /* Maintain larger size on mobile */
                }
                .hero__service-box {
                    flex-direction: row; /* Maintain horizontal layout on mobile landscape */
                    gap: 0.8rem;
                    padding: 0.8rem;
                    min-height: 70px;
                }
                .hero__service-box-text-content {
                    flex: 1; /* Take available space */
                }
                .hero__service-image {
                    width: 100px;
                    max-height: 50px;
                }
                .hero__controls {
                    position: absolute;
                    bottom: 1.25rem; /* Increased bottom spacing for mobile landscape */
                    left: 50%; /* Center of full width since layout is single column */
                    transform: translateX(-50%); /* Perfect centering under full-width content */
                    gap: 0.5rem; /* Smaller gap for mobile */
                }
                .hero__control-button {
                    width: 36px;
                    height: 36px;
                }
                .hero__control-button svg {
                    width: 16px;
                    height: 16px;
                }
                .hero__dot {
                    width: 8px;
                    height: 8px;
                }
            }

            @media (max-width: 480px) { /* Mobile Portrait */
                .hero {
                    height: 60vh; /* Adjusted responsive height for mobile portrait */
                }
                .hero__main-content-area {
                    padding: 0.25rem 1rem 1rem 1rem; /* Further reduced top padding for mobile portrait */
                }
                 .hero__content-left-column {
                    padding-bottom: 1rem;
                }
                .hero__service-box {
                     gap: 0.5rem;
                }
                .hero__controls {
                    position: absolute;
                    bottom: 1rem; /* Maintain 1rem for mobile portrait to maximize space */
                    left: 50%; /* Center of full width since layout is single column */
                    transform: translateX(-50%); /* Perfect centering under full-width content */
                    gap: 0.4rem; /* Even smaller gap for mobile portrait */
                }
                .hero__control-button {
                    width: 32px; /* Smaller for mobile portrait */
                    height: 32px;
                }
                .hero__control-button svg {
                    width: 14px;
                    height: 14px;
                }
                .hero__dot {
                    width: 7px;
                    height: 7px;
                }
                .hero__service-box {
                    flex-direction: row; /* Maintain horizontal layout even on mobile portrait */
                    gap: 0.6rem;
                    padding: 0.6rem;
                    min-height: 60px;
                }
                .hero__service-box-text-content {
                    flex: 1; /* Take available space */
                }
                .hero__service-image {
                    width: 80px;
                    max-height: 40px;
                }
            }

        </style>

        <script>
            document.addEventListener('DOMContentLoaded', () => {
                const heroData = [
                    {
                        bgImageUrl: "images/hero-section/hero-eni.webp",
                        serviceName: "Preliminary Front-End Engineering and Design", // Full name for Pre-FEED
                        serviceImage: "images/services/prefeed1.webp",
                        serviceUrl: "service/pre-feed.html"
                    },
                    {
                        bgImageUrl: "images/hero-section/hero-small4x.webp",
                        serviceName: "Front-End Engineering and Design", // Full name for FEED
                        serviceImage: "images/services/feed.webp",
                        serviceUrl: "service/feed.html"
                    },
                    {
                        bgImageUrl: "images/hero-section/hero-cafe4x.webp",
                        serviceName: "Preconstruction / Early Contractor Engagement", // Updated full name
                        serviceImage: "images/services/precon1.webp",
                        serviceUrl: "service/pre-construction.html"
                    },
                    {
                        bgImageUrl: "images/hero-gowgate2x.webp",
                        serviceName: "Construction Services",
                        serviceImage: "images/services/cons1.webp",
                        serviceUrl: "service/construction.html"
                    },
                    {
                        bgImageUrl: "images/hero-jkuat4x.webp",
                        serviceName: "Procurement Services",
                        serviceImage: "images/services/procure1.webp",
                        serviceUrl: "service/procurement.html"
                    },
                    {
                        bgImageUrl: "images/hero-ldc4x.webp",
                        serviceName: "Construction Management Services", // Added "Services"
                        serviceImage: "images/services/manage1.webp",
                        serviceUrl: "service/construction-management.html"
                    },
                    {
                        bgImageUrl: "images/hero-roof4x.webp",
                        serviceName: "Facilities Maintenance Services", // Added "Services"
                        serviceImage: "images/services/maintain1.webp",
                        serviceUrl: "service/facilities-maintenance.html"
                    }
                ];

                const slidesContainer = document.querySelector('.hero__background-slides');
                const serviceNameElement = document.getElementById('hero__service-name');
                const serviceImageElement = document.getElementById('hero__service-image'); // Now dynamic
                const serviceLinkElement = document.getElementById('hero__service-link'); // Service link element

                const dotsNavigation = document.querySelector('.hero__dots-navigation');
                const prevButton = document.querySelector('.hero__control-button--prev');
                const nextButton = document.querySelector('.hero__control-button--next');

                let currentSlideIndex = 0;
                let slideInterval;
                const SLIDE_INTERVAL_DURATION = 5000;

                heroData.forEach((data, index) => {
                    const slide = document.createElement('div');
                    slide.classList.add('hero__background-slide');
                    slide.style.backgroundImage = `url('${data.bgImageUrl}')`;
                    slide.setAttribute('aria-hidden', index === 0 ? 'false' : 'true');
                    slide.setAttribute('role', 'tabpanel');
                    slide.id = `hero-slide-${index + 1}`;
                    slidesContainer.appendChild(slide);

                    const dot = document.createElement('button');
                    dot.classList.add('hero__dot');
                    dot.setAttribute('type', 'button');
                    dot.setAttribute('aria-label', `Go to slide ${index + 1}`);
                    dot.setAttribute('aria-controls', `hero-slide-${index + 1}`);
                    if (index === 0) {
                        dot.classList.add('hero__dot--active');
                        dot.setAttribute('aria-selected', 'true');
                    }
                    dot.addEventListener('click', () => {
                        goToSlide(index);
                        resetSlideInterval();
                    });
                    dotsNavigation.appendChild(dot);
                });

                const slideElements = slidesContainer.querySelectorAll('.hero__background-slide');
                const dotElements = dotsNavigation.querySelectorAll('.hero__dot');

                function updateSlideContent(index) {
                    serviceNameElement.textContent = heroData[index].serviceName;
                    serviceImageElement.src = heroData[index].serviceImage;
                    serviceImageElement.alt = `${heroData[index].serviceName} illustration`;
                    serviceLinkElement.href = heroData[index].serviceUrl; // Update the link URL
                }

                function showSlide(index) {
                    slideElements.forEach((slide, i) => {
                        slide.classList.remove('hero__background-slide--active');
                        slide.setAttribute('aria-hidden', 'true');
                        dotElements[i].classList.remove('hero__dot--active');
                        dotElements[i].setAttribute('aria-selected', 'false');
                    });

                    slideElements[index].classList.add('hero__background-slide--active');
                    slideElements[index].setAttribute('aria-hidden', 'false');
                    dotElements[index].classList.add('hero__dot--active');
                    dotElements[index].setAttribute('aria-selected', 'true');

                    updateSlideContent(index);
                    currentSlideIndex = index;
                }

                function nextSlideFn() { // Renamed to avoid conflict with element 'nextSlide'
                    let newIndex = (currentSlideIndex + 1) % heroData.length;
                    showSlide(newIndex);
                }

                function prevSlideFn() { // Renamed
                    let newIndex = (currentSlideIndex - 1 + heroData.length) % heroData.length;
                    showSlide(newIndex);
                }

                function goToSlide(index) {
                    showSlide(index);
                }

                function startSlideInterval() {
                    slideInterval = setInterval(nextSlideFn, SLIDE_INTERVAL_DURATION);
                }

                function resetSlideInterval() {
                    clearInterval(slideInterval);
                    startSlideInterval();
                }

                prevButton.addEventListener('click', () => {
                    prevSlideFn();
                    resetSlideInterval();
                });

                nextButton.addEventListener('click', () => {
                    nextSlideFn();
                    resetSlideInterval();
                });

                showSlide(0);
                startSlideInterval();
            });
        </script>
        <!-- END OF REFACTORED HERO SECTION -->

        <footer class="u-align-center u-clearfix u-footer u-grey-80 u-footer" id="sec-1d4c">
            <div class="u-clearfix u-sheet u-sheet-1" bis_skin_checked="1">
                <p class="u-small-text u-text u-text-variant u-text-1">Sample text. Click to select the Text Element.</p>
            </div>
        </footer>
    </body>
</html>