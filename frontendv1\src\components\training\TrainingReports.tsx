import React, { useState, useEffect } from "react";
import {
	<PERSON><PERSON><PERSON>,
	Clock,
	BarChart3,
	Printer,
	Download,
	FileText,
	TrendingUp,
} from "lucide-react";
import { TrainingReport } from "../../types/training";

interface TrainingReportsProps {
	siteId: string;
}

interface ReportCardProps {
	title: string;
	description: string;
	icon: React.ReactNode;
	onClick: () => void;
}

const ReportCard: React.FC<ReportCardProps> = ({
	title,
	description,
	icon,
	onClick,
}) => {
	return (
		<div
			className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200 cursor-pointer hover:border-gray-300"
			onClick={onClick}
		>
			<div className="flex items-start space-x-4">
				<div className="flex-shrink-0">
					<div className="w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center text-green-600">
						{icon}
					</div>
				</div>
				<div className="flex-1 min-w-0">
					<h3 className="text-sm font-medium text-gray-900 mb-1">{title}</h3>
					<p className="text-sm text-gray-500">{description}</p>
				</div>
			</div>
		</div>
	);
};

// Mock report data
const mockReportData: TrainingReport = {
	title: "Training Compliance Summary",
	description: "Overall training compliance by trade and status",
	generatedAt: new Date(),
	chartData: {
		labels: ["Compliant", "Expiring Soon", "Expired", "Incomplete"],
		datasets: [
			{
				data: [65, 15, 8, 12],
				backgroundColor: ["#10B981", "#F59E0B", "#EF4444", "#6B7280"],
			},
		],
	},
	chartType: "pie",
	tableData: [
		{
			trade: "Electrician",
			total: 12,
			compliant: 8,
			expiring: 2,
			expired: 1,
			incomplete: 1,
		},
		{
			trade: "Plumber",
			total: 8,
			compliant: 6,
			expiring: 1,
			expired: 0,
			incomplete: 1,
		},
		{
			trade: "Mason",
			total: 15,
			compliant: 10,
			expiring: 3,
			expired: 1,
			incomplete: 1,
		},
		{
			trade: "Carpenter",
			total: 7,
			compliant: 5,
			expiring: 1,
			expired: 1,
			incomplete: 0,
		},
	],
	columns: [
		{ key: "trade", label: "Trade" },
		{ key: "total", label: "Total Workers" },
		{ key: "compliant", label: "Compliant" },
		{ key: "expiring", label: "Expiring Soon" },
		{ key: "expired", label: "Expired" },
		{ key: "incomplete", label: "Incomplete" },
	],
};

const TrainingReports: React.FC<TrainingReportsProps> = ({ siteId }) => {
	const [reportType, setReportType] = useState("compliance");
	const [dateRange, setDateRange] = useState("last-30");
	const [groupBy, setGroupBy] = useState("trade");
	const [reportData, setReportData] = useState<TrainingReport | null>(null);
	const [isGenerating, setIsGenerating] = useState(false);

	useEffect(() => {
		// TODO: Fetch available report templates from API
	}, [siteId]);

	const generateReport = (_type: string) => {
		setIsGenerating(true);
		// TODO: Call API to generate report
		setTimeout(() => {
			setReportData(mockReportData);
			setIsGenerating(false);
		}, 1000);
	};

	const generateCustomReport = () => {
		setIsGenerating(true);
		// TODO: Call API to generate custom report
		setTimeout(() => {
			setReportData({
				...mockReportData,
				title: `Custom ${reportType.charAt(0).toUpperCase() + reportType.slice(1)} Report`,
				description: `Custom report grouped by ${groupBy} for ${dateRange}`,
			});
			setIsGenerating(false);
		}, 1000);
	};

	const printReport = () => {
		window.print();
	};

	const exportReport = () => {
		// TODO: Implement export functionality
		console.log("Export report");
	};

	const formatDate = (date: Date) => {
		return date.toLocaleDateString("en-US", {
			month: "long",
			day: "numeric",
			year: "numeric",
			hour: "2-digit",
			minute: "2-digit",
		});
	};

	return (
		<div className="space-y-6">
			{/* Report Templates */}
			<div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
				<h3 className="text-lg font-medium mb-4">Report Templates</h3>
				<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
					<ReportCard
						title="Compliance Summary"
						description="Overall training compliance by trade and status"
						icon={<PieChart className="h-5 w-5" />}
						onClick={() => generateReport("compliance-summary")}
					/>
					<ReportCard
						title="Expiring Certifications"
						description="Workers with certifications expiring in next 30/60/90 days"
						icon={<Clock className="h-5 w-5" />}
						onClick={() => generateReport("expiring-certifications")}
					/>
					<ReportCard
						title="Training Completion"
						description="Training completion rates by program"
						icon={<BarChart3 className="h-5 w-5" />}
						onClick={() => generateReport("training-completion")}
					/>
				</div>
			</div>

			{/* Custom Report Builder */}
			<div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
				<h3 className="text-lg font-medium mb-4">Custom Report</h3>
				<div className="space-y-4">
					<div className="grid grid-cols-1 md:grid-cols-3 gap-4">
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Report Type
							</label>
							<select
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
								value={reportType}
								onChange={(e) => setReportType(e.target.value)}
							>
								<option value="compliance">Compliance Report</option>
								<option value="completion">Completion Report</option>
								<option value="expiry">Expiry Report</option>
								<option value="attendance">Attendance Report</option>
							</select>
						</div>
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Date Range
							</label>
							<select
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
								value={dateRange}
								onChange={(e) => setDateRange(e.target.value)}
							>
								<option value="last-30">Last 30 Days</option>
								<option value="last-90">Last 90 Days</option>
								<option value="year-to-date">Year to Date</option>
								<option value="custom">Custom Range</option>
							</select>
						</div>
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-1">
								Group By
							</label>
							<select
								className="w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-green-500"
								value={groupBy}
								onChange={(e) => setGroupBy(e.target.value)}
							>
								<option value="trade">Trade</option>
								<option value="program">Training Program</option>
								<option value="status">Compliance Status</option>
								<option value="department">Department</option>
							</select>
						</div>
					</div>

					<div className="flex justify-end">
						<button
							className="px-4 py-2 bg-green-600 text-white rounded-md text-sm font-medium hover:bg-green-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
							onClick={generateCustomReport}
							disabled={isGenerating}
						>
							{isGenerating ? "Generating..." : "Generate Report"}
						</button>
					</div>
				</div>
			</div>

			{/* Report Preview */}
			{reportData && (
				<div className="bg-white p-6 rounded-lg border border-gray-200 shadow-sm">
					<div className="flex justify-between items-center mb-4">
						<div>
							<h3 className="text-lg font-medium">{reportData.title}</h3>
							<p className="text-sm text-gray-500 mt-1">
								Generated on {formatDate(reportData.generatedAt)}
							</p>
						</div>
						<div className="flex space-x-2">
							<button
								className="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50 transition-colors"
								onClick={printReport}
							>
								<Printer className="h-4 w-4 inline mr-1" />
								Print
							</button>
							<button
								className="px-3 py-1 border border-gray-300 rounded-md text-sm hover:bg-gray-50 transition-colors"
								onClick={exportReport}
							>
								<Download className="h-4 w-4 inline mr-1" />
								Export
							</button>
						</div>
					</div>

					{/* Chart Placeholder */}
					<div className="mb-6 p-8 bg-gray-50 rounded-lg border-2 border-dashed border-gray-300">
						<div className="text-center">
							<TrendingUp className="h-12 w-12 text-gray-400 mx-auto mb-4" />
							<h4 className="text-lg font-medium text-gray-900 mb-2">
								Chart Visualization
							</h4>
							<p className="text-gray-500 mb-4">
								{reportData.chartType.charAt(0).toUpperCase() +
									reportData.chartType.slice(1)}{" "}
								chart would be displayed here
							</p>
							<p className="text-sm text-gray-400">
								This would typically integrate with a charting library like
								Chart.js or Recharts
							</p>
						</div>
					</div>

					{/* Data Table */}
					<div className="overflow-x-auto">
						<table className="min-w-full divide-y divide-gray-200">
							<thead className="bg-gray-50">
								<tr>
									{reportData.columns.map((column) => (
										<th
											key={column.key}
											scope="col"
											className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"
										>
											{column.label}
										</th>
									))}
								</tr>
							</thead>
							<tbody className="bg-white divide-y divide-gray-200">
								{reportData.tableData.map((row, index) => (
									<tr key={index} className="hover:bg-gray-50">
										{reportData.columns.map((column) => (
											<td
												key={column.key}
												className="px-6 py-4 whitespace-nowrap text-sm text-gray-900"
											>
												{row[column.key]}
											</td>
										))}
									</tr>
								))}
							</tbody>
						</table>
					</div>
				</div>
			)}

			{/* Empty State */}
			{!reportData && !isGenerating && (
				<div className="bg-white p-12 rounded-lg border border-gray-200 shadow-sm text-center">
					<FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
					<h3 className="text-lg font-medium text-gray-900 mb-2">
						No Report Generated
					</h3>
					<p className="text-gray-500">
						Select a report template above or create a custom report to get
						started.
					</p>
				</div>
			)}
		</div>
	);
};

export default TrainingReports;
