import React from 'react';
import { useNavigate } from 'react-router-dom';
import GraphPaperLines from '../components/MouseFollowLines';
import {
  Shield,
  Users,
  FileText,
  BarChart3,
  Smartphone,
  Clock,
  CheckCircle,
  ArrowRight,
  Star,
  Building2,
  HardHat,
  Zap,
  Wrench,
  Hammer,
  Settings,
  Truck,
  MapPin,
  Calendar,
  Camera,
  AlertTriangle,
  Target,
  TrendingUp,
  Activity,
  Database,
  Factory,
  Cog
} from 'lucide-react';

const LandingPage: React.FC = () => {
  const navigate = useNavigate();

  const handleSignIn = () => {
    navigate('/login');
  };

  const handleGetStarted = () => {
    navigate('/register');
  };

  return (
    <div className="min-h-screen bg-white relative">
      {/* Graph Paper Lines Animation */}
      <GraphPaperLines />

      {/* Global Background Elements */}
      <div className="fixed inset-0 pointer-events-none z-0 overflow-hidden">
        {/* Top area background elements */}
        <div className="absolute top-20 left-10 w-6 h-6 border border-orange-300 rotate-45 opacity-5"></div>
        <div className="absolute top-40 right-20 w-8 h-8 border border-yellow-400 rotate-12 opacity-5"></div>
        <div className="absolute top-60 left-1/4 opacity-5">
          <Wrench className="h-6 w-6 text-amber-400 rotate-45" />
        </div>

        {/* Middle area background elements */}
        <div className="absolute top-1/3 right-10 w-10 h-10 border border-orange-300 rotate-45 opacity-5"></div>
        <div className="absolute top-1/2 left-16 opacity-5">
          <HardHat className="h-8 w-8 text-yellow-400 rotate-12" />
        </div>
        <div className="absolute top-2/3 right-1/4 opacity-5">
          <Factory className="h-10 w-10 text-amber-400 rotate-45" />
        </div>

        {/* Bottom area background elements */}
        <div className="absolute bottom-40 left-20 w-8 h-8 border border-yellow-400 rotate-12 opacity-5"></div>
        <div className="absolute bottom-60 right-16 opacity-5">
          <Cog className="h-6 w-6 text-orange-400 rotate-45" />
        </div>
        <div className="absolute bottom-20 left-1/3 w-6 h-6 border border-amber-400 rotate-45 opacity-5"></div>
        <div className="absolute bottom-80 right-1/3 opacity-5">
          <Building2 className="h-8 w-8 text-orange-300 rotate-12" />
        </div>

        {/* Additional scattered elements */}
        <div className="absolute top-1/4 left-1/2 w-4 h-4 border border-yellow-300 rotate-45 opacity-5"></div>
        <div className="absolute top-3/4 right-1/2 opacity-5">
          <Hammer className="h-6 w-6 text-amber-300 rotate-45" />
        </div>
        <div className="absolute top-1/6 right-1/3 w-6 h-6 border border-orange-200 rotate-12 opacity-5"></div>
        <div className="absolute bottom-1/4 left-1/2 opacity-5">
          <Settings className="h-6 w-6 text-yellow-300 rotate-45" />
        </div>
      </div>
      {/* Navigation */}
      <nav className="bg-white border-b border-orange-200 sticky top-0 z-50 shadow-lg backdrop-blur-sm bg-white/95">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-18">
            <div className="flex items-center">
              <div className="flex-shrink-0 flex items-center">
                <div className="relative">
                  <HardHat className="h-10 w-10 text-orange-600" />
                  <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                </div>
                <div className="ml-3">
                  <span className="text-2xl font-bold text-gray-900 font-['Roboto_Slab']">Workforce</span>
                  <div className="text-xs text-orange-600 font-['Open_Sans'] font-medium">Construction Management</div>
                </div>
              </div>
            </div>
            <div className="hidden md:block">
              <div className="ml-10 flex items-baseline space-x-6">
                <a href="#features" className="text-gray-700 hover:text-orange-600 px-3 py-2 rounded-md text-sm font-medium transition-colors font-['Open_Sans']">Solutions</a>
                <a href="#benefits" className="text-gray-700 hover:text-orange-600 px-3 py-2 rounded-md text-sm font-medium transition-colors font-['Open_Sans']">Benefits</a>
                <a href="#testimonials" className="text-gray-700 hover:text-orange-600 px-3 py-2 rounded-md text-sm font-medium transition-colors font-['Open_Sans']">Case Studies</a>
                <a href="#contact" className="text-gray-700 hover:text-orange-600 px-3 py-2 rounded-md text-sm font-medium transition-colors font-['Open_Sans']">Contact</a>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              <button
                onClick={handleSignIn}
                className="text-gray-700 hover:text-orange-600 px-4 py-2 rounded-md text-sm font-medium transition-colors font-['Open_Sans'] border border-gray-300 hover:border-orange-300"
              >
                Sign In
              </button>
              <button
                onClick={handleGetStarted}
                className="bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800 text-white px-6 py-2 rounded-md text-sm font-medium transition-all duration-200 shadow-lg transform hover:scale-105 font-['Open_Sans']"
              >
                Get Started
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-orange-50 via-yellow-50 to-amber-100 py-20 lg:py-32 overflow-hidden">
        {/* Enhanced Construction Background Activity */}
        <div className="absolute inset-0 opacity-8">
          {/* Geometric construction patterns */}
          <div className="absolute top-10 left-10 w-20 h-20 border-2 border-orange-400 rotate-45 animate-pulse"></div>
          <div className="absolute top-32 right-20 w-16 h-16 border-2 border-yellow-500 rotate-12 animate-pulse delay-1000"></div>
          <div className="absolute bottom-20 left-1/4 w-12 h-12 border-2 border-amber-500 rotate-45 animate-pulse delay-500"></div>
          <div className="absolute bottom-40 right-1/3 w-24 h-24 border-2 border-orange-300 rotate-12 animate-pulse delay-1500"></div>
          <div className="absolute top-1/2 left-1/2 w-32 h-32 border-2 border-yellow-400 rotate-45 transform -translate-x-1/2 -translate-y-1/2 animate-pulse delay-700"></div>

          {/* Construction tool silhouettes */}
          <div className="absolute top-16 right-16 opacity-15 animate-bounce">
            <Factory className="h-12 w-12 text-orange-500 rotate-12" />
          </div>
          <div className="absolute bottom-16 left-16 opacity-15 animate-bounce delay-1000">
            <Building2 className="h-10 w-10 text-yellow-600 rotate-45" />
          </div>
          <div className="absolute top-1/3 left-20 opacity-10 animate-pulse delay-2000">
            <Hammer className="h-8 w-8 text-amber-500 rotate-45" />
          </div>
          <div className="absolute bottom-1/3 right-20 opacity-10 animate-pulse delay-3000">
            <Wrench className="h-6 w-6 text-orange-400 rotate-12" />
          </div>

          {/* Grid pattern overlay */}
          <div className="absolute inset-0 opacity-5" style={{
            backgroundImage: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ea580c' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
          }}></div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="lg:grid lg:grid-cols-12 lg:gap-8">
            <div className="sm:text-center md:max-w-2xl md:mx-auto lg:col-span-6 lg:text-left">
              <h1 className="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">
                <span className="block font-['Roboto_Slab'] text-5xl sm:text-6xl md:text-7xl text-orange-800 font-bold">Smart Workforce</span>
                <span className="block text-orange-600 font-['Roboto_Slab'] text-3xl sm:text-4xl md:text-5xl">Management</span>
                <span className="block font-['Open_Sans'] text-2xl sm:text-3xl md:text-4xl text-gray-700 mt-2">for Construction Sites</span>
              </h1>
              <p className="mt-6 text-base text-gray-600 sm:mt-8 sm:text-xl lg:text-lg xl:text-xl font-['Open_Sans'] leading-relaxed">
                Advanced biometric access control, real-time workforce monitoring, and comprehensive safety management. Built for the modern construction industry.
              </p>
              <div className="mt-8 sm:max-w-lg sm:mx-auto sm:text-center lg:text-left lg:mx-0">
                <div className="flex flex-col sm:flex-row gap-4">
                  <button
                    onClick={handleGetStarted}
                    className="flex items-center justify-center px-8 py-4 border border-transparent text-base font-medium rounded-lg text-white bg-gradient-to-r from-orange-600 to-orange-700 hover:from-orange-700 hover:to-orange-800 md:py-4 md:text-lg md:px-10 shadow-lg transform hover:scale-105 transition-all duration-200 font-['Open_Sans']"
                  >
                    Start Free Trial
                    <ArrowRight className="ml-2 h-5 w-5" />
                  </button>
                  <button className="flex items-center justify-center px-8 py-4 border border-orange-300 text-base font-medium rounded-lg text-orange-700 bg-white hover:bg-orange-50 md:py-4 md:text-lg md:px-10 shadow-md transition-all duration-200 font-['Open_Sans']">
                    Watch Demo
                  </button>
                </div>
                <p className="mt-4 text-sm text-gray-500 font-['Open_Sans']">
                  ✓ No setup fees  ✓ 30-day trial  ✓ Cancel anytime
                </p>
              </div>
            </div>
            <div className="mt-12 relative sm:max-w-lg sm:mx-auto lg:mt-0 lg:max-w-none lg:mx-0 lg:col-span-6 lg:flex lg:items-center">
              <div className="relative mx-auto w-full rounded-xl shadow-2xl lg:max-w-md transform hover:scale-105 transition-transform duration-300">
                <div className="relative block w-full bg-white rounded-xl overflow-hidden border-4 border-orange-200">
                  <img
                    className="w-full"
                    src="https://images.unsplash.com/photo-1504307651254-35680f356dfd?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
                    alt="Construction workers using digital tools"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-orange-900/30 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 right-4">
                    <div className="bg-white/95 backdrop-blur-sm rounded-lg p-3 border border-orange-200">
                      <div className="flex items-center space-x-2">
                        <Activity className="h-5 w-5 text-orange-600" />
                        <span className="text-sm font-medium text-gray-800 font-['Open_Sans']">Workforce Monitoring & Event Logging</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="bg-white py-16 border-t border-orange-100 relative overflow-hidden">
        {/* Sparse background elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-8 left-16 w-10 h-10 border border-orange-300 rotate-45"></div>
          <div className="absolute bottom-8 right-16 w-8 h-8 border border-yellow-400 rotate-12"></div>
          <div className="absolute top-1/2 left-1/3 opacity-8">
            <Wrench className="h-6 w-6 text-amber-400 rotate-45" />
          </div>
          <div className="absolute top-1/2 right-1/3 opacity-8">
            <HardHat className="h-8 w-8 text-orange-300 rotate-12" />
          </div>
        </div>
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-2 gap-8 md:grid-cols-4">
            <div className="text-center group">
              <div className="text-4xl font-bold text-orange-600 font-['Roboto_Slab'] group-hover:scale-110 transition-transform duration-200">99.8%</div>
              <div className="text-sm text-gray-600 font-medium font-['Open_Sans'] mt-2">Access Accuracy</div>
            </div>
            <div className="text-center group">
              <div className="text-4xl font-bold text-yellow-600 font-['Roboto_Slab'] group-hover:scale-110 transition-transform duration-200">60%</div>
              <div className="text-sm text-gray-600 font-medium font-['Open_Sans'] mt-2">Admin Time Saved</div>
            </div>
            <div className="text-center group">
              <div className="text-4xl font-bold text-amber-600 font-['Roboto_Slab'] group-hover:scale-110 transition-transform duration-200">1000+</div>
              <div className="text-sm text-gray-600 font-medium font-['Open_Sans'] mt-2">Sites Managed</div>
            </div>
            <div className="text-center group">
              <div className="text-4xl font-bold bg-gradient-to-r from-purple-600 to-pink-600 bg-clip-text text-transparent font-['Roboto_Slab'] group-hover:scale-110 transition-transform duration-200">24/7</div>
              <div className="text-sm text-gray-600 font-medium font-['Open_Sans'] mt-2">Real-time Monitoring</div>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-amber-50/30 relative overflow-hidden">
        {/* Background construction elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-10 w-16 h-16 border border-orange-300 rotate-45"></div>
          <div className="absolute bottom-20 right-10 w-12 h-12 border border-yellow-400 rotate-12"></div>
          <div className="absolute top-1/2 right-1/4 w-8 h-8 border border-amber-400 rotate-45"></div>
          <div className="absolute top-32 right-32 opacity-10">
            <Hammer className="h-8 w-8 text-orange-400 rotate-45" />
          </div>
          <div className="absolute bottom-32 left-32 opacity-10">
            <Wrench className="h-6 w-6 text-yellow-500 rotate-12" />
          </div>
          <div className="absolute top-1/3 left-1/4 opacity-8">
            <Factory className="h-10 w-10 text-amber-400 rotate-12" />
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl font-['Roboto_Slab'] text-orange-800">
              Complete Workforce Management Solutions
            </h2>
            <p className="mt-4 max-w-3xl mx-auto text-xl text-gray-600 font-['Open_Sans']">
              Advanced biometric access control, real-time monitoring, and comprehensive safety management designed specifically for construction sites.
            </p>
          </div>

          <div className="mt-20">
            <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-3">
              {/* Biometric Access Control */}
              <div className="relative group">
                <div className="relative p-6 bg-white rounded-xl shadow-sm border border-orange-200 hover:shadow-xl hover:border-orange-300 transition-all duration-300 transform hover:-translate-y-1">
                  <div>
                    <span className="rounded-xl inline-flex p-3 bg-orange-100 text-orange-600 ring-4 ring-orange-50 group-hover:scale-110 transition-transform duration-200">
                      <HardHat className="h-6 w-6 group-hover:rotate-12 transition-transform duration-300" />
                    </span>
                  </div>
                  <div className="mt-8">
                    <h3 className="text-lg font-medium text-gray-900 font-['Roboto_Slab']">Biometric Access Control</h3>
                    <p className="mt-2 text-sm text-gray-600 font-['Open_Sans']">
                      Fingerprint and facial recognition for secure site access. Know exactly who's on site and when they arrived.
                    </p>
                  </div>
                </div>
              </div>

              {/* Workforce Monitoring */}
              <div className="relative group">
                <div className="relative p-6 bg-white rounded-xl shadow-sm border border-yellow-200 hover:shadow-xl hover:border-yellow-300 transition-all duration-300 transform hover:-translate-y-1">
                  <div>
                    <span className="rounded-xl inline-flex p-3 bg-yellow-100 text-yellow-600 ring-4 ring-yellow-50 group-hover:scale-110 transition-transform duration-200">
                      <Activity className="h-6 w-6 group-hover:animate-pulse" />
                    </span>
                  </div>
                  <div className="mt-8">
                    <h3 className="text-lg font-medium text-gray-900 font-['Roboto_Slab']">Real-time Monitoring</h3>
                    <p className="mt-2 text-sm text-gray-600 font-['Open_Sans']">
                      Live workforce tracking, attendance monitoring, and instant alerts for unauthorized access attempts.
                    </p>
                  </div>
                </div>
              </div>

              {/* Skills & Competency */}
              <div className="relative group">
                <div className="relative p-6 bg-white rounded-xl shadow-sm border border-amber-200 hover:shadow-xl hover:border-amber-300 transition-all duration-300 transform hover:-translate-y-1">
                  <div>
                    <span className="rounded-xl inline-flex p-3 bg-amber-100 text-amber-600 ring-4 ring-amber-50 group-hover:scale-110 transition-transform duration-200">
                      <Users className="h-6 w-6 group-hover:scale-125 transition-transform duration-300" />
                    </span>
                  </div>
                  <div className="mt-8">
                    <h3 className="text-lg font-medium text-gray-900 font-['Roboto_Slab']">Skills & Competency</h3>
                    <p className="mt-2 text-sm text-gray-600 font-['Open_Sans']">
                      Track certifications, training records, and competency levels. Ensure only qualified workers access restricted areas.
                    </p>
                  </div>
                </div>
              </div>

              {/* Event Logging */}
              <div className="relative group">
                <div className="relative p-6 bg-white rounded-xl shadow-sm border border-orange-200 hover:shadow-xl hover:border-orange-300 transition-all duration-300 transform hover:-translate-y-1">
                  <div>
                    <span className="rounded-xl inline-flex p-3 bg-gradient-to-r from-purple-100 to-pink-100 text-purple-700 ring-4 ring-purple-50 group-hover:scale-110 transition-transform duration-200">
                      <Database className="h-6 w-6 group-hover:rotate-180 transition-transform duration-500" />
                    </span>
                  </div>
                  <div className="mt-8">
                    <h3 className="text-lg font-medium text-gray-900 font-['Roboto_Slab']">Event Logging</h3>
                    <p className="mt-2 text-sm text-gray-600 font-['Open_Sans']">
                      Comprehensive audit trails for all site activities. Perfect for compliance reporting and incident investigation.
                    </p>
                  </div>
                </div>
              </div>

              {/* Mobile Integration */}
              <div className="relative group">
                <div className="relative p-6 bg-white rounded-xl shadow-sm border border-yellow-200 hover:shadow-xl hover:border-yellow-300 transition-all duration-300 transform hover:-translate-y-1">
                  <div>
                    <span className="rounded-xl inline-flex p-3 bg-yellow-100 text-yellow-700 ring-4 ring-yellow-50 group-hover:scale-110 transition-transform duration-200">
                      <Smartphone className="h-6 w-6 group-hover:-rotate-12 transition-transform duration-300" />
                    </span>
                  </div>
                  <div className="mt-8">
                    <h3 className="text-lg font-medium text-gray-900 font-['Roboto_Slab']">Mobile Integration</h3>
                    <p className="mt-2 text-sm text-gray-600 font-['Open_Sans']">
                      Site managers can monitor workforce remotely via mobile apps. Real-time notifications and instant reporting.
                    </p>
                  </div>
                </div>
              </div>

              {/* Safety Compliance */}
              <div className="relative group">
                <div className="relative p-6 bg-white rounded-xl shadow-sm border border-amber-200 hover:shadow-xl hover:border-amber-300 transition-all duration-300 transform hover:-translate-y-1">
                  <div>
                    <span className="rounded-xl inline-flex p-3 bg-amber-100 text-amber-700 ring-4 ring-amber-50 group-hover:scale-110 transition-transform duration-200">
                      <FileText className="h-6 w-6 group-hover:scale-110 transition-transform duration-300" />
                    </span>
                  </div>
                  <div className="mt-8">
                    <h3 className="text-lg font-medium text-gray-900 font-['Roboto_Slab']">Safety Compliance</h3>
                    <p className="mt-2 text-sm text-gray-600 font-['Open_Sans']">
                      Automated safety inductions, permit-to-work systems, and compliance reporting for regulatory requirements.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section id="benefits" className="py-20 bg-white relative overflow-hidden">
        {/* Construction background elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-10 left-1/4 w-20 h-20 border border-orange-300 rotate-45 animate-pulse"></div>
          <div className="absolute bottom-10 right-1/4 w-16 h-16 border border-yellow-400 rotate-12 animate-pulse delay-1000"></div>
          <div className="absolute top-1/2 left-10 w-12 h-12 border border-amber-400 rotate-45 animate-pulse delay-500"></div>
          <div className="absolute top-20 right-10 opacity-10">
            <Cog className="h-16 w-16 text-orange-400 rotate-12" />
          </div>
          <div className="absolute bottom-20 left-20 opacity-10">
            <Factory className="h-12 w-12 text-yellow-500 rotate-45" />
          </div>
          <div className="absolute top-1/3 right-1/3 opacity-8">
            <Building2 className="h-14 w-14 text-orange-300 rotate-45" />
          </div>
          <div className="absolute bottom-1/3 left-1/3 opacity-8">
            <HardHat className="h-10 w-10 text-yellow-400 rotate-12" />
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="lg:grid lg:grid-cols-2 lg:gap-8 lg:items-center">
            <div>
              <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl font-['Roboto_Slab'] text-orange-800">
                Proven Results for Construction Sites
              </h2>
              <p className="mt-3 max-w-3xl text-lg text-gray-600 font-['Open_Sans']">
                Join thousands of construction companies who've transformed their workforce management with our proven solutions.
              </p>

              <div className="mt-8 space-y-6">
                <div className="flex">
                  <div className="flex-shrink-0">
                    <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-orange-500 text-white shadow-lg">
                      <CheckCircle className="h-6 w-6" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900 font-['Roboto_Slab']">99.8% Access Accuracy</h3>
                    <p className="mt-2 text-base text-gray-600 font-['Open_Sans']">
                      Biometric technology eliminates buddy punching and ensures only authorized personnel access your site.
                    </p>
                  </div>
                </div>

                <div className="flex">
                  <div className="flex-shrink-0">
                    <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-yellow-500 text-white shadow-lg">
                      <CheckCircle className="h-6 w-6" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900 font-['Roboto_Slab']">60% Reduction in Admin Time</h3>
                    <p className="mt-2 text-base text-gray-600 font-['Open_Sans']">
                      Automated reporting and digital workflows eliminate manual paperwork and streamline operations.
                    </p>
                  </div>
                </div>

                <div className="flex">
                  <div className="flex-shrink-0">
                    <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-amber-500 text-white shadow-lg">
                      <CheckCircle className="h-6 w-6" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900 font-['Roboto_Slab']">Complete Compliance Assurance</h3>
                    <p className="mt-2 text-base text-gray-600 font-['Open_Sans']">
                      Comprehensive audit trails and automated compliance reporting keep you ready for any inspection.
                    </p>
                  </div>
                </div>

                <div className="flex">
                  <div className="flex-shrink-0">
                    <div className="flex items-center justify-center h-10 w-10 rounded-lg bg-orange-600 text-white shadow-lg">
                      <CheckCircle className="h-6 w-6" />
                    </div>
                  </div>
                  <div className="ml-4">
                    <h3 className="text-lg font-medium text-gray-900 font-['Roboto_Slab']">Real-time Site Visibility</h3>
                    <p className="mt-2 text-base text-gray-600 font-['Open_Sans']">
                      Know exactly who's on site, their qualifications, and their current location at any moment.
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-10 lg:mt-0">
              <div className="bg-gradient-to-br from-orange-50 to-yellow-50 rounded-xl p-8 border border-orange-200 shadow-lg">
                <div className="text-center">
                  <Building2 className="mx-auto h-12 w-12 text-orange-600" />
                  <h3 className="mt-4 text-lg font-medium text-gray-900 font-['Roboto_Slab']">Enterprise-Grade Solution</h3>
                  <p className="mt-2 text-sm text-gray-600 font-['Open_Sans']">
                    Scalable workforce management for construction companies of all sizes, from single sites to global operations.
                  </p>
                </div>

                <div className="mt-8 grid grid-cols-2 gap-4">
                  <div className="text-center p-3 bg-white rounded-lg shadow-sm">
                    <div className="text-2xl font-bold text-orange-600 font-['Roboto_Slab']">1000+</div>
                    <div className="text-xs text-gray-600 font-medium font-['Open_Sans']">Sites Managed</div>
                  </div>
                  <div className="text-center p-3 bg-white rounded-lg shadow-sm">
                    <div className="text-2xl font-bold text-yellow-600 font-['Roboto_Slab']">100K+</div>
                    <div className="text-xs text-gray-600 font-medium font-['Open_Sans']">Workers Tracked</div>
                  </div>
                  <div className="text-center p-3 bg-white rounded-lg shadow-sm">
                    <div className="text-2xl font-bold text-amber-600 font-['Roboto_Slab']">99.9%</div>
                    <div className="text-xs text-gray-600 font-medium font-['Open_Sans']">System Uptime</div>
                  </div>
                  <div className="text-center p-3 bg-white rounded-lg shadow-sm">
                    <div className="text-2xl font-bold text-orange-700 font-['Roboto_Slab']">24/7</div>
                    <div className="text-xs text-gray-600 font-medium font-['Open_Sans']">Expert Support</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section id="testimonials" className="py-20 bg-gradient-to-br from-orange-50 to-yellow-50 relative overflow-hidden">
        {/* Laxmi Group inspired background elements */}
        <div className="absolute inset-0 opacity-5">
          <div className="absolute top-20 left-20 w-24 h-24 border-2 border-orange-400 rounded-full"></div>
          <div className="absolute bottom-20 right-20 w-16 h-16 border-2 border-yellow-500 rounded-full"></div>
          <div className="absolute top-1/2 left-1/2 w-32 h-32 border border-amber-400 rounded-full transform -translate-x-1/2 -translate-y-1/2"></div>
          <div className="absolute top-10 right-1/4 opacity-10">
            <Building2 className="h-20 w-20 text-orange-400" />
          </div>
          <div className="absolute bottom-32 left-16 opacity-8">
            <Cog className="h-12 w-12 text-yellow-400 rotate-45" />
          </div>
          <div className="absolute top-40 right-40 opacity-8">
            <Factory className="h-16 w-16 text-amber-400 rotate-12" />
          </div>
          <div className="absolute bottom-40 right-16 opacity-6">
            <Hammer className="h-8 w-8 text-orange-300 rotate-45" />
          </div>
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center">
            <h2 className="text-3xl font-extrabold text-gray-900 sm:text-4xl font-['Roboto_Slab'] text-orange-800">
              Trusted by Construction Leaders
            </h2>
            <p className="mt-4 max-w-2xl mx-auto text-xl text-gray-600 font-['Open_Sans']">
              See how industry professionals are transforming their workforce management with our solutions.
            </p>
          </div>

          <div className="mt-20">
            <div className="grid grid-cols-1 gap-8 lg:grid-cols-3">
              {/* Testimonial 1 */}
              <div className="bg-white rounded-xl shadow-md border border-orange-200 p-6 hover:shadow-lg transition-shadow duration-300">
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-orange-400 fill-current" />
                  ))}
                </div>
                <blockquote className="text-gray-700 mb-4 font-['Open_Sans'] italic">
                  "The biometric access control eliminated all our buddy punching issues. We now have 100% accurate attendance records and our payroll is spot on."
                </blockquote>
                <div className="flex items-center">
                  <img
                    className="h-12 w-12 rounded-full border-2 border-orange-200"
                    src="https://i.imgur.com/3v8sKhM.png"
                    alt="Pepe Construction Manager"
                  />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900 font-['Roboto_Slab']">Michael Chen</p>
                    <p className="text-sm text-gray-600 font-['Open_Sans']">Project Manager, BuildCorp</p>
                  </div>
                </div>
              </div>

              {/* Testimonial 2 */}
              <div className="bg-white rounded-xl shadow-md border border-yellow-200 p-6 hover:shadow-lg transition-shadow duration-300">
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-yellow-500 fill-current" />
                  ))}
                </div>
                <blockquote className="text-gray-700 mb-4 font-['Open_Sans'] italic">
                  "Real-time workforce monitoring gives us complete site visibility. We can track competencies and ensure only qualified workers access restricted areas."
                </blockquote>
                <div className="flex items-center">
                  <img
                    className="h-12 w-12 rounded-full border-2 border-yellow-200"
                    src="https://i.imgur.com/wJCkI2t.png"
                    alt="Pepe Safety Director"
                  />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900 font-['Roboto_Slab']">Sarah Johnson</p>
                    <p className="text-sm text-gray-600 font-['Open_Sans']">Safety Director, MegaConstruct</p>
                  </div>
                </div>
              </div>

              {/* Testimonial 3 */}
              <div className="bg-white rounded-xl shadow-md border border-amber-200 p-6 hover:shadow-lg transition-shadow duration-300">
                <div className="flex items-center mb-4">
                  {[...Array(5)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-amber-500 fill-current" />
                  ))}
                </div>
                <blockquote className="text-gray-700 mb-4 font-['Open_Sans'] italic">
                  "Managing multiple construction sites is now effortless. The comprehensive reporting and audit trails keep us compliant and inspection-ready."
                </blockquote>
                <div className="flex items-center">
                  <img
                    className="h-12 w-12 rounded-full border-2 border-amber-200"
                    src="https://i.imgur.com/QFZ9YGo.png"
                    alt="Pepe Operations Manager"
                  />
                  <div className="ml-3">
                    <p className="text-sm font-medium text-gray-900 font-['Roboto_Slab']">David Rodriguez</p>
                    <p className="text-sm text-gray-600 font-['Open_Sans']">Operations Manager, SkylineBuilders</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Company Logos */}
          <div className="mt-16">
            <p className="text-center text-sm font-semibold uppercase text-gray-400 tracking-wide font-['Open_Sans']">
              Trusted by industry leaders
            </p>
            <div className="mt-6 grid grid-cols-2 gap-8 md:grid-cols-6 lg:grid-cols-5">
              <div className="col-span-1 flex justify-center md:col-span-2 lg:col-span-1">
                <img
                  className="h-12 w-auto grayscale hover:grayscale-0 transition-all duration-300"
                  src="https://upload.wikimedia.org/wikipedia/commons/thumb/5/53/Shutterstock_Logo.svg/320px-Shutterstock_Logo.svg.png"
                  alt="Shutterstock"
                />
              </div>
              <div className="col-span-1 flex justify-center md:col-span-2 lg:col-span-1">
                <img
                  className="h-12 w-auto grayscale hover:grayscale-0 transition-all duration-300"
                  src="https://upload.wikimedia.org/wikipedia/commons/thumb/e/e1/Getty_Images_logo.svg/320px-Getty_Images_logo.svg.png"
                  alt="Getty Images"
                />
              </div>
              <div className="col-span-1 flex justify-center md:col-span-2 lg:col-span-1">
                <img
                  className="h-12 w-auto grayscale hover:grayscale-0 transition-all duration-300"
                  src="https://upload.wikimedia.org/wikipedia/commons/thumb/c/ca/Adobe_Stock_logo.svg/320px-Adobe_Stock_logo.svg.png"
                  alt="Adobe Stock"
                />
              </div>
              <div className="col-span-1 flex justify-center md:col-span-3 lg:col-span-1">
                <img
                  className="h-12 w-auto grayscale hover:grayscale-0 transition-all duration-300"
                  src="https://upload.wikimedia.org/wikipedia/commons/thumb/1/1d/Unsplash_logo.svg/320px-Unsplash_logo.svg.png"
                  alt="Unsplash"
                />
              </div>
              <div className="col-span-2 flex justify-center md:col-span-3 lg:col-span-1">
                <img
                  className="h-12 w-auto grayscale hover:grayscale-0 transition-all duration-300"
                  src="https://upload.wikimedia.org/wikipedia/commons/thumb/4/40/Pexels_logo.svg/320px-Pexels_logo.svg.png"
                  alt="Pexels"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="relative bg-gradient-to-r from-orange-600 via-orange-700 to-yellow-600 overflow-hidden">
        {/* Active Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-full h-full">
            <div className="absolute top-10 left-10 w-16 h-16 border-2 border-white rotate-45 animate-pulse"></div>
            <div className="absolute top-20 right-20 w-12 h-12 border-2 border-yellow-200 rotate-12 animate-pulse delay-1000"></div>
            <div className="absolute bottom-10 left-1/4 w-20 h-20 border-2 border-orange-200 rotate-45 animate-pulse delay-500"></div>
            <div className="absolute bottom-20 right-1/3 w-14 h-14 border-2 border-white rotate-12 animate-pulse delay-1500"></div>
            <div className="absolute top-1/2 left-1/2 w-24 h-24 border-2 border-yellow-300 rotate-45 transform -translate-x-1/2 -translate-y-1/2 animate-pulse delay-700"></div>
          </div>
          {/* Construction tool silhouettes */}
          <div className="absolute top-16 right-16 opacity-20">
            <Hammer className="h-8 w-8 text-white rotate-45" />
          </div>
          <div className="absolute bottom-16 left-16 opacity-20">
            <Wrench className="h-6 w-6 text-yellow-200 rotate-12" />
          </div>
        </div>

        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8 lg:flex lg:items-center lg:justify-between relative z-10">
          <h2 className="text-3xl font-extrabold tracking-tight text-white sm:text-4xl">
            <span className="block font-['Roboto_Slab'] text-4xl sm:text-5xl">Ready to Transform Your Site?</span>
            <span className="block text-orange-100 font-['Open_Sans'] text-xl sm:text-2xl mt-2 font-medium">Start your workforce management journey today</span>
          </h2>
          <div className="mt-8 flex lg:mt-0 lg:flex-shrink-0">
            <div className="inline-flex rounded-lg shadow-lg">
              <button
                onClick={handleGetStarted}
                className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-orange-700 bg-white hover:bg-orange-50 transform hover:scale-105 transition-all duration-200 shadow-lg font-['Open_Sans']"
              >
                Start Free Trial
                <ArrowRight className="ml-2 h-5 w-5" />
              </button>
            </div>
            <div className="ml-3 inline-flex rounded-lg shadow-lg">
              <button className="inline-flex items-center justify-center px-6 py-3 border border-white text-base font-medium rounded-lg text-white bg-orange-500 hover:bg-orange-400 transform hover:scale-105 transition-all duration-200 font-['Open_Sans']">
                Schedule Demo
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer id="contact" className="bg-gray-900">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:py-16 lg:px-8">
          <div className="xl:grid xl:grid-cols-3 xl:gap-8">
            <div className="space-y-8 xl:col-span-1">
              <div className="flex items-center">
                <HardHat className="h-8 w-8 text-orange-400" />
                <div className="ml-3">
                  <span className="text-xl font-bold text-white font-['Roboto_Slab']">Workforce</span>
                  <div className="text-xs text-orange-400 font-['Open_Sans']">Construction Management</div>
                </div>
              </div>
              <p className="text-gray-400 text-base font-['Open_Sans']">
                Advanced workforce management solutions for the modern construction industry. Secure, reliable, and built for scale.
              </p>
              <div className="flex space-x-6">
                <a href="#" className="text-gray-400 hover:text-gray-300">
                  <span className="sr-only">LinkedIn</span>
                  <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                  </svg>
                </a>
                <a href="#" className="text-gray-400 hover:text-gray-300">
                  <span className="sr-only">Twitter</span>
                  <svg className="h-6 w-6" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M8.29 20.251c7.547 0 11.675-6.253 11.675-11.675 0-.178 0-.355-.012-.53A8.348 8.348 0 0022 5.92a8.19 8.19 0 01-2.357.646 4.118 4.118 0 001.804-2.27 8.224 8.224 0 01-2.605.996 4.107 4.107 0 00-6.993 3.743 11.65 11.65 0 01-8.457-4.287 4.106 4.106 0 001.27 5.477A4.072 4.072 0 012.8 9.713v.052a4.105 4.105 0 003.292 4.022 4.095 4.095 0 01-1.853.07 4.108 4.108 0 003.834 2.85A8.233 8.233 0 012 18.407a11.616 11.616 0 006.29 1.84"/>
                  </svg>
                </a>
              </div>
            </div>
            <div className="mt-12 grid grid-cols-2 gap-8 xl:mt-0 xl:col-span-2">
              <div className="md:grid md:grid-cols-2 md:gap-8">
                <div>
                  <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">Product</h3>
                  <ul className="mt-4 space-y-4">
                    <li><a href="#features" className="text-base text-gray-300 hover:text-white">Features</a></li>
                    <li><a href="#" className="text-base text-gray-300 hover:text-white">Pricing</a></li>
                    <li><a href="#" className="text-base text-gray-300 hover:text-white">Mobile App</a></li>
                    <li><a href="#" className="text-base text-gray-300 hover:text-white">Integrations</a></li>
                  </ul>
                </div>
                <div className="mt-12 md:mt-0">
                  <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">Support</h3>
                  <ul className="mt-4 space-y-4">
                    <li><a href="#" className="text-base text-gray-300 hover:text-white">Documentation</a></li>
                    <li><a href="#" className="text-base text-gray-300 hover:text-white">Help Center</a></li>
                    <li><a href="#" className="text-base text-gray-300 hover:text-white">Contact Us</a></li>
                    <li><a href="#" className="text-base text-gray-300 hover:text-white">Status</a></li>
                  </ul>
                </div>
              </div>
              <div className="md:grid md:grid-cols-2 md:gap-8">
                <div>
                  <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">Company</h3>
                  <ul className="mt-4 space-y-4">
                    <li><a href="#" className="text-base text-gray-300 hover:text-white">About</a></li>
                    <li><a href="#" className="text-base text-gray-300 hover:text-white">Blog</a></li>
                    <li><a href="#" className="text-base text-gray-300 hover:text-white">Careers</a></li>
                    <li><a href="#" className="text-base text-gray-300 hover:text-white">Partners</a></li>
                  </ul>
                </div>
                <div className="mt-12 md:mt-0">
                  <h3 className="text-sm font-semibold text-gray-400 tracking-wider uppercase">Legal</h3>
                  <ul className="mt-4 space-y-4">
                    <li><a href="#" className="text-base text-gray-300 hover:text-white">Privacy</a></li>
                    <li><a href="#" className="text-base text-gray-300 hover:text-white">Terms</a></li>
                    <li><a href="#" className="text-base text-gray-300 hover:text-white">Security</a></li>
                    <li><a href="#" className="text-base text-gray-300 hover:text-white">Compliance</a></li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
          <div className="mt-12 border-t border-gray-700 pt-8">
            <p className="text-base text-gray-400 xl:text-center">
              &copy; 2024 Workforce Management Systems. All rights reserved.
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default LandingPage;
