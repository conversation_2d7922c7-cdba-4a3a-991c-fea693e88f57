.u-section-1 .u-sheet-1 {
  min-height: 604px;
}

.u-section-1 .u-list-1 {
  width: 735px;
  margin: 85px auto 60px 0;
}

.u-section-1 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 34px;
  grid-gap: 10px;
}

.u-section-1 .u-list-item-1 {
  transition-duration: 0.5s;
  --radius: 10px;
}

.u-section-1 .u-container-layout-1 {
  padding: 0;
}

.u-section-1 .u-text-1 {
  margin: 0 auto 0 11px;
}

.u-section-1 .u-list-item-2 {
  transition-duration: 0.5s;
  --radius: 10px;
}

.u-section-1 .u-container-layout-2 {
  padding: 0;
}

.u-section-1 .u-text-2 {
  margin: 0 auto 0 11px;
}

.u-section-1 .u-list-item-3 {
  transition-duration: 0.5s;
  --radius: 10px;
}

.u-section-1 .u-container-layout-3 {
  padding: 0;
}

.u-section-1 .u-text-3 {
  margin: 0 auto 0 11px;
}

@media (max-width: 1199px) {
  .u-section-1 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
  }
}

@media (max-width: 991px) {
  .u-section-1 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 113px;
  }
}

@media (max-width: 767px) {
  .u-section-1 .u-list-1 {
    width: 540px;
  }

  .u-section-1 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
}

@media (max-width: 575px) {
  .u-section-1 .u-list-1 {
    margin-right: initial;
    margin-left: initial;
    width: auto;
  }

  .u-section-1 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}

.u-section-1 .u-list-item-1,
.u-section-1 .u-list-item-1:before,
.u-section-1 .u-list-item-1 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-1 .u-list-item-1.u-list-item-1.u-list-item-1:hover:before {
  background-color: #e8c7e5 !important;
}

.u-section-1 .u-list-item-1.u-list-item-1.u-list-item-1.hover:before {
  background-color: #e8c7e5 !important;
}

.u-section-1 .u-list-item-2,
.u-section-1 .u-list-item-2:before,
.u-section-1 .u-list-item-2 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-1 .u-list-item-2.u-list-item-2.u-list-item-2:hover:before {
  background-color: #e8c7e5 !important;
}

.u-section-1 .u-list-item-2.u-list-item-2.u-list-item-2.hover:before {
  background-color: #e8c7e5 !important;
}

.u-section-1 .u-list-item-3,
.u-section-1 .u-list-item-3:before,
.u-section-1 .u-list-item-3 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-1 .u-list-item-3.u-list-item-3.u-list-item-3:hover:before {
  background-color: #e8c7e5 !important;
}

.u-section-1 .u-list-item-3.u-list-item-3.u-list-item-3.hover:before {
  background-color: #e8c7e5 !important;
}.u-section-2 .u-sheet-1 {
  min-height: 440px;
}

.u-section-2 .u-shape-1 {
  width: 29px;
  height: 29px;
  margin: 20px auto 0 1px;
}

.u-section-2 .u-shape-2 {
  width: 29px;
  height: 29px;
  transform-origin: center center;
  transform: scaleX(-1);
  margin: -29px 0 0 auto;
}

.u-section-2 .u-text-1 {
  font-size: 2.25rem;
  width: 692px;
  font-weight: 700;
  margin: 92px auto 0;
}

.u-section-2 .u-text-2 {
  font-weight: 500;
  font-size: 1.5rem;
  width: 668px;
  margin: 40px auto 0;
}

.u-section-2 .u-shape-3 {
  width: 29px;
  height: 29px;
  transform-origin: center center;
  transform: scaleY(-1);
  margin: 101px auto 0 0;
}

.u-section-2 .u-shape-4 {
  width: 29px;
  height: 29px;
  transform-origin: center center;
  transform: scaleX(-1) scaleY(-1);
  margin: -29px 0 21px auto;
}

.u-section-2 .embed-responsive-1 {
  position: absolute;
  width: 100%;
  left: 0%;
  height: 514%;
  top: -207%;
}

@media (max-width: 1199px) {
  .u-section-2 .u-sheet-1 {
    min-height: 589px;
  }

  .u-section-2 .u-shape-1 {
    margin-top: 49px;
    margin-left: 0;
  }

  .u-section-2 .u-text-1 {
    margin-top: 138px;
  }

  .u-section-2 .u-shape-3 {
    margin-top: 166px;
  }

  .u-section-2 .u-shape-4 {
    margin-bottom: 30px;
  }

  .u-section-2 .embed-responsive-1 {
    width: 178%;
    left: -39%;
    height: 178%;
    top: -39%;
  }
}

@media (max-width: 991px) {
  .u-section-2 .u-text-1 {
    width: auto;
    margin-left: 44px;
    margin-right: 44px;
  }
}

@media (max-width: 767px) {
  .u-section-2 .u-sheet-1 {
    min-height: 575px;
  }

  .u-section-2 .u-shape-1 {
    margin-top: 20px;
  }

  .u-section-2 .u-text-1 {
    margin-top: 159px;
    margin-right: 3px;
    margin-left: 0;
  }

  .u-section-2 .u-text-2 {
    width: auto;
    margin-top: 20px;
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-2 .u-shape-3 {
    margin-top: 190px;
  }

  .u-section-2 .u-shape-4 {
    margin-bottom: 20px;
  }
}

@media (max-width: 575px) {
  .u-section-2 .u-sheet-1 {
    min-height: 340px;
  }

  .u-section-2 .u-text-1 {
    margin-top: 13px;
    margin-left: 2px;
    margin-right: 2px;
  }

  .u-section-2 .u-text-2 {
    font-size: 1rem;
    margin-top: 21px;
    margin-left: 1px;
    margin-right: 1px;
  }

  .u-section-2 .u-shape-3 {
    margin-top: 30px;
  }
} .u-section-3 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-3 .u-sheet-1 {
  min-height: 406px;
}

.u-section-3 .u-text-1 {
  font-weight: 300;
  margin: 20px 0 0;
}

.u-section-3 .u-text-2 {
  margin: 20px auto 20px 0;
}

@media (max-width: 1199px) {
  .u-section-3 .u-sheet-1 {
    min-height: 361px;
  }
}

@media (max-width: 991px) {
  .u-section-3 .u-sheet-1 {
    min-height: 460px;
  }
}

@media (max-width: 767px) {
  .u-section-3 .u-sheet-1 {
    min-height: 482px;
  }

  .u-section-3 .u-text-2 {
    margin-bottom: -418px;
  }
}

@media (max-width: 575px) {
  .u-section-3 .u-sheet-1 {
    min-height: 515px;
  }

  .u-section-3 .u-text-2 {
    margin-bottom: -557px;
  }
} .u-section-4 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-4 .u-sheet-1 {
  min-height: 416px;
}

.u-section-4 .u-text-1 {
  font-weight: 300;
  margin: 20px 845px 0 0;
}

.u-section-4 .u-list-1 {
  margin-top: 20px;
  margin-bottom: 0;
}

.u-section-4 .u-repeater-1 {
  grid-template-columns: repeat(4, calc(25% - 15px));
  min-height: 117px;
  grid-auto-columns: calc(25% - 15px);
  grid-gap: 20px;
}

.u-section-4 .u-list-item-1 {
  --radius: 20px;
}

.u-section-4 .u-container-layout-1 {
  padding: 10px;
}

.u-section-4 .u-group-1 {
  --radius: 10px;
  width: 80px;
  min-height: 80px;
  height: auto;
  margin: 10px auto 0 11px;
}

.u-section-4 .u-container-layout-2 {
  padding-bottom: 0;
}

.u-section-4 .u-text-2 {
  font-size: 1.875rem;
  font-weight: 500;
  margin: 22px 3px 0;
}

.u-section-4 .u-text-3 {
  font-size: 1.125rem;
  font-weight: 500;
  margin: -69px 0 0 100px;
}

.u-section-4 .u-list-item-2 {
  --radius: 20px;
}

.u-section-4 .u-container-layout-3 {
  padding: 10px;
}

.u-section-4 .u-group-2 {
  --radius: 10px;
  width: 80px;
  min-height: 80px;
  height: auto;
  margin: 10px auto 0 11px;
}

.u-section-4 .u-container-layout-4 {
  padding-bottom: 0;
}

.u-section-4 .u-text-4 {
  font-size: 1.875rem;
  font-weight: 500;
  margin: 22px 3px 0;
}

.u-section-4 .u-text-5 {
  font-weight: 500;
  font-size: 1.125rem;
  margin: -69px 0 0 100px;
}

.u-section-4 .u-list-item-3 {
  --radius: 20px;
}

.u-section-4 .u-container-layout-5 {
  padding: 10px;
}

.u-section-4 .u-group-3 {
  --radius: 10px;
  width: 80px;
  min-height: 80px;
  height: auto;
  margin: 10px auto 0 11px;
}

.u-section-4 .u-container-layout-6 {
  padding-bottom: 0;
}

.u-section-4 .u-text-6 {
  font-size: 1.875rem;
  font-weight: 500;
  margin: 22px 3px 0;
}

.u-section-4 .u-text-7 {
  font-weight: 500;
  font-size: 1.125rem;
  margin: -69px 0 0 100px;
}

.u-section-4 .u-list-item-4 {
  --radius: 20px;
}

.u-section-4 .u-container-layout-7 {
  padding: 10px;
}

.u-section-4 .u-group-4 {
  --radius: 10px;
  width: 80px;
  min-height: 80px;
  height: auto;
  margin: 10px auto 0 11px;
}

.u-section-4 .u-container-layout-8 {
  padding-bottom: 0;
}

.u-section-4 .u-text-8 {
  font-size: 1.875rem;
  font-weight: 500;
  margin: 22px 3px 0;
}

.u-section-4 .u-text-9 {
  font-weight: 500;
  font-size: 1.125rem;
  margin: -69px 0 0 100px;
}

.u-section-4 .u-list-2 {
  margin-top: 30px;
  margin-bottom: -17px;
  width: 100%;
}

.u-section-4 .u-repeater-2 {
  grid-auto-columns: calc(16.6667% - 16.6667px);
  grid-template-columns: repeat(6, calc(16.6667% - 16.6667px));
  min-height: 180px;
  grid-gap: 20px;
}

.u-section-4 .u-list-item-5 {
  background-repeat: no-repeat;
  background-size: cover;
  --radius: 10px;
}

.u-section-4 .u-image-1 {
  background-image: url("../images/site11.webp");
}

.u-section-4 .u-container-layout-9 {
  padding: 10px;
}

.u-section-4 .u-list-item-6 {
  background-repeat: no-repeat;
  background-size: cover;
  --radius: 10px;
}

.u-section-4 .u-image-2 {
  background-image: url("../images/laxmi1-1.webp");
}

.u-section-4 .u-container-layout-10 {
  padding: 10px;
}

.u-section-4 .u-list-item-7 {
  background-repeat: no-repeat;
  background-size: cover;
  --radius: 10px;
}

.u-section-4 .u-image-3 {
  background-image: url("../images/site10.webp");
}

.u-section-4 .u-container-layout-11 {
  padding: 10px;
}

.u-section-4 .u-list-item-8 {
  background-repeat: no-repeat;
  background-size: cover;
  --radius: 10px;
}

.u-section-4 .u-image-4 {
  background-image: url("../images/site1.webp");
}

.u-section-4 .u-container-layout-12 {
  padding: 10px;
}

.u-section-4 .u-list-item-9 {
  background-repeat: no-repeat;
  background-size: cover;
  --radius: 10px;
}

.u-section-4 .u-image-5 {
  background-image: url("../images/site9.webp");
}

.u-section-4 .u-container-layout-13 {
  padding: 10px;
}

.u-section-4 .u-list-item-10 {
  background-repeat: no-repeat;
  background-size: cover;
  --radius: 10px;
}

.u-section-4 .u-image-6 {
  background-image: url("../images/qc2.webp");
}

.u-section-4 .u-container-layout-14 {
  padding: 10px;
}

.u-section-4 .u-gallery-nav-1 {
  position: absolute;
  left: 10px;
  width: 40px;
  height: 40px;
}

.u-section-4 .u-gallery-nav-2 {
  position: absolute;
  right: 10px;
  width: 40px;
  height: 40px;
}

@media (max-width: 1199px) {
  .u-section-4 .u-sheet-1 {
    min-height: 486px;
  }

  .u-section-4 .u-text-1 {
    margin-right: 645px;
  }

  .u-section-4 .u-repeater-1 {
    min-height: 100px;
  }

  .u-section-4 .u-group-1 {
    height: auto;
  }

  .u-section-4 .u-text-3 {
    margin-top: 20px;
    margin-left: 0;
  }

  .u-section-4 .u-group-2 {
    height: auto;
  }

  .u-section-4 .u-text-5 {
    margin-top: 20px;
    margin-left: 0;
  }

  .u-section-4 .u-group-3 {
    height: auto;
  }

  .u-section-4 .u-text-7 {
    margin-top: 20px;
    margin-left: 0;
  }

  .u-section-4 .u-group-4 {
    height: auto;
  }

  .u-section-4 .u-text-9 {
    margin-top: 20px;
    margin-left: 0;
  }

  .u-section-4 .u-repeater-2 {
    grid-auto-columns: calc(16.666666666666668% - 16.6667px);
    grid-template-columns: repeat(6, calc(16.666666666666668% - 16.6667px));
  }

  .u-section-4 .u-list-item-5 {
    background-position: 50% 50%;
  }

  .u-section-4 .u-list-item-6 {
    background-position: 50% 50%;
  }

  .u-section-4 .u-list-item-7 {
    background-position: 50% 50%;
  }

  .u-section-4 .u-list-item-8 {
    background-position: 50% 50%;
  }

  .u-section-4 .u-list-item-9 {
    background-position: 50% 50%;
  }

  .u-section-4 .u-list-item-10 {
    background-position: 50% 50%;
  }
}

@media (max-width: 991px) {
  .u-section-4 .u-sheet-1 {
    min-height: 612px;
  }

  .u-section-4 .u-text-1 {
    margin-right: 425px;
  }

  .u-section-4 .u-repeater-1 {
    grid-template-columns: repeat(2, calc(50% - 10px));
    grid-auto-columns: calc(50% - 10px);
    min-height: 334px;
  }

  .u-section-4 .u-repeater-2 {
    grid-auto-columns: calc(33.333333333333336% - 13.333359999999999px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 13.333359999999999px));
  }
}

@media (max-width: 767px) {
  .u-section-4 .u-sheet-1 {
    min-height: 416px;
  }

  .u-section-4 .u-text-1 {
    margin-right: 185px;
  }

  .u-section-4 .u-repeater-1 {
    grid-template-columns: 100%;
    grid-auto-columns: calc(100% - 0px);
  }

  .u-section-4 .u-text-3 {
    font-size: 1rem;
  }

  .u-section-4 .u-text-4 {
    font-size: 2.5rem;
  }

  .u-section-4 .u-text-5 {
    font-size: 1rem;
  }

  .u-section-4 .u-text-6 {
    font-size: 2.5rem;
  }

  .u-section-4 .u-text-7 {
    font-size: 1rem;
  }

  .u-section-4 .u-text-8 {
    font-size: 2.5rem;
  }

  .u-section-4 .u-text-9 {
    font-size: 1rem;
  }

  .u-section-4 .u-repeater-2 {
    grid-auto-columns: calc(50% - 10.00002px);
    grid-template-columns: repeat(2, calc(50% - 10.00002px));
  }
}

@media (max-width: 575px) {
  .u-section-4 .u-text-1 {
    margin-right: 0;
  }

  .u-section-4 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-4 .u-text-4 {
    font-size: 2rem;
  }

  .u-section-4 .u-text-6 {
    font-size: 2rem;
  }

  .u-section-4 .u-text-8 {
    font-size: 2rem;
  }

  .u-section-4 .u-repeater-2 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }
} .u-section-5 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-5 .u-sheet-1 {
  min-height: 718px;
}

.u-section-5 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: -56px;
}

.u-section-5 .u-layout-cell-1 {
  min-height: 224px;
}

.u-section-5 .u-container-layout-1 {
  padding: 0;
}

.u-section-5 .u-text-1 {
  font-weight: 300;
  margin: 0 20px 0 0;
}

.u-section-5 .u-layout-cell-2 {
  min-height: 224px;
}

.u-section-5 .u-container-layout-2 {
  padding: 30px;
}

.u-section-5 .u-text-2 {
  font-weight: 400;
  margin: 0 20px 0 0;
}

.u-section-5 .u-list-1 {
  width: 303px;
  margin: 20px 20px 0 0;
}

.u-section-5 .u-repeater-1 {
  grid-auto-columns: calc(50% - 5px);
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 120px;
  grid-gap: 10px;
}

.u-section-5 .u-container-layout-3 {
  padding: 0;
}

.u-section-5 .u-text-3 {
  font-weight: 400;
  font-size: 1rem;
  margin: 0 auto 0 0;
}

.u-section-5 .u-container-layout-4 {
  padding: 0;
}

.u-section-5 .u-text-4 {
  font-weight: 400;
  font-size: 1rem;
  margin: 0 auto 0 0;
}

.u-section-5 .u-layout-cell-3 {
  min-height: 448px;
}

.u-section-5 .u-container-layout-5 {
  padding: 30px;
}

.u-section-5 .u-image-1 {
  width: 570px;
  height: 394px;
  --radius: 20px;
  margin: 0 auto;
}

.u-section-5 .u-layout-cell-4 {
  min-height: 672px;
}

.u-section-5 .u-container-layout-6 {
  padding: 30px;
}

.u-section-5 .u-group-1 {
  --radius: 20px;
  min-height: 560px;
  height: auto;
  margin: 0 auto 0 20px;
}

.u-section-5 .u-container-layout-7 {
  padding: 20px;
}

.u-section-5 .u-image-2 {
  height: 356px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 20px;
  filter: brightness(0.75);
}

.u-section-5 .u-text-5 {
  font-weight: 400;
  margin-top: 30px;
  margin-bottom: 0;
}

.u-section-5 .u-btn-1 {
  margin: 20px auto 0 0;
  padding: 0;
}

@media (max-width: 1199px) {
  .u-section-5 .u-sheet-1 {
    min-height: 732px;
  }

  .u-section-5 .u-layout-wrap-1 {
    margin-bottom: -131px;
    position: relative;
  }

  .u-section-5 .u-layout-cell-1 {
    min-height: 187px;
  }

  .u-section-5 .u-text-1 {
    margin-right: 0;
  }

  .u-section-5 .u-layout-cell-2 {
    min-height: 186px;
  }

  .u-section-5 .u-text-2 {
    margin-right: 0;
  }

  .u-section-5 .u-list-1 {
    width: 300px;
    margin-right: 0;
  }

  .u-section-5 .u-repeater-1 {
    min-height: 119px;
  }

  .u-section-5 .u-layout-cell-3 {
    min-height: 374px;
  }

  .u-section-5 .u-image-1 {
    width: 557px;
    height: 385px;
  }

  .u-section-5 .u-layout-cell-4 {
    min-height: 560px;
  }

  .u-section-5 .u-group-1 {
    margin-right: initial;
    margin-left: initial;
    height: auto;
  }

  .u-section-5 .u-image-2 {
    height: 292px;
    transition-duration: 0.5s;
  }
}

@media (max-width: 991px) {
  .u-section-5 .u-sheet-1 {
    min-height: 1051px;
  }

  .u-section-5 .u-layout-wrap-1 {
    margin-bottom: 20px;
  }

  .u-section-5 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-5 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-5 .u-layout-cell-3 {
    min-height: 450px;
  }

  .u-section-5 .u-layout-cell-4 {
    min-height: 100px;
  }

  .u-section-5 .u-image-2 {
    height: 470px;
  }
}

@media (max-width: 767px) {
  .u-section-5 .u-sheet-1 {
    min-height: 830px;
  }

  .u-section-5 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-5 .u-layout-cell-3 {
    min-height: 312px;
  }

  .u-section-5 .u-container-layout-5 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-5 .u-image-1 {
    width: 520px;
    height: 359px;
  }

  .u-section-5 .u-layout-cell-4 {
    min-height: 578px;
  }

  .u-section-5 .u-container-layout-6 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-5 .u-group-1 {
    min-height: 518px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-5 .u-container-layout-7 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-5 .u-image-2 {
    height: 330px;
  }
}

@media (max-width: 575px) {
  .u-section-5 .u-sheet-1 {
    min-height: 772px;
  }

  .u-section-5 .u-layout-cell-3 {
    min-height: 196px;
  }

  .u-section-5 .u-image-1 {
    width: 320px;
    height: 221px;
  }

  .u-section-5 .u-layout-cell-4 {
    min-height: 100px;
  }

  .u-section-5 .u-group-1 {
    min-height: 387px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-5 .u-container-layout-7 {
    padding-top: 10px;
  }

  .u-section-5 .u-image-2 {
    height: 203px;
  }
} .u-section-6 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-6 .u-sheet-1 {
  min-height: 708px;
}

.u-section-6 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: 20px;
}

.u-section-6 .u-layout-cell-1 {
  min-height: 233px;
}

.u-section-6 .u-container-layout-1 {
  padding: 30px;
}

.u-section-6 .u-text-1 {
  font-weight: 300;
  margin: 0 268px 0 0;
}

.u-section-6 .u-text-2 {
  font-size: 1.875rem;
  font-weight: 600;
  margin: 11px 0 0;
}

.u-section-6 .u-btn-1 {
  margin: 20px 0 0;
  padding: 0;
}

.u-section-6 .u-layout-cell-2 {
  min-height: 233px;
}

.u-section-6 .u-container-layout-2 {
  padding: 30px;
}

.u-section-6 .u-text-3 {
  margin: 0;
}

.u-section-6 .u-layout-cell-3 {
  min-height: 435px;
}

.u-section-6 .u-container-layout-3 {
  padding: 0 30px;
}

.u-section-6 .u-text-4 {
  font-weight: 300;
  margin: 0 auto 0 0;
}

.u-section-6 .u-image-1 {
  min-height: 375px;
  height: auto;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: url("../images/laxmi4.webp");
  background-size: cover;
  --radius: 20px;
  margin: 20px 0 0;
}

.u-section-6 .u-container-layout-4 {
  padding: 30px;
}

.u-section-6 .u-layout-cell-4 {
  min-height: 435px;
}

.u-section-6 .u-container-layout-5 {
  padding: 0 30px;
}

.u-section-6 .u-list-1 {
  margin: 49px 0 0;
}

.u-section-6 .u-repeater-1 {
  grid-auto-columns: calc(50% - 5px);
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 375px;
  grid-gap: 10px;
}

.u-section-6 .u-list-item-1 {
  --radius: 20px;
}

.u-section-6 .u-container-layout-6 {
  padding: 10px;
}

.u-section-6 .u-text-5 {
  font-weight: 700;
  font-size: 3rem;
  margin: 20px auto 0 0;
}

.u-section-6 .u-text-6 {
  font-weight: 500;
  font-size: 1.125rem;
  margin: 10px 10px 0;
}

.u-section-6 .u-list-item-2 {
  --radius: 20px;
}

.u-section-6 .u-container-layout-7 {
  padding: 10px;
}

.u-section-6 .u-text-7 {
  font-weight: 700;
  font-size: 3rem;
  margin: 20px auto 0 0;
}

.u-section-6 .u-text-8 {
  font-weight: 500;
  font-size: 1.125rem;
  margin: 10px 10px 0;
}

.u-section-6 .u-list-item-3 {
  --radius: 20px;
}

.u-section-6 .u-container-layout-8 {
  padding: 10px;
}

.u-section-6 .u-text-9 {
  font-weight: 700;
  font-size: 3rem;
  margin: 20px auto 0 0;
}

.u-section-6 .u-text-10 {
  font-weight: 500;
  font-size: 1.125rem;
  margin: 10px 10px 0;
}

.u-section-6 .u-list-item-4 {
  --radius: 20px;
}

.u-section-6 .u-container-layout-9 {
  padding: 10px;
}

.u-section-6 .u-text-11 {
  font-weight: 700;
  font-size: 3rem;
  margin: 20px auto 0 0;
}

.u-section-6 .u-text-12 {
  font-weight: 500;
  font-size: 1.125rem;
  margin: 10px 10px 0;
}

@media (max-width: 1199px) {
  .u-section-6 .u-sheet-1 {
    min-height: 597px;
  }

  .u-section-6 .u-layout-wrap-1 {
    position: relative;
  }

  .u-section-6 .u-layout-cell-1 {
    min-height: 194px;
  }

  .u-section-6 .u-text-1 {
    margin-right: 168px;
  }

  .u-section-6 .u-layout-cell-2 {
    min-height: 194px;
  }

  .u-section-6 .u-layout-cell-3 {
    min-height: 363px;
  }

  .u-section-6 .u-image-1 {
    margin-right: initial;
    margin-left: initial;
    height: auto;
  }

  .u-section-6 .u-layout-cell-4 {
    min-height: 424px;
  }

  .u-section-6 .u-list-1 {
    margin-top: 0;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-6 .u-repeater-1 {
    min-height: 340px;
  }

  .u-section-6 .u-text-6 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-6 .u-text-8 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-6 .u-text-10 {
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-6 .u-text-12 {
    margin-left: 0;
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-6 .u-sheet-1 {
    min-height: 240px;
  }

  .u-section-6 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-6 .u-text-1 {
    margin-right: 58px;
  }

  .u-section-6 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-6 .u-layout-cell-3 {
    min-height: 340px;
  }

  .u-section-6 .u-image-1 {
    min-height: 234px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-6 .u-layout-cell-4 {
    min-height: 100px;
  }

  .u-section-6 .u-text-5 {
    font-size: 2.25rem;
  }

  .u-section-6 .u-text-6 {
    font-size: 0.875rem;
  }

  .u-section-6 .u-text-7 {
    font-size: 2.25rem;
  }

  .u-section-6 .u-text-8 {
    font-size: 0.875rem;
  }

  .u-section-6 .u-text-9 {
    font-size: 2.25rem;
  }

  .u-section-6 .u-text-10 {
    font-size: 0.875rem;
  }

  .u-section-6 .u-text-11 {
    font-size: 2.25rem;
  }

  .u-section-6 .u-text-12 {
    font-size: 0.875rem;
  }
}

@media (max-width: 767px) {
  .u-section-6 .u-sheet-1 {
    min-height: 440px;
  }

  .u-section-6 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-6 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-6 .u-layout-cell-3 {
    min-height: 100px;
  }

  .u-section-6 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-6 .u-container-layout-4 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-6 .u-layout-cell-4 {
    min-height: 254px;
  }

  .u-section-6 .u-container-layout-5 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-6 .u-list-1 {
    margin-top: 20px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-6 .u-repeater-1 {
    min-height: 254px;
  }

  .u-section-6 .u-text-5 {
    font-size: 1.875rem;
  }

  .u-section-6 .u-text-7 {
    font-size: 1.875rem;
  }

  .u-section-6 .u-text-9 {
    font-size: 1.875rem;
  }

  .u-section-6 .u-text-11 {
    font-size: 1.875rem;
  }
}

@media (max-width: 575px) {
  .u-section-6 .u-text-1 {
    margin-right: 0;
  }

  .u-section-6 .u-layout-cell-4 {
    min-height: 100px;
  }

  .u-section-6 .u-text-5 {
    font-size: 1.5rem;
  }

  .u-section-6 .u-text-7 {
    font-size: 1.5rem;
  }

  .u-section-6 .u-text-9 {
    font-size: 1.5rem;
  }

  .u-section-6 .u-text-11 {
    font-size: 1.5rem;
  }
} .u-section-7 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-7 .u-sheet-1 {
  min-height: 1145px;
}

.u-section-7 .u-text-1 {
  font-weight: 300;
  margin: 20px 967px 0 30px;
}

.u-section-7 .u-text-2 {
  font-weight: 400;
  font-size: 2.25rem;
  margin: 20px auto 0 30px;
}

.u-section-7 .u-btn-1 {
  margin: -23px 0 0 auto;
  padding: 0;
}

.u-section-7 .u-text-3 {
  margin: 20px auto 0 30px;
}

.u-section-7 .u-list-1 {
  width: 1160px;
  margin: 20px auto 5px;
}

.u-section-7 .u-repeater-1 {
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 943px;
  grid-auto-columns: calc(50% - 5px);
  grid-gap: 10px;
}

.u-section-7 .u-container-layout-1 {
  padding: 8px 10px;
}

.u-section-7 .u-image-1 {
  height: 352px;
  --radius: 5px;
  margin-top: 2px;
  margin-bottom: 0;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-7 .u-text-4 {
  font-weight: 400;
  transition-duration: 0.5s;
  margin: 20px 40px 0 0;
}

.u-section-7 .u-text-5 {
  margin: 21px auto 0 0;
}

.u-section-7 .u-container-layout-2 {
  padding: 8px 10px;
}

.u-section-7 .u-image-2 {
  height: 352px;
  --radius: 5px;
  margin-top: 2px;
  margin-bottom: 0;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-7 .u-text-6 {
  font-weight: 400;
  transition-duration: 0.5s;
  margin: 20px 40px 0 0;
}

.u-section-7 .u-text-7 {
  margin: 21px auto 0 0;
}

.u-section-7 .u-container-layout-3 {
  padding: 8px 10px;
}

.u-section-7 .u-image-3 {
  height: 352px;
  --radius: 5px;
  margin-top: 2px;
  margin-bottom: 0;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-7 .u-text-8 {
  font-weight: 400;
  transition-duration: 0.5s;
  margin: 20px 40px 0 0;
}

.u-section-7 .u-text-9 {
  margin: 21px auto 0 0;
}

.u-section-7 .u-container-layout-4 {
  padding: 8px 10px;
}

.u-section-7 .u-image-4 {
  height: 352px;
  --radius: 5px;
  margin-top: 2px;
  margin-bottom: 0;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-7 .u-text-10 {
  font-weight: 400;
  transition-duration: 0.5s;
  margin: 20px 40px 0 0;
}

.u-section-7 .u-text-11 {
  margin: 21px auto 0 0;
}

@media (max-width: 1199px) {
  .u-section-7 .u-sheet-1 {
    min-height: 1039px;
  }

  .u-section-7 .u-text-1 {
    margin-right: 797px;
    margin-left: 0;
  }

  .u-section-7 .u-text-2 {
    margin-left: 0;
  }

  .u-section-7 .u-list-1 {
    width: 1000px;
    margin-bottom: -101px;
  }

  .u-section-7 .u-repeater-1 {
    min-height: 813px;
  }

  .u-section-7 .u-image-1 {
    height: 302px;
  }

  .u-section-7 .u-text-4 {
    margin-right: 0;
  }

  .u-section-7 .u-image-2 {
    height: 302px;
  }

  .u-section-7 .u-text-6 {
    margin-right: 0;
  }

  .u-section-7 .u-image-3 {
    height: 302px;
  }

  .u-section-7 .u-text-8 {
    margin-right: 0;
  }

  .u-section-7 .u-image-4 {
    height: 302px;
  }

  .u-section-7 .u-text-10 {
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-7 .u-sheet-1 {
    min-height: 1160px;
  }

  .u-section-7 .u-text-1 {
    margin-right: 577px;
  }

  .u-section-7 .u-text-3 {
    margin-left: 0;
  }

  .u-section-7 .u-list-1 {
    width: 780px;
    margin-bottom: 20px;
  }

  .u-section-7 .u-repeater-1 {
    grid-template-columns: 100%;
    grid-auto-columns: calc(100% - 0px);
  }

  .u-section-7 .u-image-1 {
    height: 478px;
  }

  .u-section-7 .u-image-2 {
    height: 478px;
  }

  .u-section-7 .u-image-3 {
    height: 478px;
  }

  .u-section-7 .u-image-4 {
    height: 478px;
  }
}

@media (max-width: 767px) {
  .u-section-7 .u-text-1 {
    margin-right: 337px;
  }

  .u-section-7 .u-list-1 {
    width: 540px;
  }

  .u-section-7 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-7 .u-image-1 {
    height: 327px;
  }

  .u-section-7 .u-image-2 {
    height: 327px;
  }

  .u-section-7 .u-image-3 {
    height: 327px;
  }

  .u-section-7 .u-image-4 {
    height: 327px;
  }
}

@media (max-width: 575px) {
  .u-section-7 .u-text-1 {
    margin-right: 137px;
  }

  .u-section-7 .u-list-1 {
    width: 340px;
  }

  .u-section-7 .u-image-1 {
    height: 201px;
  }

  .u-section-7 .u-image-2 {
    height: 201px;
  }

  .u-section-7 .u-image-3 {
    height: 201px;
  }

  .u-section-7 .u-image-4 {
    height: 201px;
  }
}

.u-block-19c5-21:not([data-block-selected]):not([data-cell-selected]),
.u-block-19c5-21:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-19c5-21:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-19c5-21:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-21:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-21:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-19c5-21:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-21:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-21:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-19c5-25:not([data-block-selected]):not([data-cell-selected]),
.u-block-19c5-25:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-19c5-25:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-19c5-25:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-25:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-25:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-19c5-25:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-25:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-25:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-19c5-43:not([data-block-selected]):not([data-cell-selected]),
.u-block-19c5-43:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-19c5-43:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-19c5-43:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-43:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-43:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-19c5-43:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-43:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-43:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-19c5-96:not([data-block-selected]):not([data-cell-selected]),
.u-block-19c5-96:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-19c5-96:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-19c5-96:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-96:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-96:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-19c5-96:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-96:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-96:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-19c5-102:not([data-block-selected]):not([data-cell-selected]),
.u-block-19c5-102:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-19c5-102:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-19c5-102:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-102:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-102:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-19c5-102:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-102:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-102:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-19c5-108:not([data-block-selected]):not([data-cell-selected]),
.u-block-19c5-108:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-19c5-108:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-19c5-108:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-108:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-108:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-19c5-108:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-108:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-108:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-19c5-114:not([data-block-selected]):not([data-cell-selected]),
.u-block-19c5-114:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-19c5-114:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-19c5-114:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-114:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-114:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-19c5-114:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-114:not([data-block-selected]):not([data-cell-selected]).u-block-19c5-114:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-section-7 .u-list-item-1:not([data-block-selected]):not([data-cell-selected]),
.u-section-7 .u-list-item-1:not([data-block-selected]):not([data-cell-selected]):before,
.u-section-7 .u-list-item-1:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-7 .u-list-item-1:not([data-block-selected]):not([data-cell-selected]).u-section-7 .u-list-item-1:not([data-block-selected]):not([data-cell-selected]).u-section-7 .u-list-item-1:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-section-7 .u-list-item-1:not([data-block-selected]):not([data-cell-selected]).u-section-7 .u-list-item-1:not([data-block-selected]):not([data-cell-selected]).u-section-7 .u-list-item-1:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-section-7 .u-list-item-2:not([data-block-selected]):not([data-cell-selected]),
.u-section-7 .u-list-item-2:not([data-block-selected]):not([data-cell-selected]):before,
.u-section-7 .u-list-item-2:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-7 .u-list-item-2:not([data-block-selected]):not([data-cell-selected]).u-section-7 .u-list-item-2:not([data-block-selected]):not([data-cell-selected]).u-section-7 .u-list-item-2:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-section-7 .u-list-item-2:not([data-block-selected]):not([data-cell-selected]).u-section-7 .u-list-item-2:not([data-block-selected]):not([data-cell-selected]).u-section-7 .u-list-item-2:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-section-7 .u-list-item-3:not([data-block-selected]):not([data-cell-selected]),
.u-section-7 .u-list-item-3:not([data-block-selected]):not([data-cell-selected]):before,
.u-section-7 .u-list-item-3:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-7 .u-list-item-3:not([data-block-selected]):not([data-cell-selected]).u-section-7 .u-list-item-3:not([data-block-selected]):not([data-cell-selected]).u-section-7 .u-list-item-3:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-section-7 .u-list-item-3:not([data-block-selected]):not([data-cell-selected]).u-section-7 .u-list-item-3:not([data-block-selected]):not([data-cell-selected]).u-section-7 .u-list-item-3:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-section-7 .u-list-item-4:not([data-block-selected]):not([data-cell-selected]),
.u-section-7 .u-list-item-4:not([data-block-selected]):not([data-cell-selected]):before,
.u-section-7 .u-list-item-4:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-7 .u-list-item-4:not([data-block-selected]):not([data-cell-selected]).u-section-7 .u-list-item-4:not([data-block-selected]):not([data-cell-selected]).u-section-7 .u-list-item-4:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-section-7 .u-list-item-4:not([data-block-selected]):not([data-cell-selected]).u-section-7 .u-list-item-4:not([data-block-selected]):not([data-cell-selected]).u-section-7 .u-list-item-4:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-19c5-84:not([data-block-selected]):not([data-cell-selected]),
.u-block-19c5-84:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-19c5-84:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-container-layout:hover > .u-block-19c5-84:not([data-block-selected]):not([data-cell-selected]) {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-container-layout.hover > .u-block-19c5-84:not([data-block-selected]):not([data-cell-selected]) {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-block-19c5-90:not([data-block-selected]):not([data-cell-selected]),
.u-block-19c5-90:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-19c5-90:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-container-layout:hover > .u-block-19c5-90:not([data-block-selected]):not([data-cell-selected]) {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-container-layout.hover > .u-block-19c5-90:not([data-block-selected]):not([data-cell-selected]) {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-7 .u-image-1,
.u-section-7 .u-image-1:before,
.u-section-7 .u-image-1 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-7 :hover > .u-container-layout .u-image-1 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-7 .hover > .u-container-layout .u-image-1 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-7 .u-image-2,
.u-section-7 .u-image-2:before,
.u-section-7 .u-image-2 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-7 :hover > .u-container-layout .u-image-2 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-7 .hover > .u-container-layout .u-image-2 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-7 .u-image-3,
.u-section-7 .u-image-3:before,
.u-section-7 .u-image-3 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-7 :hover > .u-container-layout .u-image-3 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-7 .hover > .u-container-layout .u-image-3 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-7 .u-image-4,
.u-section-7 .u-image-4:before,
.u-section-7 .u-image-4 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-7 :hover > .u-container-layout .u-image-4 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-7 .hover > .u-container-layout .u-image-4 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-7 .u-text-4,
.u-section-7 .u-text-4:before,
.u-section-7 .u-text-4 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-7 :hover > .u-container-layout .u-text-4 {
  text-decoration: underline !important;
}

.u-section-7 .hover > .u-container-layout .u-text-4 {
  text-decoration: underline !important;
}

.u-section-7 .u-text-6,
.u-section-7 .u-text-6:before,
.u-section-7 .u-text-6 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-7 :hover > .u-container-layout .u-text-6 {
  text-decoration: underline !important;
}

.u-section-7 .hover > .u-container-layout .u-text-6 {
  text-decoration: underline !important;
}

.u-section-7 .u-text-8,
.u-section-7 .u-text-8:before,
.u-section-7 .u-text-8 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-7 :hover > .u-container-layout .u-text-8 {
  text-decoration: underline !important;
}

.u-section-7 .hover > .u-container-layout .u-text-8 {
  text-decoration: underline !important;
}

.u-section-7 .u-text-10,
.u-section-7 .u-text-10:before,
.u-section-7 .u-text-10 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-7 :hover > .u-container-layout .u-text-10 {
  text-decoration: underline !important;
}

.u-section-7 .hover > .u-container-layout .u-text-10 {
  text-decoration: underline !important;
} .u-section-8 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-8 .u-sheet-1 {
  min-height: 650px;
}

.u-section-8 .u-text-1 {
  font-weight: 300;
  margin: 20px 820px 0 30px;
}

.u-section-8 .u-text-2 {
  font-weight: 400;
  margin: 20px auto 0 30px;
}

.u-section-8 .u-list-1 {
  margin: 30px auto 60px;
}

.u-section-8 .u-repeater-1 {
  grid-auto-columns: 373.328px 373.328px 373.328px;
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 448px;
  grid-gap: 10px;
}

.u-section-8 .u-list-item-1 {
  --radius: 20px;
}

.u-section-8 .u-container-layout-1 {
  padding: 30px;
}

.u-section-8 .u-text-3 {
  margin: 0;
}

.u-section-8 .u-text-4 {
  font-weight: 700;
  margin: 20px 0 0;
}

.u-section-8 .u-text-5 {
  font-weight: 700;
  margin: 20px 0 0;
}

.u-section-8 .u-list-item-2 {
  --radius: 20px;
}

.u-section-8 .u-container-layout-2 {
  padding: 30px;
}

.u-section-8 .u-text-6 {
  margin: 0;
}

.u-section-8 .u-text-7 {
  font-weight: 700;
  margin: 20px 0 0;
}

.u-section-8 .u-text-8 {
  font-weight: 700;
  margin: 20px 0 0;
}

.u-section-8 .u-list-item-3 {
  --radius: 20px;
}

.u-section-8 .u-container-layout-3 {
  padding: 30px;
}

.u-section-8 .u-text-9 {
  margin: 0;
}

.u-section-8 .u-text-10 {
  font-weight: 700;
  margin: 20px 0 0;
}

.u-section-8 .u-text-11 {
  font-weight: 700;
  margin: 20px 0 0;
}

.u-section-8 .u-list-item-4 {
  --radius: 20px;
}

.u-section-8 .u-container-layout-4 {
  padding: 30px;
}

.u-section-8 .u-text-12 {
  margin: 0;
}

.u-section-8 .u-text-13 {
  font-weight: 700;
  margin: 20px 0 0;
}

.u-section-8 .u-text-14 {
  font-weight: 700;
  margin: 20px 0 0;
}

.u-section-8 .u-list-item-5 {
  --radius: 20px;
}

.u-section-8 .u-container-layout-5 {
  padding: 30px;
}

.u-section-8 .u-text-15 {
  margin: 0;
}

.u-section-8 .u-text-16 {
  font-weight: 700;
  margin: 20px 0 0;
}

.u-section-8 .u-text-17 {
  font-weight: 700;
  margin: 20px 0 0;
}

.u-section-8 .u-list-item-6 {
  --radius: 20px;
}

.u-section-8 .u-container-layout-6 {
  padding: 30px;
}

.u-section-8 .u-text-18 {
  margin: 0;
}

.u-section-8 .u-text-19 {
  font-weight: 700;
  margin: 20px 0 0;
}

.u-section-8 .u-text-20 {
  font-weight: 700;
  margin: 20px 0 0;
}

.u-section-8 .u-gallery-nav-1 {
  position: absolute;
  left: 0;
  width: 40px;
  height: 40px;
  top: 204px;
}

.u-section-8 .u-gallery-nav-2 {
  position: absolute;
  width: 40px;
  height: 40px;
  left: auto;
  top: 204px;
  right: 0;
}

@media (max-width: 1199px) {
  .u-section-8 .u-text-1 {
    margin-right: 650px;
    margin-left: 0;
  }

  .u-section-8 .u-text-2 {
    margin-left: 0;
  }

  .u-section-8 .u-list-1 {
    min-height: 448px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-8 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
  }
}

@media (max-width: 991px) {
  .u-section-8 .u-text-1 {
    margin-right: 430px;
  }

  .u-section-8 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
  }
}

@media (max-width: 767px) {
  .u-section-8 .u-sheet-1 {
    min-height: 505px;
  }

  .u-section-8 .u-text-1 {
    margin-right: 190px;
  }

  .u-section-8 .u-list-1 {
    margin-bottom: -85px;
    min-height: 313px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-8 .u-repeater-1 {
    grid-auto-columns: calc(100% + 0px);
    grid-template-columns: 100%;
    min-height: 313px;
  }

  .u-section-8 .u-gallery-nav-1 {
    top: 137px;
  }

  .u-section-8 .u-gallery-nav-2 {
    top: 137px;
  }
}

@media (max-width: 575px) {
  .u-section-8 .u-sheet-1 {
    min-height: 594px;
  }

  .u-section-8 .u-text-1 {
    margin-right: 0;
  }

  .u-section-8 .u-list-1 {
    margin-bottom: 30px;
    min-height: 409px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-8 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-8 .u-gallery-nav-1 {
    top: 185px;
  }

  .u-section-8 .u-gallery-nav-2 {
    top: 185px;
  }
}