import { ReactNode } from "react";
import TopBar from "./TopBar";

interface FloatingCardProps {
	children: ReactNode;
	title: string;
	breadcrumbs?: { name: string; path: string }[];
	layout?: 'default' | 'custom';
}

const FloatingCard = ({ children, title, breadcrumbs, layout = 'default' }: FloatingCardProps) => {
  return (
    <div className="p-2.5 min-w-0" style={{ marginLeft: '72px', width: 'calc(100vw - 72px)' }}>
      <div className="bg-[#fdfdf9] rounded-[10px] h-[calc(100vh-20px)] flex flex-col overflow-hidden min-w-0">
        <TopBar title={title} breadcrumbs={breadcrumbs} />
        {layout === 'default' ? (
          <div className="flex-1 overflow-auto p-6 min-w-0">
            {children}
          </div>
        ) : (
          <div className="flex-1 overflow-hidden min-w-0">
            {children}
          </div>
        )}
      </div>
    </div>
  );
};

export default FloatingCard;
