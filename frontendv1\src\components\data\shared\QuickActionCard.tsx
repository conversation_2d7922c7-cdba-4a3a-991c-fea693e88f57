import { ReactNode } from "react";
import { Plus } from "lucide-react";

interface QuickActionCardProps {
	title: string;
	description?: string;
	icon?: ReactNode;
	onClick: () => void;
}

const QuickActionCard = ({
	title,
	description,
	icon,
	onClick,
}: QuickActionCardProps) => {
	return (
		<button
			onClick={onClick}
			className="p-4 border border-gray-200 rounded-lg hover:border-green-500 hover:bg-green-50 transition-colors text-left group"
		>
			<div className="flex items-center mb-2">
				<div className="text-green-500 mr-2">
					{icon || <Plus className="h-5 w-5" />}
				</div>
				<h4 className="font-medium text-gray-900 group-hover:text-green-700">
					{title}
				</h4>
			</div>
			{description && (
				<p className="text-sm text-gray-500 group-hover:text-green-600">
					{description}
				</p>
			)}
		</button>
	);
};

export default QuickActionCard;
