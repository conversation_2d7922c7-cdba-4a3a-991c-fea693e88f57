import React, { useState } from "react";
import {
	Calendar,
	Filter,
	Plus,
	Search,
	Clock,
	User,
} from "lucide-react";

interface ScheduledInspectionsProps {
	siteId: string;
}

interface ScheduledInspection {
	id: string;
	templateName: string;
	target: string;
	assignedTo: string;
	dueDate: string;
	dueTime: string;
	status: "scheduled" | "overdue" | "in-progress";
	priority: "high" | "medium" | "low";
	recurrence?: string;
}

const ScheduledInspections: React.FC<ScheduledInspectionsProps> = ({
	siteId: _siteId,
}) => {
	const [viewMode, setViewMode] = useState<"list" | "calendar">("list");
	const [filterStatus, setFilterStatus] = useState<string>("all");
	const [searchTerm, setSearchTerm] = useState("");

	// Mock data - replace with actual API call
	const scheduledInspections: ScheduledInspection[] = [
		{
			id: "1",
			templateName: "Daily Site Safety Inspection",
			target: "Main Construction Area",
			assignedTo: "<PERSON>",
			dueDate: "2024-01-16",
			dueTime: "09:00",
			status: "scheduled",
			priority: "high",
			recurrence: "Daily",
		},
		{
			id: "2",
			templateName: "Equipment Pre-Use Check",
			target: "Excavator CAT-001",
			assignedTo: "Mary Wanjiku",
			dueDate: "2024-01-16",
			dueTime: "07:30",
			status: "scheduled",
			priority: "medium",
		},
		{
			id: "3",
			templateName: "Fire Safety Inspection",
			target: "Site Office Building",
			assignedTo: "Peter Kiprotich",
			dueDate: "2024-01-15",
			dueTime: "16:00",
			status: "overdue",
			priority: "high",
			recurrence: "Weekly",
		},
		{
			id: "4",
			templateName: "Scaffold Safety Check",
			target: "Building A - Level 3",
			assignedTo: "Sarah Njeri",
			dueDate: "2024-01-16",
			dueTime: "11:00",
			status: "in-progress",
			priority: "medium",
		},
		{
			id: "5",
			templateName: "PPE Compliance Check",
			target: "Site Entrance Gate",
			assignedTo: "David Ochieng",
			dueDate: "2024-01-17",
			dueTime: "08:00",
			status: "scheduled",
			priority: "low",
			recurrence: "Daily",
		},
	];

	const getStatusColor = (status: string) => {
		switch (status) {
			case "scheduled":
				return "text-blue-700 bg-blue-50";
			case "overdue":
				return "text-red-700 bg-red-50";
			case "in-progress":
				return "text-yellow-700 bg-yellow-50";
			default:
				return "text-gray-700 bg-gray-50";
		}
	};

	const getPriorityColor = (priority: string) => {
		switch (priority) {
			case "high":
				return "text-red-600";
			case "medium":
				return "text-yellow-600";
			case "low":
				return "text-green-600";
			default:
				return "text-gray-600";
		}
	};

	const filteredInspections = scheduledInspections.filter((inspection) => {
		const matchesStatus =
			filterStatus === "all" || inspection.status === filterStatus;
		const matchesSearch =
			inspection.templateName
				.toLowerCase()
				.includes(searchTerm.toLowerCase()) ||
			inspection.target.toLowerCase().includes(searchTerm.toLowerCase()) ||
			inspection.assignedTo.toLowerCase().includes(searchTerm.toLowerCase());
		return matchesStatus && matchesSearch;
	});

	const formatDate = (dateString: string) => {
		const date = new Date(dateString);
		return date.toLocaleDateString("en-US", {
			weekday: "short",
			month: "short",
			day: "numeric",
		});
	};

	return (
		<div className="space-y-6">
			{/* Header with Actions */}
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
				<div className="flex items-center space-x-4">
					<h2 className="text-xl font-semibold text-gray-900">
						Scheduled Inspections
					</h2>
					<div className="flex items-center space-x-2">
						<button
							onClick={() => setViewMode("list")}
							className={`p-2 rounded-md ${viewMode === "list" ? "bg-green-100 text-green-600" : "text-gray-400 hover:text-gray-600"}`}
						>
							<Filter className="h-4 w-4" />
						</button>
						<button
							onClick={() => setViewMode("calendar")}
							className={`p-2 rounded-md ${viewMode === "calendar" ? "bg-green-100 text-green-600" : "text-gray-400 hover:text-gray-600"}`}
						>
							<Calendar className="h-4 w-4" />
						</button>
					</div>
				</div>
				<button className="inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors">
					<Plus className="h-4 w-4 mr-2" />
					Schedule Inspection
				</button>
			</div>

			{/* Filters and Search */}
			<div className="flex flex-col sm:flex-row gap-4">
				<div className="flex-1">
					<div className="relative">
						<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
						<input
							type="text"
							placeholder="Search inspections..."
							value={searchTerm}
							onChange={(e) => setSearchTerm(e.target.value)}
							className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
						/>
					</div>
				</div>
				<select
					value={filterStatus}
					onChange={(e) => setFilterStatus(e.target.value)}
					className="px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-green-500 focus:border-transparent"
				>
					<option value="all">All Status</option>
					<option value="scheduled">Scheduled</option>
					<option value="overdue">Overdue</option>
					<option value="in-progress">In Progress</option>
				</select>
			</div>

			{/* Inspections List */}
			<div className="bg-white rounded-lg border border-gray-200 shadow-sm">
				<div className="overflow-x-auto">
					<table className="min-w-full divide-y divide-gray-200">
						<thead className="bg-gray-50">
							<tr>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Inspection
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Target
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Assigned To
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Due Date
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Status
								</th>
								<th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
									Priority
								</th>
								<th className="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
									Actions
								</th>
							</tr>
						</thead>
						<tbody className="bg-white divide-y divide-gray-200">
							{filteredInspections.map((inspection) => (
								<tr key={inspection.id} className="hover:bg-gray-50">
									<td className="px-6 py-4 whitespace-nowrap">
										<div>
											<div className="text-sm font-medium text-gray-900">
												{inspection.templateName}
											</div>
											{inspection.recurrence && (
												<div className="text-xs text-gray-500">
													Recurring: {inspection.recurrence}
												</div>
											)}
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
										{inspection.target}
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="flex items-center">
											<User className="h-4 w-4 text-gray-400 mr-2" />
											<span className="text-sm text-gray-900">
												{inspection.assignedTo}
											</span>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<div className="flex items-center">
											<Clock className="h-4 w-4 text-gray-400 mr-2" />
											<div>
												<div className="text-sm text-gray-900">
													{formatDate(inspection.dueDate)}
												</div>
												<div className="text-xs text-gray-500">
													{inspection.dueTime}
												</div>
											</div>
										</div>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<span
											className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${getStatusColor(inspection.status)}`}
										>
											{inspection.status === "in-progress"
												? "In Progress"
												: inspection.status.charAt(0).toUpperCase() +
													inspection.status.slice(1)}
										</span>
									</td>
									<td className="px-6 py-4 whitespace-nowrap">
										<span
											className={`text-sm font-medium ${getPriorityColor(inspection.priority)}`}
										>
											{inspection.priority.charAt(0).toUpperCase() +
												inspection.priority.slice(1)}
										</span>
									</td>
									<td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
										<button className="text-green-600 hover:text-green-900 mr-3">
											Edit
										</button>
										<button className="text-blue-600 hover:text-blue-900">
											Start
										</button>
									</td>
								</tr>
							))}
						</tbody>
					</table>
				</div>
			</div>

			{filteredInspections.length === 0 && (
				<div className="text-center py-12">
					<Calendar className="mx-auto h-12 w-12 text-gray-400" />
					<h3 className="mt-2 text-sm font-medium text-gray-900">
						No inspections found
					</h3>
					<p className="mt-1 text-sm text-gray-500">
						{searchTerm || filterStatus !== "all"
							? "Try adjusting your search or filter criteria."
							: "Get started by scheduling your first inspection."}
					</p>
				</div>
			)}
		</div>
	);
};

export default ScheduledInspections;
