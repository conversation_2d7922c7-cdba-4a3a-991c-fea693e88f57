import { Link, useLocation, useNavigate } from 'react-router-dom';
import { useState, useRef, useEffect } from 'react';
import { ChevronDown, ChevronUp, LogOut, Search, Settings, UserRound, Info, X } from 'lucide-react';
import { useAllSites } from '../../hooks/useSiteContext';
import { useAuth } from '../../hooks/useAuthContext';
import { isSiteBreadcrumb, getEquivalentSitePath, extractSiteId } from '../../utils/routeUtils';
import NotificationSystem from '../notifications/NotificationSystem';
import { WeatherIcon } from '../weather/WeatherIcon';

// Unified TopBar Icon Button Component
interface TopBarIconButtonProps {
  icon: React.ReactNode;
  onClick?: () => void;
  href?: string;
  title: string;
  variant?: 'default' | 'user';
  isActive?: boolean;
  className?: string;
}

const TopBarIconButton: React.FC<TopBarIconButtonProps> = ({
  icon,
  onClick,
  href,
  title,
  variant = 'default',
  isActive = false,
  className = ''
}) => {
  const baseClasses = `
    relative inline-flex items-center justify-center
    transition-all duration-200 ease-in-out
    focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2
    ${variant === 'user' ? 'p-1.5' : 'p-2'}
    ${variant === 'user' ? 'rounded-full' : 'rounded-lg'}
    ${isActive
      ? 'bg-green-50 text-green-600 shadow-sm'
      : 'text-gray-600 hover:text-green-600 hover:bg-gray-50'
    }
    ${className}
  `;

  if (href) {
    return (
      <Link to={href} className={baseClasses} title={title}>
        {icon}
      </Link>
    );
  }

  return (
    <button onClick={onClick} className={baseClasses} title={title}>
      {icon}
    </button>
  );
};

interface TopBarProps {
	title: string;
	breadcrumbs?: { name: string; path: string }[];
}

const TopBar = ({ title, breadcrumbs }: TopBarProps) => {
  const location = useLocation();
  const navigate = useNavigate();
  const allSites = useAllSites();
  const { user, logout } = useAuth();

  const [userMenuOpen, setUserMenuOpen] = useState(false);
  const [siteDropdownOpen, setSiteDropdownOpen] = useState(false);
  const [isSearchExpanded, setIsSearchExpanded] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');

  const userMenuRef = useRef<HTMLDivElement>(null);
  const userButtonRef = useRef<HTMLButtonElement>(null);
  const siteDropdownRef = useRef<HTMLDivElement>(null);
  const siteButtonRef = useRef<HTMLButtonElement>(null);
  const searchRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

	// Get current site ID for filtering
	const currentSiteId = extractSiteId(location.pathname);

	// Get current site data for dynamic site name display
	const currentSite = allSites.find((site) => site.id === currentSiteId);

	// Handle clicks outside the user menu, site dropdown, and search
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			// Handle user menu
			if (
				userMenuOpen &&
				userMenuRef.current &&
				userButtonRef.current &&
				!userMenuRef.current.contains(event.target as Node) &&
				!userButtonRef.current.contains(event.target as Node)
			) {
				setUserMenuOpen(false);
			}

			// Handle site dropdown
			if (
				siteDropdownOpen &&
				siteDropdownRef.current &&
				siteButtonRef.current &&
				!siteDropdownRef.current.contains(event.target as Node) &&
				!siteButtonRef.current.contains(event.target as Node)
			) {
				setSiteDropdownOpen(false);
			}

			// Handle search collapse
			if (
				isSearchExpanded &&
				searchRef.current &&
				!searchRef.current.contains(event.target as Node)
			) {
				setIsSearchExpanded(false);
			}
		};

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [userMenuOpen, siteDropdownOpen, isSearchExpanded]);

  // Handle site switching
  const handleSiteSwitch = (targetSiteId: string) => {
    const equivalentPath = getEquivalentSitePath(location.pathname, targetSiteId);
    setSiteDropdownOpen(false);
    navigate(equivalentPath);
  };

  // Handle logout
  const handleLogout = () => {
    logout();
    setUserMenuOpen(false);
    navigate('/login');
  };

  // Get user initials for avatar
  const getUserInitials = () => {
    if (!user) return 'U';
    return `${user.firstName.charAt(0)}${user.lastName.charAt(0)}`.toUpperCase();
  };

  // Handle search expand
  const handleSearchExpand = () => {
    setIsSearchExpanded(true);
    // Focus the input after the animation starts
    setTimeout(() => {
      searchInputRef.current?.focus();
    }, 100);
  };

  // Handle search collapse
  const handleSearchCollapse = () => {
    setIsSearchExpanded(false);
    setSearchTerm('');
  };

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    // TODO: Implement search functionality
  };

  // Handle search submit
  const handleSearchSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (searchTerm.trim()) {
      // TODO: Implement search functionality
      console.log('Searching for:', searchTerm);
    }
  };

	return (
		<div className="border-b border-[#f3f2ee]">
			<div className="flex items-center px-6 py-3">
				<div className="flex-1">
					{/* Show title only when there are no breadcrumbs (main dashboard) */}
					{(!breadcrumbs || breadcrumbs.length === 0) && (
						<h1 className="text-lg font-semibold text-gray-800">{title}</h1>
					)}

					{/* Show breadcrumbs when available (site views) */}
					{breadcrumbs && breadcrumbs.length > 0 && (
						<div className="flex items-center text-sm text-gray-500">
							{breadcrumbs.map((crumb, index) => (
								<div key={crumb.path} className="flex items-center">
									{index > 0 && <span className="mx-2">/</span>}

									{/* Site name with dropdown */}
									{isSiteBreadcrumb(breadcrumbs, index) ? (
										<div className="relative">
											<button
												ref={siteButtonRef}
												onClick={() => setSiteDropdownOpen(!siteDropdownOpen)}
												className="flex items-center hover:text-green-500 focus:outline-none transition-colors duration-200"
											>
												<span>
													{currentSite ? currentSite.name : crumb.name}
												</span>
												<div className="ml-1 flex flex-col">
													<ChevronUp
														className={`h-2 w-2 transition-opacity duration-200 ${siteDropdownOpen ? "opacity-100" : "opacity-30"}`}
													/>
													<ChevronDown
														className={`h-2 w-2 transition-opacity duration-200 ${siteDropdownOpen ? "opacity-30" : "opacity-100"}`}
													/>
												</div>
											</button>

											{siteDropdownOpen && (
												<div
													ref={siteDropdownRef}
													className="absolute top-full left-0 mt-1 w-72 bg-white rounded-md shadow-lg py-1 ring-1 ring-black ring-opacity-5 z-50 max-h-60 overflow-y-auto"
												>
													<div className="px-3 py-2 text-xs font-medium text-gray-500 border-b border-gray-100">
														Select a site:
													</div>
													{allSites.map((site) => {
														const isCurrentSite = site.id === currentSiteId;
														return (
															<button
																key={site.id}
																onClick={() => handleSiteSwitch(site.id)}
																disabled={isCurrentSite}
																className={`w-full text-left px-3 py-2 text-sm flex items-center justify-between transition-colors duration-200 ${
																	isCurrentSite
																		? "bg-green-50 text-green-700 cursor-default"
																		: "text-gray-700 hover:bg-gray-100 hover:text-gray-900"
																}`}
															>
																<div className="flex-1 min-w-0">
																	<div
																		className={`font-medium truncate ${isCurrentSite ? "text-green-700" : ""}`}
																	>
																		{site.name}
																		{isCurrentSite && (
																			<span className="ml-2 text-xs font-normal">
																				(Current)
																			</span>
																		)}
																	</div>
																	<div className="text-xs text-gray-500 truncate">
																		{site.location}
																	</div>
																</div>
																<div className="flex items-center ml-3">
																	<div
																		className={`w-2 h-2 rounded-full ${
																			site.healthStatus === "green"
																				? "bg-green-500"
																				: site.healthStatus === "amber"
																					? "bg-amber-500"
																					: "bg-red-500"
																		}`}
																	></div>
																</div>
															</button>
														);
													})}
												</div>
											)}
										</div>
									) : (
										/* Regular breadcrumb link */
										<Link
											to={crumb.path}
											className={`hover:text-green-500 ${
												index === breadcrumbs.length - 1
													? "font-medium text-gray-800"
													: ""
											}`}
										>
											{crumb.name}
										</Link>
									)}
								</div>
							))}
						</div>
					)}
				</div>

				{/* Modern Expandable Search */}
				<div
					ref={searchRef}
					className={`relative mx-4 transition-all duration-300 ease-in-out ${
						isSearchExpanded ? 'w-80' : 'w-auto'
					}`}
				>
					{!isSearchExpanded ? (
						// Collapsed state - just the search icon
						<button
							onClick={handleSearchExpand}
							className="p-2 rounded-full text-gray-600 hover:bg-gray-100 hover:text-green-500 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
							title="Search"
						>
							<Search className="h-5 w-5" />
						</button>
					) : (
						// Expanded state - full search input
						<form onSubmit={handleSearchSubmit} className="relative">
							<div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
								<Search className="h-4 w-4 text-gray-500" />
							</div>
							<input
								ref={searchInputRef}
								type="text"
								value={searchTerm}
								onChange={(e) => handleSearchChange(e.target.value)}
								className="block w-full pl-10 pr-10 py-2 border border-gray-300 rounded-full leading-5 bg-white placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-green-500 sm:text-sm shadow-sm transition-all duration-200"
								placeholder="Search across all data..."
							/>
							{searchTerm && (
								<button
									type="button"
									onClick={handleSearchCollapse}
									className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-400 hover:text-gray-600 transition-colors duration-200"
								>
									<X className="h-4 w-4" />
								</button>
							)}
						</form>
					)}
				</div>

				{/* Right Side Actions - Improved Design */}
				<div className="flex items-center space-x-2">
					{/* Site Info Icon */}
					{currentSiteId && (
						<TopBarIconButton
							icon={<Info className="h-5 w-5" />}
							href={`/sites/${currentSiteId}/info`}
							title="Site Information"
						/>
					)}

					{/* Weather Icon */}
					<WeatherIcon className="" />

					{/* Enhanced Notifications */}
					<NotificationSystem position="top-right" enableSound={true} />
				</div>

				{/* User Menu */}
				<div className="relative ml-2">
					<button
						ref={userButtonRef}
						className="flex items-center p-1.5 rounded-full text-gray-600 hover:bg-gray-50 transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2"
						onClick={() => setUserMenuOpen(!userMenuOpen)}
						title={user ? `${user.firstName} ${user.lastName}` : 'User Menu'}
					>
						{user?.avatar ? (
							<img
								src={user.avatar}
								alt={`${user.firstName} ${user.lastName}`}
								className="h-8 w-8 rounded-full object-cover ring-2 ring-white shadow-sm"
							/>
						) : (
							<div className="h-8 w-8 rounded-full bg-green-500 flex items-center justify-center text-white text-sm font-medium shadow-sm ring-2 ring-white">
								{getUserInitials()}
							</div>
						)}
						<div className="ml-1.5 flex flex-col">
							<ChevronUp className={`h-2 w-2 transition-all duration-200 ${userMenuOpen ? 'opacity-100 text-green-600' : 'opacity-40'}`} />
							<ChevronDown className={`h-2 w-2 transition-all duration-200 ${userMenuOpen ? 'opacity-40' : 'opacity-100'}`} />
						</div>
					</button>

					{userMenuOpen && (
						<div
							ref={userMenuRef}
							className="absolute right-0 mt-2 w-64 bg-white rounded-md shadow-lg py-1 ring-1 ring-black ring-opacity-5 z-50"
						>
							{/* User Info Header */}
							{user && (
								<div className="px-4 py-3 border-b border-gray-100">
									<div className="flex items-center space-x-3">
										{user.avatar ? (
											<img
												src={user.avatar}
												alt={`${user.firstName} ${user.lastName}`}
												className="w-10 h-10 rounded-full object-cover"
											/>
										) : (
											<div className="w-10 h-10 rounded-full bg-green-500 text-white flex items-center justify-center text-sm font-medium">
												{getUserInitials()}
											</div>
										)}
										<div className="flex-1 min-w-0">
											<p className="text-sm font-medium text-gray-900 truncate">
												{user.firstName} {user.lastName}
											</p>
											<p className="text-xs text-gray-500 truncate">
												{user.email}
											</p>
											<span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-green-100 text-green-800 mt-1">
												{user.role.name}
											</span>
										</div>
									</div>
								</div>
							)}

							{/* Menu Items */}
							<div className="py-1">
								<Link
									to="/account"
									onClick={() => setUserMenuOpen(false)}
									className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
								>
									<UserRound className="h-4 w-4 mr-2 text-gray-500" />
									Account Settings
								</Link>
								<Link
									to="/settings"
									onClick={() => setUserMenuOpen(false)}
									className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
								>
									<Settings className="h-4 w-4 mr-2 text-gray-500" />
									System Settings
								</Link>
							</div>

							{/* Logout */}
							<div className="border-t border-gray-100 py-1">
								<button
									onClick={handleLogout}
									className="flex items-center w-full px-4 py-2 text-sm text-red-600 hover:bg-red-50 transition-colors"
								>
									<LogOut className="h-4 w-4 mr-2" />
									Sign out
								</button>
							</div>
						</div>
					)}
				</div>
			</div>
		</div>
	);
};

export default TopBar;
