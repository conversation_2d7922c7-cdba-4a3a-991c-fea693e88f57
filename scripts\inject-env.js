#!/usr/bin/env node

/**
 * This script injects environment variables into HTML files
 * It's used by Netlify during the build process
 *
 * Usage: node inject-env.js path/to/html/file.html
 */

const fs = require('fs');
const path = require('path');

// Get the file path from command line arguments
const filePath = process.argv[2];

if (!filePath) {
  console.error('Please provide a file path');
  process.exit(1);
}

// Read the file
try {
  let content = fs.readFileSync(filePath, 'utf8');

  // Create a script tag with environment variables
  const envScript = `
  <script>
    // Environment variables injected at build time
    window.SHEET_ID = "${process.env.SHEET_ID || ''}";
    window.GOOGLE_SHEETS_API_KEY = "${process.env.GOOGLE_SHEETS_API_KEY || ''}";
  </script>
  `;

  // Insert the script tag before the closing head tag
  content = content.replace('</head>', `${envScript}\n  </head>`);

  // Write the modified content back to the file
  fs.writeFileSync(filePath, content, 'utf8');

  console.log(`Environment variables injected into ${filePath}`);
} catch (error) {
  console.error(`Error processing file ${filePath}:`, error);
  process.exit(1);
}
