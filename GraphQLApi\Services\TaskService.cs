using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;
using Task = Shared.GraphQL.Models.Task;

namespace GraphQLApi.Services
{
    public class TaskService : ITaskService
    {
        private readonly IDbContextFactory<AppDbContext> _contextFactory;

        public TaskService(IDbContextFactory<AppDbContext> contextFactory)
        {
            _contextFactory = contextFactory;
        }



        public async Task<IEnumerable<Task>> GetAllTasksAsync()
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Tasks
                .Include(t => t.ChiefEngineer)
                .Include(t => t.WorkersAssigned)
                .Include(t => t.EquipmentInvolved)
                .ToListAsync();
        }

        public async Task<Task?> GetTaskByIdAsync(int id)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Tasks
                .Include(t => t.ChiefEngineer)
                .Include(t => t.WorkersAssigned)
                .Include(t => t.EquipmentInvolved)
                .FirstOrDefaultAsync(t => t.Id == id);
        }

        public async Task<Task> CreateTaskAsync(Task task)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            
            // Generate task number if not provided
            if (string.IsNullOrEmpty(task.TaskNumber))
            {
                task.TaskNumber = await GenerateTaskNumberAsync();
            }

            context.Tasks.Add(task);
            await context.SaveChangesAsync();
            return task;
        }

        public async Task<Task?> UpdateTaskAsync(int id, Task task)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var existingTask = await context.Tasks.FindAsync(id);
            
            if (existingTask == null)
                return null;

            // Update properties
            existingTask.Name = task.Name;
            existingTask.Type = task.Type;
            existingTask.Description = task.Description;
            existingTask.Status = task.Status;
            existingTask.Priority = task.Priority;
            existingTask.TimeForCompletion = task.TimeForCompletion;
            existingTask.DueDate = task.DueDate;
            existingTask.StartDate = task.StartDate;
            existingTask.Category = task.Category;
            existingTask.InspectionStatus = task.InspectionStatus;
            existingTask.AssociatedMethodStatement = task.AssociatedMethodStatement;
            existingTask.ChiefEngineerId = task.ChiefEngineerId;

            await context.SaveChangesAsync();
            return existingTask;
        }

        public async Task<bool> DeleteTaskAsync(int id)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var task = await context.Tasks.FindAsync(id);
            
            if (task == null)
                return false;

            context.Tasks.Remove(task);
            await context.SaveChangesAsync();
            return true;
        }

        public async Task<IEnumerable<Task>> GetTasksByWorkerIdAsync(int workerId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Tasks
                .Include(t => t.ChiefEngineer)
                .Include(t => t.WorkersAssigned)
                .Include(t => t.EquipmentInvolved)
                .Where(t => t.WorkersAssigned.Any(w => w.Id == workerId))
                .ToListAsync();
        }

        public async Task<IEnumerable<Task>> GetTasksByChiefEngineerIdAsync(int chiefEngineerId)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Tasks
                .Include(t => t.ChiefEngineer)
                .Include(t => t.WorkersAssigned)
                .Include(t => t.EquipmentInvolved)
                .Where(t => t.ChiefEngineerId == chiefEngineerId)
                .ToListAsync();
        }

        public async Task<IEnumerable<Task>> GetTasksByStatusAsync(Shared.Enums.TaskStatus status)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Tasks
                .Include(t => t.ChiefEngineer)
                .Include(t => t.WorkersAssigned)
                .Include(t => t.EquipmentInvolved)
                .Where(t => t.Status == status)
                .ToListAsync();
        }

        public async Task<IEnumerable<Task>> GetTasksByPriorityAsync(Shared.Enums.TaskPriority priority)
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            return await context.Tasks
                .Include(t => t.ChiefEngineer)
                .Include(t => t.WorkersAssigned)
                .Include(t => t.EquipmentInvolved)
                .Where(t => t.Priority == priority)
                .ToListAsync();
        }

        public async Task<string> GenerateTaskNumberAsync()
        {
            await using var context = await _contextFactory.CreateDbContextAsync();
            var currentYear = DateTime.Now.Year;
            var yearPrefix = $"TSK-{currentYear}-";
            
            var lastTaskNumber = await context.Tasks
                .Where(t => t.TaskNumber.StartsWith(yearPrefix))
                .OrderByDescending(t => t.TaskNumber)
                .Select(t => t.TaskNumber)
                .FirstOrDefaultAsync();

            int nextNumber = 1;
            if (!string.IsNullOrEmpty(lastTaskNumber))
            {
                var numberPart = lastTaskNumber.Substring(yearPrefix.Length);
                if (int.TryParse(numberPart, out int lastNumber))
                {
                    nextNumber = lastNumber + 1;
                }
            }

            return $"{yearPrefix}{nextNumber:D4}";
        }
    }
}
