 .u-section-1 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-1 .u-sheet-1 {
  min-height: 480px;
}

.u-section-1 .u-text-1 {
  margin: 20px 0 0;
}

.u-section-1 .u-icon-1 {
  font-size: 1.1112em;
}

.u-section-1 .u-btn-1 {
  border-style: solid;
  padding: 0;
}

.u-section-1 .u-text-2 {
  margin: 10px 0 0;
}

.u-section-1 .u-image-1 {
  height: 400px;
  margin-top: 20px;
  margin-bottom: 20px;
  --radius: 20px;
  width: 100%;
}

@media (max-width: 575px) {
  .u-section-1 .u-sheet-1 {
    min-height: 440px;
  }

  .u-section-1 .u-image-1 {
    height: 275px;
    margin-bottom: -64px;
    width: auto;
  }
} .u-section-2 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-2 .u-sheet-1 {
  min-height: 210px;
}

.u-section-2 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: -31px;
}

.u-section-2 .u-layout-cell-1 {
  min-height: 170px;
}

.u-section-2 .u-container-layout-1 {
  padding: 0;
}

.u-section-2 .u-text-1 {
  margin: 0 170px 0 0;
}

.u-section-2 .u-text-2 {
  margin: 1px 107px 0 0;
}

.u-section-2 .u-layout-cell-2 {
  min-height: 170px;
}

.u-section-2 .u-container-layout-2 {
  padding: 30px 30px 24px;
}

.u-section-2 .u-text-3 {
  font-weight: 400;
  margin: 0;
}

@media (max-width: 1199px) {
  .u-section-2 .u-sheet-1 {
    min-height: 238px;
  }

  .u-section-2 .u-layout-wrap-1 {
    position: relative;
    margin-bottom: -60px;
  }

  .u-section-2 .u-layout-cell-1 {
    min-height: 142px;
  }

  .u-section-2 .u-text-1 {
    margin-right: 70px;
  }

  .u-section-2 .u-text-2 {
    margin-right: 7px;
  }

  .u-section-2 .u-layout-cell-2 {
    min-height: 142px;
  }
}

@media (max-width: 991px) {
  .u-section-2 .u-sheet-1 {
    min-height: 265px;
  }

  .u-section-2 .u-layout-wrap-1 {
    margin-bottom: 20px;
  }

  .u-section-2 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-2 .u-text-1 {
    margin-right: 0;
  }

  .u-section-2 .u-text-2 {
    margin-right: 0;
  }

  .u-section-2 .u-layout-cell-2 {
    min-height: 100px;
  }
}

@media (max-width: 767px) {
  .u-section-2 .u-sheet-1 {
    min-height: 322px;
  }

  .u-section-2 .u-layout-wrap-1 {
    margin-bottom: 10px;
  }

  .u-section-2 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-2 .u-sheet-1 {
    min-height: 262px;
  }

  .u-section-2 .u-layout-wrap-1 {
    margin-bottom: 20px;
  }

  .u-section-2 .u-layout-cell-2 {
    min-height: 180px;
  }

  .u-section-2 .u-container-layout-2 {
    padding-top: 0;
    padding-bottom: 0;
  }

  .u-section-2 .u-text-3 {
    width: auto;
  }
} .u-section-3 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-3 .u-sheet-1 {
  min-height: 544px;
}

.u-section-3 .u-list-1 {
  margin-bottom: -4px;
  margin-top: 20px;
}

.u-section-3 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 504px;
  grid-gap: 10px;
}

.u-section-3 .u-container-layout-1 {
  padding: 0;
}

.u-section-3 .u-group-1 {
  --radius: 20px;
  min-height: 503px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  background-image: linear-gradient(180deg, rgba(249, 240, 248, 0.6), rgba(255, 255, 255, 1));
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-size: cover;
}

.u-section-3 .u-container-layout-2 {
  padding: 30px;
}

.u-section-3 .u-image-1 {
  --radius: 20px;
  height: auto;
  margin: -2px 0 0;
}

.u-section-3 .u-text-1 {
  margin: 30px 0 0;
}

.u-section-3 .u-icon-1 {
  width: 40px;
  height: 40px;
  --radius: 100px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  margin: -34px 0 0 auto;
  padding: 8px;
}

.u-section-3 .u-text-2 {
  margin: 14px 0 0;
}

.u-section-3 .u-container-layout-3 {
  padding: 0;
}

.u-section-3 .u-group-2 {
  --radius: 20px;
  min-height: 503px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  background-image: linear-gradient(180deg, rgba(249, 240, 248, 0.6), rgba(255, 255, 255, 1));
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-size: cover;
}

.u-section-3 .u-container-layout-4 {
  padding: 30px;
}

.u-section-3 .u-image-2 {
  --radius: 20px;
  height: auto;
  margin: -2px 0 0;
}

.u-section-3 .u-text-3 {
  margin: 30px 0 0;
}

.u-section-3 .u-icon-2 {
  width: 40px;
  height: 40px;
  --radius: 100px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  margin: -34px 0 0 auto;
  padding: 8px;
}

.u-section-3 .u-text-4 {
  margin: 14px 0 0;
}

.u-section-3 .u-container-layout-5 {
  padding: 0;
}

.u-section-3 .u-group-3 {
  --radius: 20px;
  min-height: 503px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  background-image: linear-gradient(180deg, rgba(249, 240, 248, 0.6), rgba(255, 255, 255, 1));
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-size: cover;
}

.u-section-3 .u-container-layout-6 {
  padding: 30px;
}

.u-section-3 .u-image-3 {
  --radius: 20px;
  height: auto;
  margin: -2px 0 0;
}

.u-section-3 .u-text-5 {
  margin: 30px 0 0;
}

.u-section-3 .u-icon-3 {
  width: 40px;
  height: 40px;
  --radius: 100px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  margin: -34px 0 0 auto;
  padding: 8px;
}

.u-section-3 .u-text-6 {
  margin: 14px 0 0;
}

@media (max-width: 1199px) {
  .u-section-3 .u-sheet-1 {
    min-height: 547px;
  }

  .u-section-3 .u-list-1 {
    margin-bottom: 20px;
  }

  .u-section-3 .u-repeater-1 {
    min-height: 507px;
  }

  .u-section-3 .u-group-1 {
    height: auto;
  }

  .u-section-3 .u-image-1 {
    height: NaNpx;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-3 .u-group-2 {
    height: auto;
  }

  .u-section-3 .u-image-2 {
    height: NaNpx;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-3 .u-group-3 {
    height: auto;
  }

  .u-section-3 .u-image-3 {
    height: NaNpx;
    margin-right: initial;
    margin-left: initial;
  }
}

@media (max-width: 991px) {
  .u-section-3 .u-sheet-1 {
    min-height: 1251px;
  }

  .u-section-3 .u-repeater-1 {
    grid-auto-columns: 100%;
    grid-template-columns: repeat(1, 100%);
    min-height: 1211px;
  }
}

@media (max-width: 767px) {
  .u-section-3 .u-sheet-1 {
    min-height: 2119px;
  }

  .u-section-3 .u-list-1 {
    margin-bottom: -3776px;
  }

  .u-section-3 .u-repeater-1 {
    grid-template-columns: 100%;
    min-height: 1394px;
  }

  .u-section-3 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-3 .u-text-2 {
    margin-top: 20px;
  }

  .u-section-3 .u-container-layout-4 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-3 .u-icon-2 {
    margin-top: -37px;
  }

  .u-section-3 .u-text-4 {
    margin-top: 17px;
  }

  .u-section-3 .u-container-layout-6 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-3 .u-text-6 {
    margin-top: 20px;
  }
}

@media (max-width: 575px) {
  .u-section-3 .u-sheet-1 {
    min-height: 1460px;
  }

  .u-section-3 .u-list-1 {
    margin-bottom: -4435px;
  }

  .u-section-3 .u-group-1 {
    min-height: 467px;
  }

  .u-section-3 .u-container-layout-2 {
    padding: 20px;
  }

  .u-section-3 .u-group-2 {
    min-height: 467px;
  }

  .u-section-3 .u-container-layout-4 {
    padding: 20px;
  }

  .u-section-3 .u-group-3 {
    min-height: 467px;
  }

  .u-section-3 .u-container-layout-6 {
    padding: 20px;
  }
}

.u-section-3 .u-icon-3,
.u-section-3 .u-icon-3:before,
.u-section-3 .u-icon-3 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 .u-icon-3.u-icon-3.u-icon-3:hover {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-3 .u-icon-3.u-icon-3.u-icon-3.hover {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-3 .u-icon-1,
.u-section-3 .u-icon-1:before,
.u-section-3 .u-icon-1 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 .u-icon-1.u-icon-1.u-icon-1:hover {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-3 .u-icon-1.u-icon-1.u-icon-1.hover {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-3 .u-icon-2,
.u-section-3 .u-icon-2:before,
.u-section-3 .u-icon-2 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 .u-icon-2.u-icon-2.u-icon-2:hover {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-3 .u-icon-2.u-icon-2.u-icon-2.hover {
  transform: translateX(0px) translateY(-5px) !important;
} .u-section-4 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-4 .u-sheet-1 {
  min-height: 544px;
}

.u-section-4 .u-list-1 {
  margin-bottom: -4px;
  margin-top: 20px;
}

.u-section-4 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 504px;
  grid-gap: 10px;
}

.u-section-4 .u-container-layout-1 {
  padding: 0;
}

.u-section-4 .u-group-1 {
  --radius: 20px;
  min-height: 503px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  background-image: linear-gradient(180deg, rgba(249, 240, 248, 0.6), rgba(255, 255, 255, 1));
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-size: cover;
}

.u-section-4 .u-container-layout-2 {
  padding: 30px;
}

.u-section-4 .u-image-1 {
  --radius: 20px;
  height: auto;
  margin: -2px 0 0;
}

.u-section-4 .u-text-1 {
  margin: 30px 0 0;
}

.u-section-4 .u-icon-1 {
  width: 40px;
  height: 40px;
  --radius: 100px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  margin: -34px 0 0 auto;
  padding: 8px;
}

.u-section-4 .u-text-2 {
  margin: 14px 0 0;
}

.u-section-4 .u-container-layout-3 {
  padding: 0;
}

.u-section-4 .u-group-2 {
  --radius: 20px;
  min-height: 503px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  background-image: linear-gradient(180deg, rgba(249, 240, 248, 0.6), rgba(255, 255, 255, 1));
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-size: cover;
}

.u-section-4 .u-container-layout-4 {
  padding: 30px;
}

.u-section-4 .u-image-2 {
  --radius: 20px;
  height: auto;
  margin: -2px 0 0;
}

.u-section-4 .u-text-3 {
  margin: 30px 0 0;
}

.u-section-4 .u-icon-2 {
  width: 40px;
  height: 40px;
  --radius: 100px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  margin: -34px 0 0 auto;
  padding: 8px;
}

.u-section-4 .u-text-4 {
  margin: 14px 0 0;
}

.u-section-4 .u-container-layout-5 {
  padding: 0;
}

.u-section-4 .u-group-3 {
  --radius: 20px;
  min-height: 503px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  background-image: linear-gradient(180deg, rgba(249, 240, 248, 0.6), rgba(255, 255, 255, 1));
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-size: cover;
}

.u-section-4 .u-container-layout-6 {
  padding: 30px;
}

.u-section-4 .u-image-3 {
  --radius: 20px;
  height: auto;
  margin: -2px 0 0;
}

.u-section-4 .u-text-5 {
  margin: 30px 0 0;
}

.u-section-4 .u-icon-3 {
  width: 40px;
  height: 40px;
  --radius: 100px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  margin: -34px 0 0 auto;
  padding: 8px;
}

.u-section-4 .u-text-6 {
  margin: 14px 0 0;
}

@media (max-width: 1199px) {
  .u-section-4 .u-sheet-1 {
    min-height: 543px;
  }

  .u-section-4 .u-list-1 {
    margin-bottom: 20px;
  }

  .u-section-4 .u-repeater-1 {
    min-height: 503px;
  }

  .u-section-4 .u-group-1 {
    height: auto;
  }

  .u-section-4 .u-image-1 {
    height: NaNpx;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-4 .u-group-2 {
    height: auto;
  }

  .u-section-4 .u-image-2 {
    height: NaNpx;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-4 .u-group-3 {
    height: auto;
  }

  .u-section-4 .u-image-3 {
    height: NaNpx;
    margin-right: initial;
    margin-left: initial;
  }
}

@media (max-width: 991px) {
  .u-section-4 .u-sheet-1 {
    min-height: 1251px;
  }

  .u-section-4 .u-repeater-1 {
    grid-auto-columns: 100%;
    grid-template-columns: repeat(1, 100%);
    min-height: 1211px;
  }
}

@media (max-width: 767px) {
  .u-section-4 .u-sheet-1 {
    min-height: 2119px;
  }

  .u-section-4 .u-list-1 {
    margin-bottom: -3776px;
  }

  .u-section-4 .u-repeater-1 {
    grid-template-columns: 100%;
    min-height: 1394px;
  }

  .u-section-4 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-text-2 {
    margin-top: 20px;
  }

  .u-section-4 .u-container-layout-4 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-icon-2 {
    margin-top: -37px;
  }

  .u-section-4 .u-text-4 {
    margin-top: 17px;
  }

  .u-section-4 .u-container-layout-6 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-text-6 {
    margin-top: 20px;
  }
}

@media (max-width: 575px) {
  .u-section-4 .u-sheet-1 {
    min-height: 1458px;
  }

  .u-section-4 .u-list-1 {
    margin-bottom: -4437px;
  }

  .u-section-4 .u-group-1 {
    min-height: 466px;
    width: 100%;
  }

  .u-section-4 .u-container-layout-2 {
    padding: 20px;
  }

  .u-section-4 .u-group-2 {
    min-height: 466px;
    width: 100%;
  }

  .u-section-4 .u-container-layout-4 {
    padding: 20px;
  }

  .u-section-4 .u-group-3 {
    min-height: 466px;
    width: 100%;
  }

  .u-section-4 .u-container-layout-6 {
    padding: 20px;
  }
}

.u-section-4 .u-icon-3,
.u-section-4 .u-icon-3:before,
.u-section-4 .u-icon-3 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-4 .u-icon-3.u-icon-3.u-icon-3:hover {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-4 .u-icon-3.u-icon-3.u-icon-3.hover {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-4 .u-icon-1,
.u-section-4 .u-icon-1:before,
.u-section-4 .u-icon-1 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-4 .u-icon-1.u-icon-1.u-icon-1:hover {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-4 .u-icon-1.u-icon-1.u-icon-1.hover {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-4 .u-icon-2,
.u-section-4 .u-icon-2:before,
.u-section-4 .u-icon-2 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-4 .u-icon-2.u-icon-2.u-icon-2:hover {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-4 .u-icon-2.u-icon-2.u-icon-2.hover {
  transform: translateX(0px) translateY(-5px) !important;
}