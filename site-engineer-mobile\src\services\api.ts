// API service for Site Engineer Mobile Application

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000/api';

export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

class ApiService {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
    this.token = localStorage.getItem('auth_token');
  }

  setToken(token: string) {
    this.token = token;
    localStorage.setItem('auth_token', token);
  }

  clearToken() {
    this.token = null;
    localStorage.removeItem('auth_token');
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    const url = `${this.baseURL}${endpoint}`;
    
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(url, {
        ...options,
        headers,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `HTTP error! status: ${response.status}`);
      }

      return {
        success: true,
        data: data.data || data,
        message: data.message,
      };
    } catch (error) {
      console.error('API request failed:', error);
      return {
        success: false,
        data: null as T,
        error: error instanceof Error ? error.message : 'Unknown error occurred',
      };
    }
  }

  // Authentication endpoints
  async login(email: string, password: string) {
    return this.request<{ user: any; token: string }>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password }),
    });
  }

  async logout() {
    const response = await this.request('/auth/logout', {
      method: 'POST',
    });
    this.clearToken();
    return response;
  }

  async refreshToken() {
    return this.request<{ token: string }>('/auth/refresh', {
      method: 'POST',
    });
  }

  // Worker endpoints
  async getMyWorkers(engineerId: string) {
    return this.request(`/engineers/${engineerId}/workers`);
  }

  async submitOvertimeRequest(workerId: string, overtimeData: any) {
    return this.request(`/workers/${workerId}/overtime`, {
      method: 'POST',
      body: JSON.stringify(overtimeData),
    });
  }

  // Task endpoints
  async getMyTasks(engineerId: string) {
    return this.request(`/engineers/${engineerId}/tasks`);
  }

  async createTask(taskData: any) {
    return this.request('/tasks', {
      method: 'POST',
      body: JSON.stringify(taskData),
    });
  }

  async updateTask(taskId: string, updates: any) {
    return this.request(`/tasks/${taskId}`, {
      method: 'PUT',
      body: JSON.stringify(updates),
    });
  }

  async updateTaskProgress(taskId: string, progress: any) {
    return this.request(`/tasks/${taskId}/progress`, {
      method: 'PUT',
      body: JSON.stringify(progress),
    });
  }

  // Permit endpoints
  async getMyPermits(engineerId: string) {
    return this.request(`/engineers/${engineerId}/permits`);
  }

  async submitPermitRequest(permitData: any) {
    return this.request('/permits/request', {
      method: 'POST',
      body: JSON.stringify(permitData),
    });
  }

  async getPermitStatus(permitId: string) {
    return this.request(`/permits/${permitId}/status`);
  }

  // Report endpoints
  async submitDailyReport(reportData: any) {
    return this.request('/reports/daily', {
      method: 'POST',
      body: JSON.stringify(reportData),
    });
  }

  async submitIssueReport(issueData: any) {
    return this.request('/reports/issues', {
      method: 'POST',
      body: JSON.stringify(issueData),
    });
  }

  async getMyReports(engineerId: string) {
    return this.request(`/reports/my-reports?engineerId=${engineerId}`);
  }

  // File upload endpoint
  async uploadFile(file: File, type: 'photo' | 'document' = 'photo') {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', type);

    const headers: HeadersInit = {};
    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    try {
      const response = await fetch(`${this.baseURL}/upload`, {
        method: 'POST',
        headers,
        body: formData,
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.message || `Upload failed! status: ${response.status}`);
      }

      return {
        success: true,
        data: data.data || data,
        message: data.message,
      };
    } catch (error) {
      console.error('File upload failed:', error);
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Upload failed',
      };
    }
  }
}

// Create and export a singleton instance
export const apiService = new ApiService();
export default apiService;
