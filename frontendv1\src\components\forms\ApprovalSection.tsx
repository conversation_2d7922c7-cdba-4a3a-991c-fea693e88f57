import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ircle, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';

interface ApprovalField {
  name: string;
  type: 'text' | 'signature' | 'time' | 'date';
  required?: boolean;
}

interface ApprovalData {
  [key: string]: any;
}

interface ApprovalSectionProps {
  title: string;
  description?: string;
  data: ApprovalData[];
  tableHeaders: ApprovalField[];
  onDataChange: (rowIndex: number, field: string, value: any) => void;
  onAddRow: () => void;
  onRemoveRow: (rowIndex: number) => void;
  additionalFields?: ApprovalField[];
  additionalData?: { [key: string]: any };
  onAdditionalFieldChange?: (field: string, value: any) => void;
  isRequired?: boolean;
  showValidationErrors?: boolean;
}

const ApprovalSection: React.FC<ApprovalSectionProps> = ({
  title,
  description,
  data,
  tableHeaders,
  onDataChange,
  onAddRow,
  onRemoveRow,
  additionalFields,
  additionalData,
  onAdditionalFieldChange,
  isRequired = true,
  showValidationErrors = false
}) => {
  
  // Validation logic
  const getValidationErrors = () => {
    const errors: string[] = [];
    
    // Check if at least one row has required fields filled
    const hasValidApprover = data.some(row => {
      return tableHeaders.filter(header => header.required).every(header => {
        const value = row[header.name];
        return value && value.toString().trim() !== '';
      });
    });
    
    if (!hasValidApprover && isRequired) {
      errors.push('At least one complete approval is required');
    }
    
    // Check additional required fields
    if (additionalFields && onAdditionalFieldChange) {
      additionalFields.filter(field => field.required).forEach(field => {
        const value = additionalData?.[field.name];
        if (!value || value.toString().trim() === '') {
          errors.push(`${field.name} is required`);
        }
      });
    }
    
    return errors;
  };

  const validationErrors = getValidationErrors();
  const hasErrors = showValidationErrors && validationErrors.length > 0;

  const renderField = (field: ApprovalField, value: any, onChange: (value: any) => void) => {
    const baseClasses = `w-full px-2 py-1 text-sm border rounded-md focus:outline-none focus:ring-2 ${
      hasErrors 
        ? 'border-red-300 focus:ring-red-500 focus:border-red-500' 
        : 'border-gray-300 focus:ring-blue-500 focus:border-blue-500'
    }`;

    switch (field.type) {
      case 'signature':
        return (
          <input
            type="text"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            placeholder="Digital signature"
            className={baseClasses}
            required={field.required}
          />
        );
      case 'time':
        return (
          <input
            type="time"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            className={baseClasses}
            required={field.required}
          />
        );
      case 'date':
        return (
          <input
            type="date"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            className={baseClasses}
            required={field.required}
          />
        );
      default:
        return (
          <input
            type="text"
            value={value || ''}
            onChange={(e) => onChange(e.target.value)}
            className={baseClasses}
            required={field.required}
          />
        );
    }
  };

  return (
    <div className={`rounded-lg border-2 p-4 ${
      hasErrors 
        ? 'border-red-300 bg-red-50' 
        : 'border-orange-300 bg-gradient-to-r from-orange-50 to-yellow-50'
    }`}>
      {/* Header with prominent styling */}
      <div className="flex items-center space-x-3 mb-4">
        <div className={`p-2 rounded-full ${
          hasErrors ? 'bg-red-100' : 'bg-orange-100'
        }`}>
          {hasErrors ? (
            <AlertTriangle className="h-6 w-6 text-red-600" />
          ) : (
            <Shield className="h-6 w-6 text-orange-600" />
          )}
        </div>
        <div className="flex-1">
          <h3 className={`text-lg font-bold ${
            hasErrors ? 'text-red-800' : 'text-orange-800'
          }`}>
            {title}
            {isRequired && <span className="text-red-600 ml-1">*</span>}
          </h3>
          {description && (
            <p className={`text-sm mt-1 ${
              hasErrors ? 'text-red-700' : 'text-orange-700'
            }`}>
              {description}
            </p>
          )}
        </div>
        {!hasErrors && (
          <div className="flex items-center space-x-1 text-orange-600">
            <UserCheck className="h-5 w-5" />
            <span className="text-sm font-medium">Required</span>
          </div>
        )}
      </div>

      {/* Validation Errors */}
      {hasErrors && (
        <div className="mb-4 p-3 bg-red-100 border border-red-300 rounded-md">
          <div className="flex items-center space-x-2 mb-2">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <span className="text-sm font-medium text-red-800">Approval Required</span>
          </div>
          <ul className="text-sm text-red-700 space-y-1">
            {validationErrors.map((error, index) => (
              <li key={index}>• {error}</li>
            ))}
          </ul>
        </div>
      )}

      {/* Additional Fields */}
      {additionalFields && additionalFields.length > 0 && (
        <div className="mb-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {additionalFields.map((field, fieldIndex) => (
              <div key={fieldIndex}>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  {field.name}
                  {field.required && <span className="text-red-500 ml-1">*</span>}
                </label>
                {renderField(field, additionalData?.[field.name], (value) => 
                  onAdditionalFieldChange?.(field.name, value)
                )}
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Approval Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full border border-gray-300 rounded-md">
          <thead className={hasErrors ? 'bg-red-100' : 'bg-orange-100'}>
            <tr>
              <th className="px-3 py-2 text-left text-sm font-medium text-gray-700 border-r border-gray-300">
                #
              </th>
              {tableHeaders.map((header, headerIndex) => (
                <th key={headerIndex} className="px-3 py-2 text-left text-sm font-medium text-gray-700 border-r border-gray-300">
                  {header.name}
                  {header.required && <span className="text-red-500 ml-1">*</span>}
                </th>
              ))}
              <th className="px-3 py-2 text-left text-sm font-medium text-gray-700">
                Action
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map((row, rowIndex) => (
              <tr key={rowIndex} className="hover:bg-gray-50">
                <td className="px-3 py-2 whitespace-nowrap text-sm text-gray-900 border-r border-gray-300 font-medium">
                  {rowIndex + 1}
                </td>
                {tableHeaders.map((header, headerIndex) => (
                  <td key={headerIndex} className="px-3 py-2 whitespace-nowrap border-r border-gray-300">
                    {renderField(header, row[header.name], (value) => 
                      onDataChange(rowIndex, header.name, value)
                    )}
                  </td>
                ))}
                <td className="px-3 py-2 whitespace-nowrap">
                  <button
                    onClick={() => onRemoveRow(rowIndex)}
                    className="text-red-600 hover:text-red-800 p-1 rounded"
                    disabled={data.length <= 1}
                    title="Remove row"
                  >
                    <X className="h-4 w-4" />
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Add Row Button */}
      <div className="mt-3 flex justify-between items-center">
        <button
          onClick={onAddRow}
          className={`px-4 py-2 text-sm font-medium rounded-md border ${
            hasErrors
              ? 'border-red-300 text-red-700 bg-red-100 hover:bg-red-200'
              : 'border-orange-300 text-orange-700 bg-orange-100 hover:bg-orange-200'
          }`}
        >
          Add Approver
        </button>
        
        {!hasErrors && data.some(row => 
          tableHeaders.filter(h => h.required).every(h => row[h.name] && row[h.name].toString().trim())
        ) && (
          <div className="flex items-center space-x-2 text-green-600">
            <CheckCircle className="h-4 w-4" />
            <span className="text-sm font-medium">Approval Complete</span>
          </div>
        )}
      </div>
    </div>
  );
};

export default ApprovalSection;
