 .u-section-1 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-1 .u-sheet-1 {
  min-height: 281px;
}

.u-section-1 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: -169px;
}

.u-section-1 .u-layout-cell-1 {
  min-height: 261px;
}

.u-section-1 .u-container-layout-1 {
  padding: 0;
}

.u-section-1 .u-text-1 {
  margin: 0 900px 0 0;
}

.u-section-1 .u-text-2 {
  margin: 10px 234px 0 0;
}

.u-section-1 .u-text-3 {
  font-weight: 300;
  margin: 20px 234px 0 0;
}

@media (max-width: 1199px) {
  .u-section-1 .u-sheet-1 {
    min-height: 285px;
  }

  .u-section-1 .u-layout-wrap-1 {
    margin-bottom: 13px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 265px;
  }

  .u-section-1 .u-text-1 {
    margin-right: 700px;
  }

  .u-section-1 .u-text-2 {
    margin-right: 34px;
  }

  .u-section-1 .u-text-3 {
    margin-right: 34px;
  }
}

@media (max-width: 991px) {
  .u-section-1 .u-sheet-1 {
    min-height: 94px;
  }

  .u-section-1 .u-layout-wrap-1 {
    margin-bottom: 20px;
    position: relative;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 356px;
  }

  .u-section-1 .u-text-1 {
    margin-right: 480px;
  }

  .u-section-1 .u-text-2 {
    margin-right: 0;
  }

  .u-section-1 .u-text-3 {
    margin-right: 0;
  }
}

@media (max-width: 767px) {
  .u-section-1 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-1 .u-text-1 {
    margin-right: 240px;
  }
}

@media (max-width: 575px) {
  .u-section-1 .u-text-1 {
    margin-right: 40px;
  }
} .u-section-2 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-2 .u-sheet-1 {
  min-height: 1317px;
}

.u-section-2 .u-list-1 {
  margin-bottom: 20px;
  margin-top: 20px;
}

.u-section-2 .u-repeater-1 {
  grid-auto-columns: calc(50% - 10px);
  grid-template-columns: repeat(2, calc(50% - 10px));
  min-height: 1277px;
  --gap: 20px;
}

.u-section-2 .u-list-item-1 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(253, 247, 250, 1), rgba(253, 247, 250, 1));
  background-size: cover;
  --radius: 20px;
  box-shadow: 5px 5px 8px 0 rgba(224,229,235,0.4);
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-container-layout-1 {
  padding: 20px;
}

.u-section-2 .u-image-1 {
  height: 341px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 20px;
}

.u-section-2 .u-text-1 {
  margin-top: 30px;
  margin-bottom: 0;
}

.u-section-2 .u-text-2 {
  margin-left: 0;
  margin-right: auto;
  margin-bottom: 0;
  font-weight: 300;
}

.u-section-2 .u-btn-1 {
  margin: 20px 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-2 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(253, 247, 250, 1), rgba(253, 247, 250, 1));
  background-size: cover;
  --radius: 20px;
  box-shadow: 5px 5px 8px 0 rgba(224,229,235,0.4);
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-container-layout-2 {
  padding: 20px;
}

.u-section-2 .u-image-2 {
  height: 341px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 20px;
}

.u-section-2 .u-text-3 {
  margin-top: 30px;
  margin-bottom: 0;
}

.u-section-2 .u-text-4 {
  margin-left: 0;
  margin-right: auto;
  margin-bottom: 0;
  font-weight: 300;
}

.u-section-2 .u-btn-2 {
  margin: 20px 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-3 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(253, 247, 250, 1), rgba(253, 247, 250, 1));
  background-size: cover;
  --radius: 20px;
  box-shadow: 5px 5px 8px 0 rgba(224,229,235,0.4);
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-container-layout-3 {
  padding: 20px;
}

.u-section-2 .u-image-3 {
  height: 341px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 20px;
}

.u-section-2 .u-text-5 {
  margin-top: 30px;
  margin-bottom: 0;
}

.u-section-2 .u-text-6 {
  margin-left: 0;
  margin-right: auto;
  margin-bottom: 0;
  font-weight: 300;
}

.u-section-2 .u-btn-3 {
  margin: 20px 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-4 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(253, 247, 250, 1), rgba(253, 247, 250, 1));
  background-size: cover;
  --radius: 20px;
  box-shadow: 5px 5px 8px 0 rgba(224,229,235,0.4);
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-container-layout-4 {
  padding: 20px;
}

.u-section-2 .u-image-4 {
  height: 341px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 20px;
}

.u-section-2 .u-text-7 {
  margin-top: 30px;
  margin-bottom: 0;
}

.u-section-2 .u-text-8 {
  margin-left: 0;
  margin-right: auto;
  margin-bottom: 0;
  font-weight: 300;
}

.u-section-2 .u-btn-4 {
  margin: 20px 0 0;
  padding: 0;
}

@media (max-width: 1199px) {
  .u-section-2 .u-sheet-1 {
    min-height: 1209px;
  }

  .u-section-2 .u-list-1 {
    margin-bottom: -88px;
  }

  .u-section-2 .u-repeater-1 {
    min-height: 1064px;
    grid-gap: 20px;
  }

  .u-section-2 .u-image-1 {
    height: 280px;
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-2 {
    height: 280px;
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-3 {
    height: 280px;
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-4 {
    height: 280px;
    transition-duration: 0.5s;
  }
}

@media (max-width: 991px) {
  .u-section-2 .u-sheet-1 {
    min-height: 2543px;
  }

  .u-section-2 .u-repeater-1 {
    grid-auto-columns: calc(100% + 0px);
    grid-template-columns: 100%;
    min-height: 2503px;
  }

  .u-section-2 .u-image-1 {
    height: 347px;
  }

  .u-section-2 .u-image-2 {
    height: 347px;
  }

  .u-section-2 .u-image-3 {
    height: 347px;
  }

  .u-section-2 .u-image-4 {
    height: 347px;
  }
}

@media (max-width: 767px) {
  .u-section-2 .u-sheet-1 {
    min-height: 2076px;
  }

  .u-section-2 .u-list-1 {
    margin-bottom: -407px;
  }

  .u-section-2 .u-repeater-1 {
    grid-auto-columns: 100%;
    min-height: 1976px;
  }

  .u-section-2 .u-image-1 {
    height: 238px;
  }

  .u-section-2 .u-image-2 {
    height: 238px;
  }

  .u-section-2 .u-image-3 {
    height: 238px;
  }

  .u-section-2 .u-image-4 {
    height: 238px;
  }
}

@media (max-width: 575px) {
  .u-section-2 .u-sheet-1 {
    min-height: 2308px;
  }

  .u-section-2 .u-list-1 {
    margin-bottom: -44px;
  }

  .u-section-2 .u-repeater-1 {
    min-height: 2224px;
  }

  .u-section-2 .u-image-1 {
    height: 237px;
  }

  .u-section-2 .u-image-2 {
    height: 237px;
  }

  .u-section-2 .u-image-3 {
    height: 237px;
  }

  .u-section-2 .u-image-4 {
    height: 237px;
  }
}

.u-section-2 .u-list-item-1,
.u-section-2 .u-list-item-1:before,
.u-section-2 .u-list-item-1 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 .u-list-item-1.u-list-item-1.u-list-item-1:hover {
  transform: translateX(0px) translateY(-5px) !important;
  border-color: #ffffff !important;
}

.u-section-2 .u-list-item-1:before {
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)) !important;
}

.u-section-2 .u-list-item-1.u-list-item-1.u-list-item-1.hover {
  transform: translateX(0px) translateY(-5px) !important;
  border-color: #ffffff !important;
}

.u-section-2 .u-list-item-1:before {
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)) !important;
}

.u-section-2 .u-list-item-2,
.u-section-2 .u-list-item-2:before,
.u-section-2 .u-list-item-2 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 .u-list-item-2.u-list-item-2.u-list-item-2:hover {
  transform: translateX(0px) translateY(-5px) !important;
  border-color: #ffffff !important;
}

.u-section-2 .u-list-item-2:before {
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)) !important;
}

.u-section-2 .u-list-item-2.u-list-item-2.u-list-item-2.hover {
  transform: translateX(0px) translateY(-5px) !important;
  border-color: #ffffff !important;
}

.u-section-2 .u-list-item-2:before {
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)) !important;
}

.u-section-2 .u-list-item-3,
.u-section-2 .u-list-item-3:before,
.u-section-2 .u-list-item-3 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 .u-list-item-3.u-list-item-3.u-list-item-3:hover {
  transform: translateX(0px) translateY(-5px) !important;
  border-color: #ffffff !important;
}

.u-section-2 .u-list-item-3:before {
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)) !important;
}

.u-section-2 .u-list-item-3.u-list-item-3.u-list-item-3.hover {
  transform: translateX(0px) translateY(-5px) !important;
  border-color: #ffffff !important;
}

.u-section-2 .u-list-item-3:before {
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)) !important;
}

.u-section-2 .u-list-item-4,
.u-section-2 .u-list-item-4:before,
.u-section-2 .u-list-item-4 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 .u-list-item-4.u-list-item-4.u-list-item-4:hover {
  transform: translateX(0px) translateY(-5px) !important;
  border-color: #ffffff !important;
}

.u-section-2 .u-list-item-4:before {
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)) !important;
}

.u-section-2 .u-list-item-4.u-list-item-4.u-list-item-4.hover {
  transform: translateX(0px) translateY(-5px) !important;
  border-color: #ffffff !important;
}

.u-section-2 .u-list-item-4:before {
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)) !important;
} .u-section-3 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-3 .u-sheet-1 {
  min-height: 1317px;
}

.u-section-3 .u-list-1 {
  margin-bottom: 20px;
  margin-top: 20px;
}

.u-section-3 .u-repeater-1 {
  grid-auto-columns: calc(50% - 10px);
  grid-template-columns: repeat(2, calc(50% - 10px));
  min-height: 1277px;
  --gap: 20px;
}

.u-section-3 .u-list-item-1 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(253, 247, 250, 1), rgba(253, 247, 250, 1));
  background-size: cover;
  --radius: 20px;
  box-shadow: 5px 5px 8px 0 rgba(224,229,235,0.4);
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-3 .u-container-layout-1 {
  padding: 20px;
}

.u-section-3 .u-image-1 {
  height: 341px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 20px;
}

.u-section-3 .u-text-1 {
  margin-top: 30px;
  margin-bottom: 0;
}

.u-section-3 .u-text-2 {
  margin-left: 0;
  margin-right: auto;
  margin-bottom: 0;
  font-weight: 300;
}

.u-section-3 .u-btn-1 {
  margin: 20px 0 0;
  padding: 0;
}

.u-section-3 .u-list-item-2 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(253, 247, 250, 1), rgba(253, 247, 250, 1));
  background-size: cover;
  --radius: 20px;
  box-shadow: 5px 5px 8px 0 rgba(224,229,235,0.4);
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-3 .u-container-layout-2 {
  padding: 20px;
}

.u-section-3 .u-image-2 {
  height: 341px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 20px;
}

.u-section-3 .u-text-3 {
  margin-top: 30px;
  margin-bottom: 0;
}

.u-section-3 .u-text-4 {
  margin-left: 0;
  margin-right: auto;
  margin-bottom: 0;
  font-weight: 300;
}

.u-section-3 .u-btn-2 {
  margin: 20px 0 0;
  padding: 0;
}

.u-section-3 .u-list-item-3 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(253, 247, 250, 1), rgba(253, 247, 250, 1));
  background-size: cover;
  --radius: 20px;
  box-shadow: 5px 5px 8px 0 rgba(224,229,235,0.4);
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-3 .u-container-layout-3 {
  padding: 20px;
}

.u-section-3 .u-image-3 {
  height: 341px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 20px;
}

.u-section-3 .u-text-5 {
  margin-top: 30px;
  margin-bottom: 0;
}

.u-section-3 .u-text-6 {
  margin-left: 0;
  margin-right: auto;
  margin-bottom: 0;
  font-weight: 300;
}

.u-section-3 .u-btn-3 {
  margin: 20px 0 0;
  padding: 0;
}

@media (max-width: 1199px) {
  .u-section-3 .u-sheet-1 {
    min-height: 1151px;
  }

  .u-section-3 .u-list-1 {
    margin-bottom: -146px;
  }

  .u-section-3 .u-repeater-1 {
    min-height: 1064px;
    grid-gap: 20px;
  }

  .u-section-3 .u-image-1 {
    height: 280px;
    transition-duration: 0.5s;
  }

  .u-section-3 .u-image-2 {
    height: 280px;
    transition-duration: 0.5s;
  }

  .u-section-3 .u-image-3 {
    height: 280px;
    transition-duration: 0.5s;
  }
}

@media (max-width: 991px) {
  .u-section-3 .u-sheet-1 {
    min-height: 1817px;
  }

  .u-section-3 .u-list-1 {
    margin-bottom: -872px;
  }

  .u-section-3 .u-repeater-1 {
    grid-auto-columns: calc(100% + 0px);
    grid-template-columns: 100%;
  }

  .u-section-3 .u-image-1 {
    height: 347px;
  }

  .u-section-3 .u-image-2 {
    height: 347px;
  }

  .u-section-3 .u-image-3 {
    height: 347px;
  }
}

@media (max-width: 767px) {
  .u-section-3 .u-sheet-1 {
    min-height: 1518px;
  }

  .u-section-3 .u-list-1 {
    margin-bottom: -965px;
  }

  .u-section-3 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-3 .u-image-1 {
    height: 238px;
  }

  .u-section-3 .u-image-2 {
    height: 238px;
  }

  .u-section-3 .u-image-3 {
    height: 238px;
  }
}

@media (max-width: 575px) {
  .u-section-3 .u-sheet-1 {
    min-height: 1672px;
  }

  .u-section-3 .u-list-1 {
    margin-bottom: -1464px;
  }

  .u-section-3 .u-image-1 {
    height: 237px;
  }

  .u-section-3 .u-image-2 {
    height: 237px;
  }

  .u-section-3 .u-image-3 {
    height: 237px;
  }
}

.u-section-3 .u-list-item-1,
.u-section-3 .u-list-item-1:before,
.u-section-3 .u-list-item-1 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 .u-list-item-1.u-list-item-1.u-list-item-1:hover {
  transform: translateX(0px) translateY(-5px) !important;
  border-color: #ffffff !important;
}

.u-section-3 .u-list-item-1:before {
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)) !important;
}

.u-section-3 .u-list-item-1.u-list-item-1.u-list-item-1.hover {
  transform: translateX(0px) translateY(-5px) !important;
  border-color: #ffffff !important;
}

.u-section-3 .u-list-item-1:before {
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)) !important;
}

.u-section-3 .u-list-item-2,
.u-section-3 .u-list-item-2:before,
.u-section-3 .u-list-item-2 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 .u-list-item-2.u-list-item-2.u-list-item-2:hover {
  transform: translateX(0px) translateY(-5px) !important;
  border-color: #ffffff !important;
}

.u-section-3 .u-list-item-2:before {
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)) !important;
}

.u-section-3 .u-list-item-2.u-list-item-2.u-list-item-2.hover {
  transform: translateX(0px) translateY(-5px) !important;
  border-color: #ffffff !important;
}

.u-section-3 .u-list-item-2:before {
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)) !important;
}

.u-section-3 .u-list-item-3,
.u-section-3 .u-list-item-3:before,
.u-section-3 .u-list-item-3 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 .u-list-item-3.u-list-item-3.u-list-item-3:hover {
  transform: translateX(0px) translateY(-5px) !important;
  border-color: #ffffff !important;
}

.u-section-3 .u-list-item-3:before {
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)) !important;
}

.u-section-3 .u-list-item-3.u-list-item-3.u-list-item-3.hover {
  transform: translateX(0px) translateY(-5px) !important;
  border-color: #ffffff !important;
}

.u-section-3 .u-list-item-3:before {
  background-image: linear-gradient(to bottom, rgba(255, 255, 255, 1), rgba(255, 255, 255, 1)) !important;
} .u-section-4 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-4 .u-sheet-1 {
  min-height: 236px;
}

.u-section-4 .u-group-1 {
  --radius: 30px;
  min-height: 140px;
  margin-top: 40px;
  margin-bottom: 60px;
  height: auto;
}

.u-section-4 .u-container-layout-1 {
  padding: 30px;
}

.u-section-4 .u-text-1 {
  font-size: 2.25rem;
  font-family: Handlee;
  margin: 18px auto 0 50px;
}

.u-section-4 .u-btn-1 {
  margin: -33px 50px 0 auto;
  padding: 0;
}

@media (max-width: 1199px) {
  .u-section-4 .u-group-1 {
    height: auto;
  }

  .u-section-4 .u-btn-1 {
    margin-right: 0;
  }
}

@media (max-width: 767px) {
  .u-section-4 .u-sheet-1 {
    min-height: 283px;
  }

  .u-section-4 .u-group-1 {
    margin-bottom: 15px;
    min-height: 183px;
  }

  .u-section-4 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-text-1 {
    width: auto;
    margin-top: 50px;
    margin-left: 25px;
  }

  .u-section-4 .u-btn-1 {
    margin-top: -11px;
    margin-right: 33px;
  }
}

@media (max-width: 575px) {
  .u-section-4 .u-sheet-1 {
    min-height: 293px;
  }

  .u-section-4 .u-group-1 {
    margin-bottom: 33px;
    min-height: 189px;
  }

  .u-section-4 .u-container-layout-1 {
    padding: 20px;
  }

  .u-section-4 .u-text-1 {
    margin-top: 0;
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-4 .u-btn-1 {
    margin-top: 0;
    margin-right: 0;
  }
}