/**
 * Hero Section Interactive Slideshow
 * Manages background image transitions, service content updates, and navigation controls
 */

document.addEventListener('DOMContentLoaded', () => {
    // Hero data configuration - contains all slide information
    const heroData = [
        {
            bgImageUrl: "images/hero-section/hero-eni.webp",
            serviceName: "Preliminary Front-End Engineering and Design", // Full name for Pre-FEED
            serviceImage: "images/services/prefeed1.webp",
            serviceUrl: "service/pre-feed.html"
        },
        {
            bgImageUrl: "images/hero-section/hero-small4x.webp",
            serviceName: "Front-End Engineering and Design", // Full name for FEED
            serviceImage: "images/services/feed.webp",
            serviceUrl: "service/feed.html"
        },
        {
            bgImageUrl: "images/hero-section/hero-cafe4x.webp",
            serviceName: "Preconstruction / Early Contractor Engagement", // Updated full name
            serviceImage: "images/services/precon1.webp",
            serviceUrl: "service/pre-construction.html"
        },
        {
            bgImageUrl: "images/hero-gowgate2x.webp",
            serviceName: "Construction Services",
            serviceImage: "images/services/cons1.webp",
            serviceUrl: "service/construction.html"
        },
        {
            bgImageUrl: "images/hero-jkuat4x.webp",
            serviceName: "Procurement Services",
            serviceImage: "images/services/procure1.webp",
            serviceUrl: "service/procurement.html"
        },
        {
            bgImageUrl: "images/hero-ldc4x.webp",
            serviceName: "Construction Management Services", // Added "Services"
            serviceImage: "images/services/manage1.webp",
            serviceUrl: "service/construction-management.html"
        },
        {
            bgImageUrl: "images/hero-roof4x.webp",
            serviceName: "Facilities Maintenance Services", // Added "Services"
            serviceImage: "images/services/maintain1.webp",
            serviceUrl: "service/facilities-maintenance.html"
        }
    ];

    // DOM element references
    const slidesContainer = document.querySelector('.hero__background-slides');
    const serviceNameElement = document.getElementById('hero__service-name');
    const serviceImageElement = document.getElementById('hero__service-image'); // Now dynamic
    const serviceLinkElement = document.getElementById('hero__service-link'); // Service link element

    const dotsNavigation = document.querySelector('.hero__dots-navigation');
    const prevButton = document.querySelector('.hero__control-button--prev');
    const nextButton = document.querySelector('.hero__control-button--next');

    // Slideshow state management
    let currentSlideIndex = 0;
    let slideInterval;
    const SLIDE_INTERVAL_DURATION = 5000; // 5 seconds between auto-transitions

    // Initialize slides and navigation dots
    heroData.forEach((data, index) => {
        // Create background slide element
        const slide = document.createElement('div');
        slide.classList.add('hero__background-slide');
        slide.style.backgroundImage = `url('${data.bgImageUrl}')`;
        slide.setAttribute('aria-hidden', index === 0 ? 'false' : 'true');
        slide.setAttribute('role', 'tabpanel');
        slide.id = `hero-slide-${index + 1}`;
        slidesContainer.appendChild(slide);

        // Create navigation dot
        const dot = document.createElement('button');
        dot.classList.add('hero__dot');
        dot.setAttribute('type', 'button');
        dot.setAttribute('aria-label', `Go to slide ${index + 1}`);
        dot.setAttribute('aria-controls', `hero-slide-${index + 1}`);
        if (index === 0) {
            dot.classList.add('hero__dot--active');
            dot.setAttribute('aria-selected', 'true');
        }
        dot.addEventListener('click', () => {
            goToSlide(index);
            resetSlideInterval();
        });
        dotsNavigation.appendChild(dot);
    });

    // Get references to dynamically created elements
    const slideElements = slidesContainer.querySelectorAll('.hero__background-slide');
    const dotElements = dotsNavigation.querySelectorAll('.hero__dot');

    /**
     * Updates the service content (name, image, link) based on current slide
     * @param {number} index - Index of the current slide
     */
    function updateSlideContent(index) {
        serviceNameElement.textContent = heroData[index].serviceName;
        serviceImageElement.src = heroData[index].serviceImage;
        serviceImageElement.alt = `${heroData[index].serviceName} illustration`;
        serviceLinkElement.href = heroData[index].serviceUrl; // Update the link URL
    }

    /**
     * Shows the specified slide and updates all related UI elements
     * @param {number} index - Index of the slide to show
     */
    function showSlide(index) {
        // Hide all slides and deactivate all dots
        slideElements.forEach((slide, i) => {
            slide.classList.remove('hero__background-slide--active');
            slide.setAttribute('aria-hidden', 'true');
            dotElements[i].classList.remove('hero__dot--active');
            dotElements[i].setAttribute('aria-selected', 'false');
        });

        // Show current slide and activate corresponding dot
        slideElements[index].classList.add('hero__background-slide--active');
        slideElements[index].setAttribute('aria-hidden', 'false');
        dotElements[index].classList.add('hero__dot--active');
        dotElements[index].setAttribute('aria-selected', 'true');

        // Update service content
        updateSlideContent(index);
        currentSlideIndex = index;
    }

    /**
     * Advances to the next slide (with wraparound)
     */
    function nextSlideFn() { // Renamed to avoid conflict with element 'nextSlide'
        let newIndex = (currentSlideIndex + 1) % heroData.length;
        showSlide(newIndex);
    }

    /**
     * Goes back to the previous slide (with wraparound)
     */
    function prevSlideFn() { // Renamed
        let newIndex = (currentSlideIndex - 1 + heroData.length) % heroData.length;
        showSlide(newIndex);
    }

    /**
     * Jumps directly to a specific slide
     * @param {number} index - Index of the slide to show
     */
    function goToSlide(index) {
        showSlide(index);
    }

    /**
     * Starts the automatic slideshow interval
     */
    function startSlideInterval() {
        slideInterval = setInterval(nextSlideFn, SLIDE_INTERVAL_DURATION);
    }

    /**
     * Resets the automatic slideshow interval (useful when user interacts)
     */
    function resetSlideInterval() {
        clearInterval(slideInterval);
        startSlideInterval();
    }

    // Event listeners for navigation controls
    prevButton.addEventListener('click', () => {
        prevSlideFn();
        resetSlideInterval();
    });

    nextButton.addEventListener('click', () => {
        nextSlideFn();
        resetSlideInterval();
    });

    // Initialize the slideshow
    showSlide(0); // Show first slide
    startSlideInterval(); // Start automatic transitions
});
