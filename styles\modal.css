/* Modal Styles */
.modal {
  /* Hidden by default */
  display: none;
  
  /* Fixed position, covers entire viewport */
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  
  /* High z-index to appear above everything */
  z-index: 1000;
  
  /* Semi-transparent background */
  background-color: rgba(0, 0, 0, 0.7);
  
  /* Enable scrolling for tall modals */
  overflow-y: auto;
}

.modal.open {
  display: block;
}

.modal-content {
  /* White background and spacing */
  background-color: #fff;
  margin: 5vh auto;
  padding: 2rem;
  border-radius: 8px;
  
  /* Width constraints */
  width: 90%;
  max-width: 1200px;
  
  /* Box shadow for depth */
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  
  /* Prevent content overflow */
  position: relative;
}

.close-modal {
  position: absolute;
  top: 1rem;
  right: 1.5rem;
  font-size: 28px;
  font-weight: bold;
  color: #333;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-modal:hover {
  color: #84277F;
}

/* Lock body scrolling when modal is open */
body.modal-open {
  overflow: hidden;
}

/* Modal Navigation */
.modal-navigation {
  display: flex;
  justify-content: space-between;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.nav-btn {
  display: flex;
  align-items: center;
  background-color: #F9F0F8;
  color: #84277F;
  border: 1px solid #84277F;
  border-radius: 10px;
  padding: 0.5rem 1rem;
  font-family: 'Figtree', sans-serif;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.nav-btn:hover {
  background-color: #84277F;
  color: #fff;
}

.nav-arrow {
  font-size: 18px;
  margin: 0 0.5rem;
}

.prev-btn .nav-arrow {
  margin-right: 0.5rem;
}

.next-btn .nav-arrow {
  margin-left: 0.5rem;
}

/* Project Attributes Styling */
.modal-attributes {
  margin: 1.5rem 0;
  background-color: #f8f8f8;
  border-radius: 10px;
  padding: 1rem;
}

.attribute-labels {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1rem;
  margin-bottom: 0.5rem;
}

.attribute-values {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 1rem;
}

.attribute-label {
  color: #777777;
  font-size: 14px;
  font-weight: 500;
  text-transform: uppercase;
}

.attribute-value {
  color: #333333;
  font-size: 16px;
  font-weight: 600;
}

/* Responsive styles */
@media (max-width: 768px) {
  .attribute-labels,
  .attribute-values {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }
  
  .attribute-label {
    margin-top: 0.5rem;
    border-bottom: 1px solid #eee;
    padding-bottom: 0.25rem;
  }
  
  .attribute-value {
    margin-bottom: 0.5rem;
  }
  
  .nav-text {
    display: none;
  }
  
  .nav-btn {
    padding: 0.5rem;
  }
  
  .nav-arrow {
    margin: 0;
  }
}
