import { useState, useEffect, createContext, useContext } from 'react';
import { SiteEngineer, AuthContextType } from '../types';
import { apiService } from '../services/api';

// Mock user data for development
const mockUser: SiteEngineer = {
  id: 'engineer-1',
  name: '<PERSON>',
  email: '<EMAIL>',
  phone: '+254712345678',
  employeeId: 'ENG001',
  siteId: 'site-1',
  siteName: 'Riverside Residential Complex',
  role: 'site-engineer',
  permissions: [
    'view_workers',
    'submit_overtime',
    'create_tasks',
    'request_permits',
    'submit_reports'
  ],
  supervisedWorkers: ['worker-1', 'worker-2', 'worker-3', 'worker-4'],
  createdAt: new Date('2023-01-15')
};

// Create Auth Context
export const AuthContext = createContext<AuthContextType | null>(null);

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

// Auth hook implementation
export const useAuthState = (): AuthContextType => {
  const [user, setUser] = useState<SiteEngineer | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check for existing token on app start
    const token = localStorage.getItem('auth_token');
    if (token) {
      // In a real app, you would validate the token with the server
      // For now, we'll use mock data
      setUser(mockUser);
      apiService.setToken(token);
    }
    setLoading(false);
  }, []);

  const login = async (email: string, password: string): Promise<void> => {
    setLoading(true);
    try {
      // In development, we'll simulate a successful login
      if (email === '<EMAIL>' && password === 'password') {
        const mockToken = 'mock-jwt-token-' + Date.now();
        apiService.setToken(mockToken);
        setUser(mockUser);
      } else {
        throw new Error('Invalid credentials');
      }

      // Real implementation would be:
      // const response = await apiService.login(email, password);
      // if (response.success) {
      //   apiService.setToken(response.data.token);
      //   setUser(response.data.user);
      // } else {
      //   throw new Error(response.error || 'Login failed');
      // }
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  const logout = async (): Promise<void> => {
    setLoading(true);
    try {
      await apiService.logout();
      setUser(null);
    } catch (error) {
      console.error('Logout failed:', error);
      // Even if logout fails on server, clear local state
      setUser(null);
      apiService.clearToken();
    } finally {
      setLoading(false);
    }
  };

  return {
    user,
    isAuthenticated: !!user,
    login,
    logout,
    loading,
  };
};
