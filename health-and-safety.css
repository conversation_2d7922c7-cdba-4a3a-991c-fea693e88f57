 .u-section-1 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-1 .u-sheet-1 {
  min-height: 912px;
}

.u-section-1 .u-text-1 {
  margin: 20px 0 0;
}

.u-section-1 .u-text-2 {
  font-weight: 500;
  margin: 20px 0 0;
}

.u-section-1 .u-layout-wrap-1 {
  margin-top: 50px;
  margin-bottom: 20px;
  --radius: 20px;
}

.u-section-1 .u-layout-cell-1 {
  min-height: 740px;
  --radius: 20px;
}

.u-section-1 .u-container-layout-1 {
  padding: 30px;
}

.u-section-1 .u-image-1 {
  width: 752px;
  height: 472px;
  --radius: 20px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  margin: 0;
}

.u-section-1 .u-text-3 {
  font-weight: 500;
  font-size: 1.5rem;
  margin: 30px 35px 0 0;
}

.u-section-1 .u-text-4 {
  font-size: 1rem;
  margin: 17px 10px 0 0;
}

.u-section-1 .u-layout-cell-2 {
  min-height: 392px;
  --radius: 30px;
}

.u-section-1 .u-container-layout-2 {
  padding: 20px;
}

.u-section-1 .u-image-2 {
  height: 343px;
  --radius: 20px;
  transition-duration: 0.5s;
  width: 324px;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  margin: 0 auto;
}

.u-section-1 .u-layout-cell-3 {
  min-height: 348px;
  --radius: 20px;
}

.u-section-1 .u-container-layout-3 {
  padding: 20px;
}

.u-section-1 .u-image-3 {
  height: 198px;
  --radius: 20px;
  transition-duration: 0.5s;
  width: 324px;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  margin: 0 auto;
}

.u-section-1 .u-text-5 {
  font-size: 1rem;
  margin: 30px auto 0;
}

@media (max-width: 1199px) {
  .u-section-1 .u-sheet-1 {
    min-height: 638px;
  }

  .u-section-1 .u-layout-wrap-1 {
    position: relative;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 744px;
    background-position: 50% 50%;
  }

  .u-section-1 .u-image-1 {
    height: 337px;
    margin-right: initial;
    margin-left: initial;
    width: auto;
  }

  .u-section-1 .u-text-3 {
    width: auto;
    margin-top: 20px;
    margin-right: 0;
  }

  .u-section-1 .u-text-4 {
    width: auto;
    margin-top: 18px;
    margin-right: 0;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 327px;
  }

  .u-section-1 .u-image-2 {
    margin-right: initial;
    margin-left: initial;
    width: auto;
  }

  .u-section-1 .u-layout-cell-3 {
    min-height: 290px;
  }

  .u-section-1 .u-image-3 {
    margin-right: initial;
    margin-left: initial;
    width: auto;
  }
}

@media (max-width: 991px) {
  .u-section-1 .u-sheet-1 {
    min-height: 531px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-1 .u-image-1 {
    height: 350px;
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 255px;
  }

  .u-section-1 .u-layout-cell-3 {
    min-height: 100px;
  }
}

@media (max-width: 767px) {
  .u-section-1 .u-sheet-1 {
    min-height: 774px;
  }

  .u-section-1 .u-container-layout-1 {
    padding: 20px;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 622px;
  }

  .u-section-1 .u-image-2 {
    height: 566px;
    width: auto;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-1 .u-image-3 {
    height: 398px;
    width: auto;
    margin-right: initial;
    margin-left: initial;
  }
}

@media (max-width: 575px) {
  .u-section-1 .u-sheet-1 {
    min-height: 583px;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 392px;
  }

  .u-section-1 .u-image-2 {
    height: 418px;
    width: auto;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-1 .u-image-3 {
    height: 242px;
    width: auto;
    margin-right: initial;
    margin-left: initial;
  }
}

.u-section-1 .u-image-3,
.u-section-1 .u-image-3:before,
.u-section-1 .u-image-3 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-1 :hover > .u-container-layout .u-image-3 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-1 .hover > .u-container-layout .u-image-3 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-block-dbdf-49:not([data-block-selected]):not([data-cell-selected]),
.u-block-dbdf-49:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-dbdf-49:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

:hover:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout > .u-block-dbdf-49 {
  transform: translateX(0px) translateY(-5px) !important;
}

.hover > .u-container-layout > .u-block-dbdf-49 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-1 .u-image-1,
.u-section-1 .u-image-1:before,
.u-section-1 .u-image-1 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-1 :hover > .u-container-layout .u-image-1 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-1 .hover > .u-container-layout .u-image-1 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-1 .u-image-2,
.u-section-1 .u-image-2:before,
.u-section-1 .u-image-2 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-1 :hover > .u-container-layout .u-image-2 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-1 .hover > .u-container-layout .u-image-2 {
  transform: translateX(0px) translateY(-5px) !important;
} .u-section-2 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-2 .u-sheet-1 {
  min-height: 634px;
}

.u-section-2 .u-text-1 {
  letter-spacing: 2px;
  margin: 20px 600px 0 0;
}

.u-section-2 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: 40px;
}

.u-section-2 .u-layout-cell-1 {
  min-height: 255px;
  --radius: 20px;
}

.u-section-2 .u-container-layout-1 {
  box-shadow: 5px 5px 8px 0 rgba(204,211,219,0.4);
  padding: 30px;
}

.u-section-2 .u-text-2 {
  font-size: 2.25rem;
  font-weight: 600;
  margin: 50px 20px 0 0;
}

.u-section-2 .u-text-3 {
  font-size: 1.5rem;
  font-weight: 500;
  font-family: Figtree;
  margin: 10px 66px 0 0;
}

.u-section-2 .u-layout-cell-2 {
  min-height: 255px;
  --radius: 20px;
}

.u-section-2 .u-container-layout-2 {
  box-shadow: 5px 5px 8px 0 rgba(204,211,219,0.4);
  padding: 30px;
}

.u-section-2 .u-text-4 {
  font-size: 2.25rem;
  font-weight: 600;
  margin: 50px 20px 0 0;
}

.u-section-2 .u-text-5 {
  font-size: 1.5rem;
  font-weight: 500;
  font-family: Figtree;
  margin: 10px 0 0;
}

.u-section-2 .u-layout-cell-3 {
  min-height: 255px;
  --radius: 20px;
}

.u-section-2 .u-container-layout-3 {
  box-shadow: 5px 5px 8px 0 rgba(204,211,219,0.4);
  padding: 30px;
}

.u-section-2 .u-text-6 {
  font-size: 2.25rem;
  font-weight: 600;
  margin: 50px 20px 0 0;
}

.u-section-2 .u-text-7 {
  font-size: 1.5rem;
  font-weight: 500;
  font-family: Figtree;
  margin: 10px 20px 0 0;
}

.u-section-2 .u-layout-cell-4 {
  min-height: 251px;
  --radius: 20px;
}

.u-section-2 .u-container-layout-4 {
  box-shadow: 5px 5px 8px 0 rgba(204,211,219,0.4);
  padding: 29px 30px;
}

.u-section-2 .u-text-8 {
  font-size: 2.25rem;
  font-weight: 600;
  margin: 0;
}

.u-section-2 .u-text-9 {
  font-size: 1.5rem;
  font-weight: 500;
  font-family: Figtree;
  margin: 27px 20px 0 0;
}

.u-section-2 .u-layout-cell-5 {
  min-height: 246px;
  --radius: 20px;
}

.u-section-2 .u-container-layout-5 {
  box-shadow: 5px 5px 8px 0 rgba(204,211,219,0.4);
  padding: 30px;
}

.u-section-2 .u-text-10 {
  font-size: 2.25rem;
  font-weight: 600;
  margin: 0;
}

.u-section-2 .u-text-11 {
  font-size: 1.5rem;
  font-weight: 500;
  font-family: Figtree;
  margin: 8px 20px 0 0;
}

@media (max-width: 1199px) {
  .u-section-2 .u-sheet-1 {
    min-height: 734px;
  }

  .u-section-2 .u-text-1 {
    width: auto;
    margin-right: 247px;
  }

  .u-section-2 .u-layout-cell-1 {
    min-height: 213px;
  }

  .u-section-2 .u-text-2 {
    margin-right: 0;
  }

  .u-section-2 .u-text-3 {
    margin-right: 0;
  }

  .u-section-2 .u-layout-cell-2 {
    min-height: 213px;
  }

  .u-section-2 .u-text-4 {
    margin-right: 0;
  }

  .u-section-2 .u-layout-cell-3 {
    min-height: 213px;
  }

  .u-section-2 .u-text-6 {
    margin-right: 0;
  }

  .u-section-2 .u-text-7 {
    margin-right: 0;
  }

  .u-section-2 .u-layout-cell-4 {
    min-height: 333px;
  }

  .u-section-2 .u-text-8 {
    width: auto;
  }

  .u-section-2 .u-text-9 {
    margin-top: 40px;
    margin-right: 0;
  }

  .u-section-2 .u-layout-cell-5 {
    min-height: 205px;
  }

  .u-section-2 .u-text-11 {
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-2 .u-sheet-1 {
    min-height: 142px;
  }

  .u-section-2 .u-text-1 {
    margin-right: 27px;
  }

  .u-section-2 .u-layout-wrap-1 {
    position: relative;
  }

  .u-section-2 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-2 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-2 .u-layout-cell-3 {
    min-height: 100px;
  }

  .u-section-2 .u-layout-cell-4 {
    min-height: 100px;
  }

  .u-section-2 .u-layout-cell-5 {
    min-height: 100px;
  }
}

@media (max-width: 767px) {
  .u-section-2 .u-sheet-1 {
    min-height: 962px;
  }

  .u-section-2 .u-text-1 {
    margin-right: 0;
  }

  .u-section-2 .u-container-layout-1 {
    padding: 20px;
  }

  .u-section-2 .u-text-3 {
    font-size: 1.3333333333333333rem;
  }

  .u-section-2 .u-container-layout-2 {
    padding: 20px;
  }

  .u-section-2 .u-text-5 {
    font-size: 1.3333333333333333rem;
  }

  .u-section-2 .u-container-layout-3 {
    padding: 20px;
  }

  .u-section-2 .u-text-7 {
    font-size: 1.3333333333333333rem;
  }

  .u-section-2 .u-layout-cell-4 {
    min-height: 177px;
  }

  .u-section-2 .u-container-layout-4 {
    padding: 20px;
  }

  .u-section-2 .u-text-8 {
    margin-top: 50px;
  }

  .u-section-2 .u-text-9 {
    font-size: 1.3333333333333333rem;
    margin-top: 10px;
  }

  .u-section-2 .u-container-layout-5 {
    padding: 20px;
  }

  .u-section-2 .u-text-10 {
    margin-top: 50px;
  }

  .u-section-2 .u-text-11 {
    font-size: 1.3333333333333333rem;
  }
}

@media (max-width: 575px) {
  .u-section-2 .u-sheet-1 {
    min-height: 562px;
  }

  .u-section-2 .u-text-6 {
    width: auto;
  }

  .u-section-2 .u-layout-cell-4 {
    min-height: 100px;
  }
} .u-section-3 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-3 .u-sheet-1 {
  min-height: 503px;
}

.u-section-3 .u-text-1 {
  letter-spacing: 2px;
  margin: 20px 0 0;
}

.u-section-3 .u-layout-wrap-1 {
  width: 100%;
  margin: 20px 0 60px;
}

.u-section-3 .u-layout-cell-1 {
  min-height: 310px;
  --radius: 20px;
}

.u-section-3 .u-container-layout-1 {
  box-shadow: 5px 5px 8px 0 rgba(224,229,235,0.4);
  padding: 10px;
}

.u-section-3 .u-text-2 {
  font-weight: 500;
  margin: 0;
}

.u-section-3 .u-text-3 {
  font-size: 1rem;
  font-weight: 300;
  margin: 20px 0 0;
}

.u-section-3 .u-layout-cell-2 {
  min-height: 310px;
  background-repeat: no-repeat;
  background-size: cover;
  --radius: 20px;
}

.u-section-3 .u-image-1 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: url("images/safety9.webp");
  background-size: cover;
}

.u-section-3 .u-container-layout-2 {
  padding: 30px;
}

.u-section-3 .u-layout-cell-3 {
  min-height: 310px;
  --radius: 20px;
}

.u-section-3 .u-container-layout-3 {
  box-shadow: 5px 5px 8px 0 rgba(224,229,235,0.4);
  padding: 10px;
}

.u-section-3 .u-text-4 {
  font-weight: 500;
  margin: 49px 0 0;
}

.u-section-3 .u-text-5 {
  font-size: 1rem;
  font-weight: 300;
  margin: 20px 0 0;
}

.u-section-3 .u-layout-cell-4 {
  min-height: 310px;
  background-repeat: no-repeat;
  background-size: cover;
  --radius: 20px;
}

.u-section-3 .u-image-2 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: url("images/hselaxmi4.webp");
  background-size: cover;
}

.u-section-3 .u-container-layout-4 {
  padding: 30px;
}

.u-section-3 .u-layout-cell-5 {
  min-height: 310px;
  background-repeat: no-repeat;
  background-size: cover;
  --radius: 20px;
}

.u-section-3 .u-image-3 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: url("images/hselaxmi7.webp");
  background-size: cover;
}

.u-section-3 .u-container-layout-5 {
  padding: 30px;
}

.u-section-3 .u-layout-cell-6 {
  min-height: 310px;
  --radius: 20px;
}

.u-section-3 .u-container-layout-6 {
  box-shadow: 5px 5px 8px 0 rgba(224,229,235,0.4);
  padding: 10px;
}

.u-section-3 .u-text-6 {
  font-weight: 500;
  margin: 49px 0 0;
}

.u-section-3 .u-text-7 {
  font-size: 1rem;
  font-weight: 300;
  margin: 20px 0 0;
}

.u-section-3 .u-layout-cell-7 {
  min-height: 310px;
  background-repeat: no-repeat;
  background-size: cover;
  --radius: 20px;
}

.u-section-3 .u-image-4 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: url("images/safety1.webp");
  background-size: cover;
}

.u-section-3 .u-container-layout-7 {
  padding: 30px;
}

.u-section-3 .u-layout-cell-8 {
  min-height: 325px;
  --radius: 20px;
}

.u-section-3 .u-container-layout-8 {
  box-shadow: 5px 5px 8px 0 rgba(224,229,235,0.4);
  padding: 10px;
}

.u-section-3 .u-text-8 {
  font-weight: 500;
  margin: 49px 0 0;
}

.u-section-3 .u-text-9 {
  font-size: 1rem;
  font-weight: 300;
  margin: 20px 0 0;
}

@media (max-width: 1199px) {
  .u-section-3 .u-layout-wrap-1 {
    position: relative;
  }

  .u-section-3 .u-layout-cell-2 {
    min-height: 167px;
    background-position: 50% 50%;
  }

  .u-section-3 .u-layout-cell-4 {
    min-height: 167px;
    background-position: 50% 50%;
  }

  .u-section-3 .u-layout-cell-5 {
    background-position: 50% 50%;
  }

  .u-section-3 .u-layout-cell-7 {
    background-position: 50% 50%;
  }
}

@media (max-width: 991px) {
  .u-section-3 .u-sheet-1 {
    min-height: 965px;
  }

  .u-section-3 .u-text-1 {
    margin-top: 236px;
  }

  .u-section-3 .u-layout-wrap-1 {
    margin: -249px auto -1494px -20px;
  }

  .u-section-3 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-3 .u-layout-cell-2 {
    min-height: 130px;
  }

  .u-section-3 .u-layout-cell-3 {
    min-height: 100px;
  }

  .u-section-3 .u-layout-cell-4 {
    min-height: 130px;
  }

  .u-section-3 .u-layout-cell-5 {
    min-height: 229px;
  }

  .u-section-3 .u-layout-cell-6 {
    min-height: 229px;
  }

  .u-section-3 .u-text-6 {
    width: auto;
    margin-top: 36px;
    margin-left: 2px;
    margin-right: 2px;
  }

  .u-section-3 .u-text-7 {
    width: auto;
    margin-left: 2px;
    margin-right: 2px;
  }

  .u-section-3 .u-layout-cell-7 {
    min-height: 257px;
  }

  .u-section-3 .u-layout-cell-8 {
    min-height: 257px;
  }

  .u-section-3 .u-text-8 {
    width: auto;
    margin-top: 19px;
    margin-left: 2px;
    margin-right: 2px;
  }

  .u-section-3 .u-text-9 {
    width: auto;
    margin-left: 2px;
    margin-right: 2px;
  }
}

@media (max-width: 767px) {
  .u-section-3 .u-sheet-1 {
    min-height: 2219px;
  }

  .u-section-3 .u-text-1 {
    margin-top: 20px;
  }

  .u-section-3 .u-layout-wrap-1 {
    margin-top: 20px;
    margin-bottom: -33px;
    margin-right: initial;
    margin-left: initial;
    width: auto;
  }

  .u-section-3 .u-container-layout-1 {
    padding: 20px;
  }

  .u-section-3 .u-text-2 {
    margin-top: 50px;
  }

  .u-section-3 .u-layout-cell-2 {
    min-height: 298px;
  }

  .u-section-3 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-3 .u-container-layout-3 {
    padding: 20px;
  }

  .u-section-3 .u-layout-cell-4 {
    min-height: 298px;
  }

  .u-section-3 .u-container-layout-4 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-3 .u-layout-cell-5 {
    min-height: 299px;
  }

  .u-section-3 .u-container-layout-5 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-3 .u-layout-cell-6 {
    min-height: 100px;
  }

  .u-section-3 .u-container-layout-6 {
    padding: 20px;
  }

  .u-section-3 .u-text-6 {
    margin-top: 50px;
  }

  .u-section-3 .u-layout-cell-7 {
    min-height: 298px;
  }

  .u-section-3 .u-container-layout-7 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-3 .u-layout-cell-8 {
    min-height: 100px;
  }

  .u-section-3 .u-container-layout-8 {
    padding: 20px;
  }

  .u-section-3 .u-text-8 {
    margin-top: 50px;
  }
}

@media (max-width: 575px) {
  .u-section-3 .u-sheet-1 {
    min-height: 2203px;
  }

  .u-section-3 .u-text-1 {
    margin-top: 30px;
  }

  .u-section-3 .u-layout-wrap-1 {
    margin-bottom: -2723px;
    width: auto;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-3 .u-layout-cell-2 {
    min-height: 218px;
  }

  .u-section-3 .u-layout-cell-4 {
    min-height: 218px;
  }

  .u-section-3 .u-layout-cell-5 {
    min-height: 219px;
  }

  .u-section-3 .u-layout-cell-7 {
    min-height: 218px;
  }
} .u-section-4 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-4 .u-sheet-1 {
  min-height: 533px;
}

.u-section-4 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: -144px;
}

.u-section-4 .u-image-1 {
  min-height: 493px;
  background-image: url("images/golden-rules.png");
  --radius: 30px;
  background-position: 50% 50%;
}

.u-section-4 .u-container-layout-1 {
  padding: 0;
}

.u-section-4 .u-layout-cell-2 {
  min-height: 493px;
}

.u-section-4 .u-container-layout-2 {
  padding: 10px 30px 30px;
}

.u-section-4 .u-text-1 {
  letter-spacing: 2px;
  font-size: 1.5rem;
  margin: 0;
}

.u-section-4 .u-text-2 {
  font-size: 1rem;
  font-weight: 300;
  margin: 20px 0 0;
}

@media (max-width: 1199px) {
  .u-section-4 .u-sheet-1 {
    min-height: 697px;
  }

  .u-section-4 .u-image-1 {
    min-height: 411px;
  }

  .u-section-4 .u-layout-cell-2 {
    min-height: 411px;
  }
}

@media (max-width: 991px) {
  .u-section-4 .u-layout-wrap-1 {
    margin-top: 30px;
    margin-bottom: -356px;
  }

  .u-section-4 .u-image-1 {
    min-height: 440px;
  }

  .u-section-4 .u-layout-cell-2 {
    min-height: 197px;
  }

  .u-section-4 .u-container-layout-2 {
    padding-bottom: 0;
  }
}

@media (max-width: 767px) {
  .u-section-4 .u-sheet-1 {
    min-height: 618px;
  }

  .u-section-4 .u-layout-wrap-1 {
    margin-top: -153px;
    margin-bottom: -153px;
  }

  .u-section-4 .u-image-1 {
    min-height: 333px;
  }

  .u-section-4 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-4 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-4 .u-sheet-1 {
    min-height: 556px;
  }

  .u-section-4 .u-layout-wrap-1 {
    margin-top: 16px;
    margin-bottom: 16px;
  }

  .u-section-4 .u-image-1 {
    min-height: 210px;
  }
} .u-section-5 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-5 .u-sheet-1 {
  min-height: 520px;
}

.u-section-5 .u-text-1 {
  margin: 20px 0 0;
}

.u-section-5 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: 10px;
}

.u-section-5 .u-layout-cell-1 {
  min-height: 431px;
  --radius: 30px;
}

.u-section-5 .u-container-layout-1 {
  padding: 30px 24px;
}

.u-section-5 .u-text-2 {
  margin: 0 6px;
}

.u-section-5 .u-text-3 {
  font-weight: 300;
  margin: 244px 0 0;
}

.u-section-5 .u-layout-cell-2 {
  min-height: 431px;
}

.u-section-5 .u-container-layout-2 {
  padding: 0;
}

.u-section-5 .u-blog-1 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-5 .u-repeater-1 {
  grid-template-columns: repeat(2, calc(50% - 10px));
  min-height: 180px;
  grid-auto-columns: calc(50% - 10px);
  --gap: 20px;
}

.u-section-5 .u-repeater-item-1 {
  background-image: none;
  --radius: 20px;
}

.u-section-5 .u-container-layout-3 {
  padding: 30px 0;
}

.u-section-5 .u-image-1 {
  height: 249px;
  margin-top: -30px;
  margin-bottom: 0;
  --top-left-radius: 20px;
  --top-right-radius: 20px;
}

.u-section-5 .u-text-4 {
  font-size: 1.125rem;
  margin: 20px 20px 0;
}

.u-section-5 .u-btn-1 {
  background-image: none;
  border-style: solid;
  margin: 20px auto 0 20px;
  padding: 0;
}

.u-section-5 .u-repeater-item-2 {
  --radius: 20px;
}

.u-section-5 .u-container-layout-4 {
  padding: 30px 0;
}

.u-section-5 .u-image-2 {
  height: 249px;
  margin-top: -30px;
  margin-bottom: 0;
  --top-left-radius: 20px;
  --top-right-radius: 20px;
}

.u-section-5 .u-text-5 {
  font-size: 1.125rem;
  margin: 20px 20px 0;
}

.u-section-5 .u-btn-2 {
  background-image: none;
  border-style: solid;
  margin: 20px auto 0 20px;
  padding: 0;
}

.u-section-5 .u-repeater-item-3 {
  --radius: 20px;
}

.u-section-5 .u-container-layout-5 {
  padding: 30px 0;
}

.u-section-5 .u-image-3 {
  height: 249px;
  margin-top: -30px;
  margin-bottom: 0;
  --top-left-radius: 20px;
  --top-right-radius: 20px;
}

.u-section-5 .u-text-6 {
  font-size: 1.125rem;
  margin: 20px 20px 0;
}

.u-section-5 .u-btn-3 {
  background-image: none;
  border-style: solid;
  margin: 20px auto 0 20px;
  padding: 0;
}

.u-section-5 .u-gallery-nav-1 {
  position: absolute;
  left: 10px;
  width: 40px;
  height: 40px;
}

.u-section-5 .u-gallery-nav-2 {
  position: absolute;
  right: 10px;
  width: 40px;
  height: 40px;
}

@media (max-width: 1199px) {
  .u-section-5 .u-layout-wrap-1 {
    margin-bottom: 60px;
  }

  .u-section-5 .u-layout-cell-1 {
    min-height: 401px;
  }

  .u-section-5 .u-text-3 {
    width: auto;
    margin-top: 183px;
  }

  .u-section-5 .u-layout-cell-2 {
    min-height: 394px;
  }

  .u-section-5 .u-repeater-1 {
    min-height: 381px;
    grid-gap: 20px;
  }

  .u-section-5 .u-container-layout-3 {
    padding-top: 0;
    padding-bottom: 0;
  }

  .u-section-5 .u-image-1 {
    height: 207px;
    margin-top: 0;
  }

  .u-section-5 .u-text-4 {
    width: auto;
  }

  .u-section-5 .u-container-layout-4 {
    padding-top: 0;
    padding-bottom: 0;
  }

  .u-section-5 .u-image-2 {
    height: 207px;
    margin-top: 0;
  }

  .u-section-5 .u-text-5 {
    width: auto;
  }

  .u-section-5 .u-container-layout-5 {
    padding-top: 0;
    padding-bottom: 0;
  }

  .u-section-5 .u-image-3 {
    height: 207px;
    margin-top: 0;
  }

  .u-section-5 .u-text-6 {
    width: auto;
  }
}

@media (max-width: 991px) {
  .u-section-5 .u-sheet-1 {
    min-height: 139px;
  }

  .u-section-5 .u-layout-wrap-1 {
    position: relative;
  }

  .u-section-5 .u-layout-cell-1 {
    min-height: 414px;
  }

  .u-section-5 .u-text-2 {
    width: auto;
    margin-right: auto;
    margin-left: 0;
  }

  .u-section-5 .u-text-3 {
    margin-top: 218px;
  }

  .u-section-5 .u-layout-cell-2 {
    min-height: 406px;
  }

  .u-section-5 .u-repeater-1 {
    min-height: 394px;
    grid-auto-columns: calc(100% + 0px);
    grid-template-columns: 100%;
  }

  .u-section-5 .u-image-1 {
    height: 250px;
    width: 100%;
  }

  .u-section-5 .u-image-2 {
    height: 250px;
    width: 100%;
  }

  .u-section-5 .u-image-3 {
    height: 250px;
    width: 100%;
  }
}

@media (max-width: 767px) {
  .u-section-5 .u-sheet-1 {
    min-height: 239px;
  }

  .u-section-5 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-5 .u-container-layout-1 {
    padding-left: 30px;
    padding-right: 30px;
  }

  .u-section-5 .u-layout-cell-2 {
    min-height: 461px;
  }

  .u-section-5 .u-repeater-1 {
    min-height: 441px;
    grid-auto-columns: 100%;
  }
}

@media (max-width: 575px) {
  .u-section-5 .u-layout-cell-1 {
    min-height: 300px;
  }

  .u-section-5 .u-text-3 {
    margin-top: 145px;
  }

  .u-section-5 .u-layout-cell-2 {
    min-height: 385px;
  }

  .u-section-5 .u-repeater-1 {
    min-height: 365px;
  }

  .u-section-5 .u-image-1 {
    height: 200px;
  }

  .u-section-5 .u-image-2 {
    height: 200px;
  }

  .u-section-5 .u-image-3 {
    height: 200px;
  }
}