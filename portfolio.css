 .u-section-1 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-1 .u-sheet-1 {
  min-height: 69px;
}

.u-section-1 .u-text-1 {
  margin-top: 20px;
  margin-right: 960px;
  margin-bottom: -76px;
}

@media (max-width: 1199px) {
  .u-section-1 .u-text-1 {
    margin-right: 760px;
    margin-bottom: -183px;
    margin-left: 0;
  }
}

@media (max-width: 991px) {
  .u-section-1 .u-text-1 {
    margin-right: 540px;
    margin-bottom: -244px;
  }
}

@media (max-width: 767px) {
  .u-section-1 .u-sheet-1 {
    min-height: 66px;
  }

  .u-section-1 .u-text-1 {
    margin-right: 300px;
    margin-bottom: -408px;
  }
}

@media (max-width: 575px) {
  .u-section-1 .u-text-1 {
    margin-right: 100px;
  }
} .u-section-2 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-2 .u-sheet-1 {
  min-height: 870px;
}

.u-section-2 .u-tabs-1 {
  min-height: 750px;
  margin-top: 20px;
  margin-bottom: -305px;
  height: auto;
}

.u-section-2 .u-tab-item-1 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-tab-link-1 {
  font-size: 1.25rem;
  font-family: Figtree;
  --radius: 10px;
  background-image: none;
  padding: 10px 20px;
}

.u-section-2 .u-tab-item-2 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-tab-link-2 {
  font-size: 1.25rem;
  font-family: Figtree;
  --radius: 10px;
  background-image: none;
  padding: 10px 20px;
}

.u-section-2 .u-tab-item-3 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-tab-link-3 {
  font-size: 1.25rem;
  font-family: Figtree;
  --radius: 10px;
  background-image: none;
  padding: 10px 20px;
}

.u-section-2 .u-tab-item-4 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-tab-link-4 {
  font-size: 1.25rem;
  font-family: Figtree;
  --radius: 10px;
  background-image: none;
  padding: 10px 20px;
}

.u-section-2 .u-tab-item-5 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-tab-link-5 {
  font-size: 1.25rem;
  font-family: Figtree;
  --radius: 10px;
  background-image: none;
  padding: 10px 20px;
}

.u-section-2 .u-tab-item-6 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-tab-link-6 {
  font-size: 1.25rem;
  font-family: Figtree;
  --radius: 10px;
  background-image: none;
  padding: 10px 20px;
}

.u-section-2 .u-tab-item-7 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-tab-link-7 {
  font-size: 1.25rem;
  font-family: Figtree;
  --radius: 10px;
  background-image: none;
  padding: 10px 20px;
}

.u-section-2 .u-tab-item-8 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-tab-link-8 {
  font-size: 1.25rem;
  font-family: Figtree;
  --radius: 10px;
  background-image: none;
  padding: 10px 20px;
}

.u-section-2 .u-tab-pane-1 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-container-layout-1 {
  padding: 10px 0;
}

.u-section-2 .u-list-1 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 664px;
  grid-gap: 10px;
}

.u-section-2 .u-list-item-1 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-2 {
  padding: 10px;
}

.u-section-2 .u-image-1 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
}

.u-section-2 .u-text-1 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-1 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-2 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-3 {
  padding: 10px;
}

.u-section-2 .u-image-2 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
}

.u-section-2 .u-text-2 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-2 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-3 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-4 {
  padding: 10px;
}

.u-section-2 .u-image-3 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
}

.u-section-2 .u-text-3 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-3 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-4 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-5 {
  padding: 10px;
}

.u-section-2 .u-image-4 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
}

.u-section-2 .u-text-4 {
  font-size: 1.25rem;
  font-weight: 400;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-4 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-5 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-6 {
  padding: 10px;
}

.u-section-2 .u-image-5 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
}

.u-section-2 .u-text-5 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-5 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-6 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-7 {
  padding: 10px;
}

.u-section-2 .u-image-6 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
}

.u-section-2 .u-text-6 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-6 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-tab-pane-2 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-container-layout-8 {
  padding: 10px 0;
}

.u-section-2 .u-list-2 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-repeater-2 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 664px;
  grid-gap: 10px;
}

.u-section-2 .u-list-item-7 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-9 {
  padding: 10px;
}

.u-section-2 .u-image-7 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
}

.u-section-2 .u-text-7 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-7 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-8 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-10 {
  padding: 10px;
}

.u-section-2 .u-image-8 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
}

.u-section-2 .u-text-8 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-8 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-9 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-11 {
  padding: 10px;
}

.u-section-2 .u-image-9 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
}

.u-section-2 .u-text-9 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-9 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-10 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-12 {
  padding: 10px;
}

.u-section-2 .u-image-10 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
}

.u-section-2 .u-text-10 {
  font-size: 1.25rem;
  font-weight: 400;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-10 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-11 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-13 {
  padding: 10px;
}

.u-section-2 .u-image-11 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
}

.u-section-2 .u-text-11 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-11 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-12 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-14 {
  padding: 10px;
}

.u-section-2 .u-image-12 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
}

.u-section-2 .u-text-12 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-12 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-tab-pane-3 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-container-layout-15 {
  padding: 10px 0;
}

.u-section-2 .u-list-3 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-repeater-3 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 664px;
  grid-gap: 10px;
}

.u-section-2 .u-list-item-13 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-16 {
  padding: 10px;
}

.u-section-2 .u-image-13 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-13 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-13 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-14 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-17 {
  padding: 10px;
}

.u-section-2 .u-image-14 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-14 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-14 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-15 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-18 {
  padding: 10px;
}

.u-section-2 .u-image-15 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-15 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-15 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-16 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-19 {
  padding: 10px;
}

.u-section-2 .u-image-16 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-16 {
  font-size: 1.25rem;
  font-weight: 400;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-16 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-17 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-20 {
  padding: 10px;
}

.u-section-2 .u-image-17 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-17 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-17 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-18 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-21 {
  padding: 10px;
}

.u-section-2 .u-image-18 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-18 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-18 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-tab-pane-4 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-container-layout-22 {
  padding: 10px 0;
}

.u-section-2 .u-list-4 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-repeater-4 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 664px;
  grid-gap: 10px;
}

.u-section-2 .u-list-item-19 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-23 {
  padding: 10px;
}

.u-section-2 .u-image-19 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-19 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-19 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-20 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-24 {
  padding: 10px;
}

.u-section-2 .u-image-20 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-20 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-20 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-21 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-25 {
  padding: 10px;
}

.u-section-2 .u-image-21 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-21 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-21 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-22 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-26 {
  padding: 10px;
}

.u-section-2 .u-image-22 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-22 {
  font-size: 1.25rem;
  font-weight: 400;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-22 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-23 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-27 {
  padding: 10px;
}

.u-section-2 .u-image-23 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-23 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-23 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-24 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-28 {
  padding: 10px;
}

.u-section-2 .u-image-24 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-24 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-24 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-tab-pane-5 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-container-layout-29 {
  padding: 10px 0;
}

.u-section-2 .u-list-5 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-repeater-5 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 664px;
  grid-gap: 10px;
}

.u-section-2 .u-list-item-25 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-30 {
  padding: 10px;
}

.u-section-2 .u-image-25 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-25 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-25 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-26 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-31 {
  padding: 10px;
}

.u-section-2 .u-image-26 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-26 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-26 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-27 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-32 {
  padding: 10px;
}

.u-section-2 .u-image-27 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-27 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-27 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-28 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-33 {
  padding: 10px;
}

.u-section-2 .u-image-28 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-28 {
  font-size: 1.25rem;
  font-weight: 400;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-28 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-29 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-34 {
  padding: 10px;
}

.u-section-2 .u-image-29 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-29 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-29 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-30 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-35 {
  padding: 10px;
}

.u-section-2 .u-image-30 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-30 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-30 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-tab-pane-6 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-container-layout-36 {
  padding: 10px 0;
}

.u-section-2 .u-list-6 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-repeater-6 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 664px;
  grid-gap: 10px;
}

.u-section-2 .u-list-item-31 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-37 {
  padding: 10px;
}

.u-section-2 .u-image-31 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-31 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-31 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-32 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-38 {
  padding: 10px;
}

.u-section-2 .u-image-32 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-32 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-32 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-33 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-39 {
  padding: 10px;
}

.u-section-2 .u-image-33 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-33 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-33 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-34 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-40 {
  padding: 10px;
}

.u-section-2 .u-image-34 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-34 {
  font-size: 1.25rem;
  font-weight: 400;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-34 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-35 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-41 {
  padding: 10px;
}

.u-section-2 .u-image-35 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-35 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-35 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-36 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-42 {
  padding: 10px;
}

.u-section-2 .u-image-36 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-36 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-36 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-tab-pane-7 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-container-layout-43 {
  padding: 10px 0;
}

.u-section-2 .u-list-7 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-repeater-7 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 664px;
  grid-gap: 10px;
}

.u-section-2 .u-list-item-37 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-44 {
  padding: 10px;
}

.u-section-2 .u-image-37 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-37 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-37 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-38 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-45 {
  padding: 10px;
}

.u-section-2 .u-image-38 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-38 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-38 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-39 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-46 {
  padding: 10px;
}

.u-section-2 .u-image-39 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-39 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-39 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-40 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-47 {
  padding: 10px;
}

.u-section-2 .u-image-40 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-40 {
  font-size: 1.25rem;
  font-weight: 400;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-40 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-41 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-48 {
  padding: 10px;
}

.u-section-2 .u-image-41 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-41 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-41 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-42 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-49 {
  padding: 10px;
}

.u-section-2 .u-image-42 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-42 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-42 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-tab-pane-8 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-container-layout-50 {
  padding: 10px 0;
}

.u-section-2 .u-list-8 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-2 .u-repeater-8 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 664px;
  grid-gap: 10px;
}

.u-section-2 .u-list-item-43 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-51 {
  padding: 10px;
}

.u-section-2 .u-image-43 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-43 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-43 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-44 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-52 {
  padding: 10px;
}

.u-section-2 .u-image-44 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-44 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-44 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-45 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-53 {
  padding: 10px;
}

.u-section-2 .u-image-45 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-45 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-45 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-46 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-54 {
  padding: 10px;
}

.u-section-2 .u-image-46 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-46 {
  font-size: 1.25rem;
  font-weight: 400;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-46 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-47 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-55 {
  padding: 10px;
}

.u-section-2 .u-image-47 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-47 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-47 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

.u-section-2 .u-list-item-48 {
  box-shadow: 2px 0 0 0 rgba(0,0,0,0);
}

.u-section-2 .u-container-layout-56 {
  padding: 10px;
}

.u-section-2 .u-image-48 {
  height: 222px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-48 {
  font-weight: 400;
  font-size: 1.25rem;
  margin: 20px auto 0 0;
}

.u-section-2 .u-btn-48 {
  font-size: 0.875rem;
  margin: 20px auto 0 0;
  padding: 0;
}

@media (max-width: 1199px) {
  .u-section-2 .u-tab-link-1 {
    border-style: solid;
  }

  .u-section-2 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 553px;
  }

  .u-section-2 .u-list-item-1 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-1 {
    height: 183px;
    transition-duration: 0.5s;
    transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  }

  .u-section-2 .u-text-1 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-1 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-2 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-2 {
    height: 183px;
    transition-duration: 0.5s;
    transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  }

  .u-section-2 .u-text-2 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-2 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-3 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-3 {
    height: 183px;
    transition-duration: 0.5s;
    transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  }

  .u-section-2 .u-text-3 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-3 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-4 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-4 {
    height: 183px;
    transition-duration: 0.5s;
    transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  }

  .u-section-2 .u-text-4 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-4 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-5 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-5 {
    height: 183px;
    transition-duration: 0.5s;
    transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  }

  .u-section-2 .u-text-5 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-5 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-6 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-6 {
    height: 183px;
    transition-duration: 0.5s;
    transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  }

  .u-section-2 .u-text-6 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-6 {
    border-style: solid;
  }

  .u-section-2 .u-repeater-2 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 553px;
  }

  .u-section-2 .u-list-item-7 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-7 {
    height: 183px;
    transition-duration: 0.5s;
    transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  }

  .u-section-2 .u-text-7 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-7 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-8 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-8 {
    height: 183px;
    transition-duration: 0.5s;
    transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  }

  .u-section-2 .u-text-8 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-8 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-9 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-9 {
    height: 183px;
    transition-duration: 0.5s;
    transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  }

  .u-section-2 .u-text-9 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-9 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-10 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-10 {
    height: 183px;
    transition-duration: 0.5s;
    transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  }

  .u-section-2 .u-text-10 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-10 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-11 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-11 {
    height: 183px;
    transition-duration: 0.5s;
    transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  }

  .u-section-2 .u-text-11 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-11 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-12 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-12 {
    height: 183px;
    transition-duration: 0.5s;
    transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  }

  .u-section-2 .u-text-12 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-12 {
    border-style: solid;
  }

  .u-section-2 .u-repeater-3 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 553px;
  }

  .u-section-2 .u-list-item-13 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-13 {
    height: 183px;
  }

  .u-section-2 .u-text-13 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-13 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-14 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-14 {
    height: 183px;
  }

  .u-section-2 .u-text-14 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-14 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-15 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-15 {
    height: 183px;
  }

  .u-section-2 .u-text-15 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-15 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-16 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-16 {
    height: 183px;
  }

  .u-section-2 .u-text-16 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-16 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-17 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-17 {
    height: 183px;
  }

  .u-section-2 .u-text-17 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-17 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-18 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-18 {
    height: 183px;
  }

  .u-section-2 .u-text-18 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-18 {
    border-style: solid;
  }

  .u-section-2 .u-repeater-4 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 553px;
  }

  .u-section-2 .u-list-item-19 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-19 {
    height: 183px;
  }

  .u-section-2 .u-text-19 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-19 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-20 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-20 {
    height: 183px;
  }

  .u-section-2 .u-text-20 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-20 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-21 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-21 {
    height: 183px;
  }

  .u-section-2 .u-text-21 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-21 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-22 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-22 {
    height: 183px;
  }

  .u-section-2 .u-text-22 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-22 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-23 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-23 {
    height: 183px;
  }

  .u-section-2 .u-text-23 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-23 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-24 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-24 {
    height: 183px;
  }

  .u-section-2 .u-text-24 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-24 {
    border-style: solid;
  }

  .u-section-2 .u-repeater-5 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 553px;
  }

  .u-section-2 .u-list-item-25 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-25 {
    height: 183px;
  }

  .u-section-2 .u-text-25 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-25 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-26 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-26 {
    height: 183px;
  }

  .u-section-2 .u-text-26 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-26 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-27 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-27 {
    height: 183px;
  }

  .u-section-2 .u-text-27 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-27 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-28 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-28 {
    height: 183px;
  }

  .u-section-2 .u-text-28 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-28 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-29 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-29 {
    height: 183px;
  }

  .u-section-2 .u-text-29 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-29 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-30 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-30 {
    height: 183px;
  }

  .u-section-2 .u-text-30 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-30 {
    border-style: solid;
  }

  .u-section-2 .u-repeater-6 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 553px;
  }

  .u-section-2 .u-list-item-31 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-31 {
    height: 183px;
  }

  .u-section-2 .u-text-31 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-31 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-32 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-32 {
    height: 183px;
  }

  .u-section-2 .u-text-32 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-32 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-33 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-33 {
    height: 183px;
  }

  .u-section-2 .u-text-33 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-33 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-34 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-34 {
    height: 183px;
  }

  .u-section-2 .u-text-34 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-34 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-35 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-35 {
    height: 183px;
  }

  .u-section-2 .u-text-35 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-35 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-36 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-36 {
    height: 183px;
  }

  .u-section-2 .u-text-36 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-36 {
    border-style: solid;
  }

  .u-section-2 .u-repeater-7 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 553px;
  }

  .u-section-2 .u-list-item-37 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-37 {
    height: 183px;
  }

  .u-section-2 .u-text-37 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-37 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-38 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-38 {
    height: 183px;
  }

  .u-section-2 .u-text-38 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-38 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-39 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-39 {
    height: 183px;
  }

  .u-section-2 .u-text-39 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-39 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-40 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-40 {
    height: 183px;
  }

  .u-section-2 .u-text-40 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-40 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-41 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-41 {
    height: 183px;
  }

  .u-section-2 .u-text-41 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-41 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-42 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-42 {
    height: 183px;
  }

  .u-section-2 .u-text-42 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-42 {
    border-style: solid;
  }

  .u-section-2 .u-repeater-8 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 553px;
  }

  .u-section-2 .u-list-item-43 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-43 {
    height: 183px;
  }

  .u-section-2 .u-text-43 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-43 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-44 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-44 {
    height: 183px;
  }

  .u-section-2 .u-text-44 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-44 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-45 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-45 {
    height: 183px;
  }

  .u-section-2 .u-text-45 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-45 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-46 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-46 {
    height: 183px;
  }

  .u-section-2 .u-text-46 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-46 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-47 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-47 {
    height: 183px;
  }

  .u-section-2 .u-text-47 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-47 {
    border-style: solid;
  }

  .u-section-2 .u-list-item-48 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-image-48 {
    height: 183px;
  }

  .u-section-2 .u-text-48 {
    transition-duration: 0.5s;
  }

  .u-section-2 .u-btn-48 {
    border-style: solid;
  }
}

@media (max-width: 991px) {
  .u-section-2 .u-tabs-1 {
    margin-bottom: 100px;
  }

  .u-section-2 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 980px;
  }

  .u-section-2 .u-image-1 {
    height: 216px;
  }

  .u-section-2 .u-image-2 {
    height: 216px;
  }

  .u-section-2 .u-image-3 {
    height: 216px;
  }

  .u-section-2 .u-image-4 {
    height: 216px;
  }

  .u-section-2 .u-image-5 {
    height: 216px;
  }

  .u-section-2 .u-image-6 {
    height: 216px;
  }

  .u-section-2 .u-repeater-2 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 980px;
  }

  .u-section-2 .u-image-7 {
    height: 216px;
  }

  .u-section-2 .u-image-8 {
    height: 216px;
  }

  .u-section-2 .u-image-9 {
    height: 216px;
  }

  .u-section-2 .u-image-10 {
    height: 216px;
  }

  .u-section-2 .u-image-11 {
    height: 216px;
  }

  .u-section-2 .u-image-12 {
    height: 216px;
  }

  .u-section-2 .u-repeater-3 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 980px;
  }

  .u-section-2 .u-image-13 {
    height: 216px;
  }

  .u-section-2 .u-image-14 {
    height: 216px;
  }

  .u-section-2 .u-image-15 {
    height: 216px;
  }

  .u-section-2 .u-image-16 {
    height: 216px;
  }

  .u-section-2 .u-image-17 {
    height: 216px;
  }

  .u-section-2 .u-image-18 {
    height: 216px;
  }

  .u-section-2 .u-repeater-4 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 980px;
  }

  .u-section-2 .u-image-19 {
    height: 216px;
  }

  .u-section-2 .u-image-20 {
    height: 216px;
  }

  .u-section-2 .u-image-21 {
    height: 216px;
  }

  .u-section-2 .u-image-22 {
    height: 216px;
  }

  .u-section-2 .u-image-23 {
    height: 216px;
  }

  .u-section-2 .u-image-24 {
    height: 216px;
  }

  .u-section-2 .u-repeater-5 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 980px;
  }

  .u-section-2 .u-image-25 {
    height: 216px;
  }

  .u-section-2 .u-image-26 {
    height: 216px;
  }

  .u-section-2 .u-image-27 {
    height: 216px;
  }

  .u-section-2 .u-image-28 {
    height: 216px;
  }

  .u-section-2 .u-image-29 {
    height: 216px;
  }

  .u-section-2 .u-image-30 {
    height: 216px;
  }

  .u-section-2 .u-repeater-6 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 980px;
  }

  .u-section-2 .u-image-31 {
    height: 216px;
  }

  .u-section-2 .u-image-32 {
    height: 216px;
  }

  .u-section-2 .u-image-33 {
    height: 216px;
  }

  .u-section-2 .u-image-34 {
    height: 216px;
  }

  .u-section-2 .u-image-35 {
    height: 216px;
  }

  .u-section-2 .u-image-36 {
    height: 216px;
  }

  .u-section-2 .u-repeater-7 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 980px;
  }

  .u-section-2 .u-image-37 {
    height: 216px;
  }

  .u-section-2 .u-image-38 {
    height: 216px;
  }

  .u-section-2 .u-image-39 {
    height: 216px;
  }

  .u-section-2 .u-image-40 {
    height: 216px;
  }

  .u-section-2 .u-image-41 {
    height: 216px;
  }

  .u-section-2 .u-image-42 {
    height: 216px;
  }

  .u-section-2 .u-repeater-8 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 980px;
  }

  .u-section-2 .u-image-43 {
    height: 216px;
  }

  .u-section-2 .u-image-44 {
    height: 216px;
  }

  .u-section-2 .u-image-45 {
    height: 216px;
  }

  .u-section-2 .u-image-46 {
    height: 216px;
  }

  .u-section-2 .u-image-47 {
    height: 216px;
  }

  .u-section-2 .u-image-48 {
    height: 216px;
  }
}

@media (max-width: 767px) {
  .u-section-2 .u-tab-link-1 {
    font-size: 1rem;
  }

  .u-section-2 .u-tab-link-2 {
    font-size: 1.1111111111111112rem;
  }

  .u-section-2 .u-tab-link-3 {
    font-size: 1.1111111111111112rem;
  }

  .u-section-2 .u-tab-link-4 {
    font-size: 1.1111111111111112rem;
  }

  .u-section-2 .u-tab-link-5 {
    font-size: 1.1111111111111112rem;
  }

  .u-section-2 .u-tab-link-6 {
    font-size: 1.1111111111111112rem;
  }

  .u-section-2 .u-tab-link-7 {
    font-size: 1.1111111111111112rem;
  }

  .u-section-2 .u-tab-link-8 {
    font-size: 1.1111111111111112rem;
  }

  .u-section-2 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-2 .u-image-1 {
    height: 304px;
  }

  .u-section-2 .u-image-2 {
    height: 304px;
  }

  .u-section-2 .u-image-3 {
    height: 304px;
  }

  .u-section-2 .u-image-4 {
    height: 304px;
  }

  .u-section-2 .u-image-5 {
    height: 304px;
  }

  .u-section-2 .u-image-6 {
    height: 304px;
  }

  .u-section-2 .u-repeater-2 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-2 .u-image-7 {
    height: 304px;
  }

  .u-section-2 .u-image-8 {
    height: 304px;
  }

  .u-section-2 .u-image-9 {
    height: 304px;
  }

  .u-section-2 .u-image-10 {
    height: 304px;
  }

  .u-section-2 .u-image-11 {
    height: 304px;
  }

  .u-section-2 .u-image-12 {
    height: 304px;
  }

  .u-section-2 .u-repeater-3 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-2 .u-image-13 {
    height: 304px;
  }

  .u-section-2 .u-image-14 {
    height: 304px;
  }

  .u-section-2 .u-image-15 {
    height: 304px;
  }

  .u-section-2 .u-image-16 {
    height: 304px;
  }

  .u-section-2 .u-image-17 {
    height: 304px;
  }

  .u-section-2 .u-image-18 {
    height: 304px;
  }

  .u-section-2 .u-repeater-4 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-2 .u-image-19 {
    height: 304px;
  }

  .u-section-2 .u-image-20 {
    height: 304px;
  }

  .u-section-2 .u-image-21 {
    height: 304px;
  }

  .u-section-2 .u-image-22 {
    height: 304px;
  }

  .u-section-2 .u-image-23 {
    height: 304px;
  }

  .u-section-2 .u-image-24 {
    height: 304px;
  }

  .u-section-2 .u-repeater-5 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-2 .u-image-25 {
    height: 304px;
  }

  .u-section-2 .u-image-26 {
    height: 304px;
  }

  .u-section-2 .u-image-27 {
    height: 304px;
  }

  .u-section-2 .u-image-28 {
    height: 304px;
  }

  .u-section-2 .u-image-29 {
    height: 304px;
  }

  .u-section-2 .u-image-30 {
    height: 304px;
  }

  .u-section-2 .u-repeater-6 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-2 .u-image-31 {
    height: 304px;
  }

  .u-section-2 .u-image-32 {
    height: 304px;
  }

  .u-section-2 .u-image-33 {
    height: 304px;
  }

  .u-section-2 .u-image-34 {
    height: 304px;
  }

  .u-section-2 .u-image-35 {
    height: 304px;
  }

  .u-section-2 .u-image-36 {
    height: 304px;
  }

  .u-section-2 .u-repeater-7 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-2 .u-image-37 {
    height: 304px;
  }

  .u-section-2 .u-image-38 {
    height: 304px;
  }

  .u-section-2 .u-image-39 {
    height: 304px;
  }

  .u-section-2 .u-image-40 {
    height: 304px;
  }

  .u-section-2 .u-image-41 {
    height: 304px;
  }

  .u-section-2 .u-image-42 {
    height: 304px;
  }

  .u-section-2 .u-repeater-8 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-2 .u-image-43 {
    height: 304px;
  }

  .u-section-2 .u-image-44 {
    height: 304px;
  }

  .u-section-2 .u-image-45 {
    height: 304px;
  }

  .u-section-2 .u-image-46 {
    height: 304px;
  }

  .u-section-2 .u-image-47 {
    height: 304px;
  }

  .u-section-2 .u-image-48 {
    height: 304px;
  }
}

@media (max-width: 575px) {
  .u-section-2 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-2 .u-image-1 {
    height: 187px;
  }

  .u-section-2 .u-image-2 {
    height: 187px;
  }

  .u-section-2 .u-image-3 {
    height: 187px;
  }

  .u-section-2 .u-image-4 {
    height: 187px;
  }

  .u-section-2 .u-image-5 {
    height: 187px;
  }

  .u-section-2 .u-image-6 {
    height: 187px;
  }

  .u-section-2 .u-repeater-2 {
    grid-auto-columns: 100%;
  }

  .u-section-2 .u-image-7 {
    height: 187px;
  }

  .u-section-2 .u-image-8 {
    height: 187px;
  }

  .u-section-2 .u-image-9 {
    height: 187px;
  }

  .u-section-2 .u-image-10 {
    height: 187px;
  }

  .u-section-2 .u-image-11 {
    height: 187px;
  }

  .u-section-2 .u-image-12 {
    height: 187px;
  }

  .u-section-2 .u-repeater-3 {
    grid-auto-columns: 100%;
  }

  .u-section-2 .u-image-13 {
    height: 187px;
  }

  .u-section-2 .u-image-14 {
    height: 187px;
  }

  .u-section-2 .u-image-15 {
    height: 187px;
  }

  .u-section-2 .u-image-16 {
    height: 187px;
  }

  .u-section-2 .u-image-17 {
    height: 187px;
  }

  .u-section-2 .u-image-18 {
    height: 187px;
  }

  .u-section-2 .u-repeater-4 {
    grid-auto-columns: 100%;
  }

  .u-section-2 .u-image-19 {
    height: 187px;
  }

  .u-section-2 .u-image-20 {
    height: 187px;
  }

  .u-section-2 .u-image-21 {
    height: 187px;
  }

  .u-section-2 .u-image-22 {
    height: 187px;
  }

  .u-section-2 .u-image-23 {
    height: 187px;
  }

  .u-section-2 .u-image-24 {
    height: 187px;
  }

  .u-section-2 .u-repeater-5 {
    grid-auto-columns: 100%;
  }

  .u-section-2 .u-image-25 {
    height: 187px;
  }

  .u-section-2 .u-image-26 {
    height: 187px;
  }

  .u-section-2 .u-image-27 {
    height: 187px;
  }

  .u-section-2 .u-image-28 {
    height: 187px;
  }

  .u-section-2 .u-image-29 {
    height: 187px;
  }

  .u-section-2 .u-image-30 {
    height: 187px;
  }

  .u-section-2 .u-repeater-6 {
    grid-auto-columns: 100%;
  }

  .u-section-2 .u-image-31 {
    height: 187px;
  }

  .u-section-2 .u-image-32 {
    height: 187px;
  }

  .u-section-2 .u-image-33 {
    height: 187px;
  }

  .u-section-2 .u-image-34 {
    height: 187px;
  }

  .u-section-2 .u-image-35 {
    height: 187px;
  }

  .u-section-2 .u-image-36 {
    height: 187px;
  }

  .u-section-2 .u-repeater-7 {
    grid-auto-columns: 100%;
  }

  .u-section-2 .u-image-37 {
    height: 187px;
  }

  .u-section-2 .u-image-38 {
    height: 187px;
  }

  .u-section-2 .u-image-39 {
    height: 187px;
  }

  .u-section-2 .u-image-40 {
    height: 187px;
  }

  .u-section-2 .u-image-41 {
    height: 187px;
  }

  .u-section-2 .u-image-42 {
    height: 187px;
  }

  .u-section-2 .u-repeater-8 {
    grid-auto-columns: 100%;
  }

  .u-section-2 .u-image-43 {
    height: 187px;
  }

  .u-section-2 .u-image-44 {
    height: 187px;
  }

  .u-section-2 .u-image-45 {
    height: 187px;
  }

  .u-section-2 .u-image-46 {
    height: 187px;
  }

  .u-section-2 .u-image-47 {
    height: 187px;
  }

  .u-section-2 .u-image-48 {
    height: 187px;
  }
}

.u-section-2 .u-image-46,
.u-section-2 .u-image-46:before,
.u-section-2 .u-image-46 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-46 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-46 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-43,
.u-section-2 .u-image-43:before,
.u-section-2 .u-image-43 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-43 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-43 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-44,
.u-section-2 .u-image-44:before,
.u-section-2 .u-image-44 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-44 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-44 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-45,
.u-section-2 .u-image-45:before,
.u-section-2 .u-image-45 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-45 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-45 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-47,
.u-section-2 .u-image-47:before,
.u-section-2 .u-image-47 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-47 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-47 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-48,
.u-section-2 .u-image-48:before,
.u-section-2 .u-image-48 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-48 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-48 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-39,
.u-section-2 .u-image-39:before,
.u-section-2 .u-image-39 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-39 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-39 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-37,
.u-section-2 .u-image-37:before,
.u-section-2 .u-image-37 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-37 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-37 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-38,
.u-section-2 .u-image-38:before,
.u-section-2 .u-image-38 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-38 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-38 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-40,
.u-section-2 .u-image-40:before,
.u-section-2 .u-image-40 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-40 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-40 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-41,
.u-section-2 .u-image-41:before,
.u-section-2 .u-image-41 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-41 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-41 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-42,
.u-section-2 .u-image-42:before,
.u-section-2 .u-image-42 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-42 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-42 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-33,
.u-section-2 .u-image-33:before,
.u-section-2 .u-image-33 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-33 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-33 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-31,
.u-section-2 .u-image-31:before,
.u-section-2 .u-image-31 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-31 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-31 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-32,
.u-section-2 .u-image-32:before,
.u-section-2 .u-image-32 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-32 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-32 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-34,
.u-section-2 .u-image-34:before,
.u-section-2 .u-image-34 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-34 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-34 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-35,
.u-section-2 .u-image-35:before,
.u-section-2 .u-image-35 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-35 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-35 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-36,
.u-section-2 .u-image-36:before,
.u-section-2 .u-image-36 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-36 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-36 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-26,
.u-section-2 .u-image-26:before,
.u-section-2 .u-image-26 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-26 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-26 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-25,
.u-section-2 .u-image-25:before,
.u-section-2 .u-image-25 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-25 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-25 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-27,
.u-section-2 .u-image-27:before,
.u-section-2 .u-image-27 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-27 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-27 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-28,
.u-section-2 .u-image-28:before,
.u-section-2 .u-image-28 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-28 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-28 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-29,
.u-section-2 .u-image-29:before,
.u-section-2 .u-image-29 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-29 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-29 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-30,
.u-section-2 .u-image-30:before,
.u-section-2 .u-image-30 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-30 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-30 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-20,
.u-section-2 .u-image-20:before,
.u-section-2 .u-image-20 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-20 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-20 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-19,
.u-section-2 .u-image-19:before,
.u-section-2 .u-image-19 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-19 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-19 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-21,
.u-section-2 .u-image-21:before,
.u-section-2 .u-image-21 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-21 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-21 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-22,
.u-section-2 .u-image-22:before,
.u-section-2 .u-image-22 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-22 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-22 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-23,
.u-section-2 .u-image-23:before,
.u-section-2 .u-image-23 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-23 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-23 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-24,
.u-section-2 .u-image-24:before,
.u-section-2 .u-image-24 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-24 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-24 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-14,
.u-section-2 .u-image-14:before,
.u-section-2 .u-image-14 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-14 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-14 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-13,
.u-section-2 .u-image-13:before,
.u-section-2 .u-image-13 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-13 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-13 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-15,
.u-section-2 .u-image-15:before,
.u-section-2 .u-image-15 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-15 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-15 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-16,
.u-section-2 .u-image-16:before,
.u-section-2 .u-image-16 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-16 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-16 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-17,
.u-section-2 .u-image-17:before,
.u-section-2 .u-image-17 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-17 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-17 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-18,
.u-section-2 .u-image-18:before,
.u-section-2 .u-image-18 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-18 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .hover > .u-container-layout .u-image-18 {
  transform: translateX(0px) translateY(-5px) !important;
}