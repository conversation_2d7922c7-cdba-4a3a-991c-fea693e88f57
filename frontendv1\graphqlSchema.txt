schema {
  query: Query
  mutation: Mutation
}

type Equipment {
  id: Int!
  name: String!
  description: String
  serialNumber: String
  model: String
  manufacturer: String
  purchaseDate: DateTime
  lastMaintenanceDate: DateTime
  nextMaintenanceDate: DateTime
  location: String
  status: String
  purchasePrice: Decimal
  category: String
  tasks: [Task]
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String
  isDeleted: Boolean!
  deletedAt: DateTime
  deletedBy: String
}

type Incident {
  id: Int!
  title: String!
  description: String!
  incidentDate: DateTime!
  location: String!
  status: IncidentStatus!
  reportedBy: String
  investigatedBy: String
  resolution: String
  resolvedDate: DateTime
  workers: [Worker]
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String!
  isDeleted: Boolean!
  deletedAt: DateTime
  deletedBy: String
}

type Mutation {
  createWorker(
    name: String!
    company: String!
    nationalId: String!
    gender: String!
    phoneNumber: String!
    dateOfBirth: Date
    trainingIds: [Int!]
    tradeIds: [Int!]
    skillIds: [Int!]
    mpesaNumber: String
    email: String
    inductionDate: DateTime
    medicalCheckDate: DateTime
  ): Worker! @cost(weight: "10")
  updateWorker(
    id: Int!
    name: String
    company: String
    dateOfBirth: Date
    trainingIds: [Int!]
    tradeIds: [Int!]
    skillIds: [Int!]
    manHours: Int
    rating: Float
    gender: String
    phoneNumber: String
    mpesaNumber: String
    email: String
    inductionDate: DateTime
    medicalCheckDate: DateTime
  ): Worker @cost(weight: "10")
  deleteWorker(id: Int!): Boolean! @cost(weight: "10")
  createTraining(
    name: String!
    description: String
    startDate: DateTime
    endDate: DateTime
    duration: String
    validityPeriodMonths: Int
    trainingType: String
    trainer: String
    frequency: String
    status: TrainingStatus! = SCHEDULED
    workerIds: [Int!]
  ): Training! @cost(weight: "10")
  updateTraining(
    id: Int!
    name: String
    description: String
    startDate: DateTime
    endDate: DateTime
    duration: String
    validityPeriodMonths: Int
    trainingType: String
    trainer: String
    frequency: String
    status: TrainingStatus
    workerIds: [Int!]
  ): Training! @cost(weight: "10")
  deleteTraining(id: Int!): Boolean! @cost(weight: "10")
  assignTrainingToWorkersByTrade(trainingId: Int!, tradeIds: [Int!]!): Int!
    @cost(weight: "10")
  completeTraining(
    workerId: Int!
    trainingId: Int!
    completionDate: DateTime
    score: Decimal
    notes: String
  ): WorkerTrainingHistory! @cost(weight: "10")
  updateTrainingStatuses: Boolean! @cost(weight: "10")
  createTrade(name: String!, description: String, workerIds: [Int!]): Trade!
    @cost(weight: "10")
  updateTrade(
    id: Int!
    name: String
    description: String
    workerIds: [Int!]
  ): Trade @cost(weight: "10")
  deleteTrade(id: Int!): Boolean! @cost(weight: "10")
  createSkill(name: String!, description: String, workerIds: [Int!]): Skill!
    @cost(weight: "10")
  updateSkill(
    id: Int!
    name: String
    description: String
    workerIds: [Int!]
  ): Skill @cost(weight: "10")
  deleteSkill(id: Int!): Boolean! @cost(weight: "10")
  createTask(
    name: String!
    type: String!
    description: String!
    status: TaskStatus! = TODO
    priority: TaskPriority! = MEDIUM
    timeForCompletion: String
    dueDate: DateTime
    startDate: DateTime
    category: String
    inspectionStatus: InspectionStatus! = NOT_REQUIRED
    associatedMethodStatement: String
    chiefEngineerId: Int
    workerIds: [Int!]
    equipmentIds: [Int!]
  ): Task! @cost(weight: "10")
  updateTask(
    id: Int!
    name: String
    type: String
    description: String
    status: TaskStatus
    priority: TaskPriority
    timeForCompletion: String
    dueDate: DateTime
    startDate: DateTime
    category: String
    inspectionStatus: InspectionStatus
    associatedMethodStatement: String
    chiefEngineerId: Int
    workerIds: [Int!]
    equipmentIds: [Int!]
  ): Task @cost(weight: "10")
  deleteTask(id: Int!): Boolean! @cost(weight: "10")
  createEquipment(
    name: String!
    description: String
    serialNumber: String
    model: String
    manufacturer: String
    purchaseDate: DateTime
    lastMaintenanceDate: DateTime
    nextMaintenanceDate: DateTime
    location: String
    status: String
    purchasePrice: Decimal
    category: String
  ): Equipment! @cost(weight: "10")
  updateEquipment(
    id: Int!
    name: String
    description: String
    serialNumber: String
    model: String
    manufacturer: String
    purchaseDate: DateTime
    lastMaintenanceDate: DateTime
    nextMaintenanceDate: DateTime
    location: String
    status: String
    purchasePrice: Decimal
    category: String
  ): Equipment @cost(weight: "10")
  deleteEquipment(id: Int!): Boolean! @cost(weight: "10")
}

type Query {
  allWorkers(where: WorkerFilterInput @cost(weight: "10")): [Worker!]!
    @cost(weight: "10")
  workerById(id: Int!): [Worker!]! @cost(weight: "10")
  allTrainings(where: TrainingFilterInput @cost(weight: "10")): [Training!]!
    @cost(weight: "10")
  trainingById(id: Int!): [Training!]! @cost(weight: "10")
  allTrades: [Trade!]! @cost(weight: "10")
  tradeById(id: Int!): Trade @cost(weight: "10")
  allSkills: [Skill!]! @cost(weight: "10")
  skillById(id: Int!): Skill @cost(weight: "10")
  workerTrainingHistory(workerId: Int!): [WorkerTrainingHistory!]!
    @cost(weight: "10")
  expiringTrainings(daysAhead: Int! = 30): [WorkerTrainingHistory!]!
    @cost(weight: "10")
  expiredTrainings: [WorkerTrainingHistory!]! @cost(weight: "10")
  allTasks(where: TaskFilterInput @cost(weight: "10")): [Task!]!
    @cost(weight: "10")
  taskById(id: Int!): [Task!]! @cost(weight: "10")
  tasksByWorkerId(workerId: Int!): [Task!]! @cost(weight: "10")
  tasksByChiefEngineerId(chiefEngineerId: Int!): [Task!]! @cost(weight: "10")
  tasksByStatus(status: TaskStatus!): [Task!]! @cost(weight: "10")
  tasksByPriority(priority: TaskPriority!): [Task!]! @cost(weight: "10")
  allEquipment: [Equipment!]! @cost(weight: "10")
  equipmentById(id: Int!): Equipment @cost(weight: "10")
  equipmentByStatus(status: String!): [Equipment!]! @cost(weight: "10")
  equipmentByCategory(category: String!): [Equipment!]! @cost(weight: "10")
  equipmentByLocation(location: String!): [Equipment!]! @cost(weight: "10")
}

type Skill {
  id: Int!
  name: String!
  description: String
  workers: [Worker]
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String!
}

type Task {
  id: Int!
  name: String!
  type: String!
  description: String!
  status: TaskStatus!
  taskNumber: String!
  priority: TaskPriority!
  timeForCompletion: String
  dueDate: DateTime
  startDate: DateTime
  category: String
  inspectionStatus: InspectionStatus!
  associatedMethodStatement: String
  chiefEngineerId: Int
  chiefEngineer: Worker
  workersAssigned: [Worker]
  equipmentInvolved: [Equipment]
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String
  isDeleted: Boolean!
  deletedAt: DateTime
  deletedBy: String
}

type ToolboxAttendance {
  id: Int!
  toolboxSessionId: Int!
  workerId: Int!
  worker: Worker
  wasPresent: Boolean!
  notes: String
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String
  toolboxSession: ToolboxSession!
}

type ToolboxSession {
  id: Int!
  sessionTime: DateTime!
  topic: String!
  conductor: String!
  photoUrl: String!
  notes: String
  attendances: [ToolboxAttendance]
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String
}

type Trade {
  id: Int!
  name: String!
  description: String
  workers: [Worker]
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String!
}

type Training {
  id: Int!
  name: String!
  description: String
  workers: [Worker]
  trainingHistory: [WorkerTrainingHistory]
  duration: String
  parsedDuration: String @cost(weight: "10")
  startDate: DateTime
  endDate: DateTime
  validityPeriodMonths: Int
  trainingType: String
  trainer: String
  frequency: String
  status: TrainingStatus!
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String!
  isDeleted: Boolean!
  deletedAt: DateTime
  deletedBy: String
}

type Worker {
  id: Int!
  name: String!
  company: String!
  phoneNumber: String!
  nationalId: String!
  gender: String!
  dateOfBirth: Date
  inductionDate: DateTime
  medicalCheckDate: DateTime
  photoUrl: String
  mpesaNumber: String
  email: String
  age: Int
  trainingsCompleted: Int!
  manHours: Int!
  rating: Float!
  trainings: [Training]
  trades: [Trade]
  skills: [Skill]
  trainingHistory: [WorkerTrainingHistory]
  incidents: [Incident]
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String!
  isDeleted: Boolean!
  deletedAt: DateTime
  deletedBy: String
}

type WorkerAttendance {
  id: Int!
  workerId: Int!
  worker: Worker
  checkInTime: DateTime!
  checkOutTime: DateTime
  status: String!
  notes: String
  isVerifiedByHikvision: Boolean!
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String
}

type WorkerTrainingHistory {
  id: Int!
  workerId: Int!
  trainingId: Int!
  worker: Worker!
  training: Training!
  completionDate: DateTime!
  expiryDate: DateTime
  status: TrainingStatus!
  notes: String
  certificateUrl: String
  score: Float
  isExpired: Boolean!
  isExpiringSoon: Boolean!
  daysUntilExpiry: Int
  createdAt: DateTime!
  createdBy: String!
  updatedAt: DateTime
  updatedBy: String!
}

input BooleanOperationFilterInput {
  eq: Boolean @cost(weight: "10")
  neq: Boolean @cost(weight: "10")
}

input DateOperationFilterInput {
  eq: Date @cost(weight: "10")
  neq: Date @cost(weight: "10")
  in: [Date] @cost(weight: "10")
  nin: [Date] @cost(weight: "10")
  gt: Date @cost(weight: "10")
  ngt: Date @cost(weight: "10")
  gte: Date @cost(weight: "10")
  ngte: Date @cost(weight: "10")
  lt: Date @cost(weight: "10")
  nlt: Date @cost(weight: "10")
  lte: Date @cost(weight: "10")
  nlte: Date @cost(weight: "10")
}

input DateTimeOperationFilterInput {
  eq: DateTime @cost(weight: "10")
  neq: DateTime @cost(weight: "10")
  in: [DateTime] @cost(weight: "10")
  nin: [DateTime] @cost(weight: "10")
  gt: DateTime @cost(weight: "10")
  ngt: DateTime @cost(weight: "10")
  gte: DateTime @cost(weight: "10")
  ngte: DateTime @cost(weight: "10")
  lt: DateTime @cost(weight: "10")
  nlt: DateTime @cost(weight: "10")
  lte: DateTime @cost(weight: "10")
  nlte: DateTime @cost(weight: "10")
}

input DecimalOperationFilterInput {
  eq: Decimal @cost(weight: "10")
  neq: Decimal @cost(weight: "10")
  in: [Decimal] @cost(weight: "10")
  nin: [Decimal] @cost(weight: "10")
  gt: Decimal @cost(weight: "10")
  ngt: Decimal @cost(weight: "10")
  gte: Decimal @cost(weight: "10")
  ngte: Decimal @cost(weight: "10")
  lt: Decimal @cost(weight: "10")
  nlt: Decimal @cost(weight: "10")
  lte: Decimal @cost(weight: "10")
  nlte: Decimal @cost(weight: "10")
}

input EquipmentFilterInput {
  and: [EquipmentFilterInput!]
  or: [EquipmentFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  description: StringOperationFilterInput
  serialNumber: StringOperationFilterInput
  model: StringOperationFilterInput
  manufacturer: StringOperationFilterInput
  purchaseDate: DateTimeOperationFilterInput
  lastMaintenanceDate: DateTimeOperationFilterInput
  nextMaintenanceDate: DateTimeOperationFilterInput
  location: StringOperationFilterInput
  status: StringOperationFilterInput
  purchasePrice: DecimalOperationFilterInput
  category: StringOperationFilterInput
  tasks: ListFilterInputTypeOfTaskFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
  isDeleted: BooleanOperationFilterInput
  deletedAt: DateTimeOperationFilterInput
  deletedBy: StringOperationFilterInput
}

input FloatOperationFilterInput {
  eq: Float @cost(weight: "10")
  neq: Float @cost(weight: "10")
  in: [Float] @cost(weight: "10")
  nin: [Float] @cost(weight: "10")
  gt: Float @cost(weight: "10")
  ngt: Float @cost(weight: "10")
  gte: Float @cost(weight: "10")
  ngte: Float @cost(weight: "10")
  lt: Float @cost(weight: "10")
  nlt: Float @cost(weight: "10")
  lte: Float @cost(weight: "10")
  nlte: Float @cost(weight: "10")
}

input IncidentFilterInput {
  and: [IncidentFilterInput!]
  or: [IncidentFilterInput!]
  id: IntOperationFilterInput
  title: StringOperationFilterInput
  description: StringOperationFilterInput
  incidentDate: DateTimeOperationFilterInput
  location: StringOperationFilterInput
  status: IncidentStatusOperationFilterInput
  reportedBy: StringOperationFilterInput
  investigatedBy: StringOperationFilterInput
  resolution: StringOperationFilterInput
  resolvedDate: DateTimeOperationFilterInput
  workers: ListFilterInputTypeOfWorkerFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
  isDeleted: BooleanOperationFilterInput
  deletedAt: DateTimeOperationFilterInput
  deletedBy: StringOperationFilterInput
}

input IncidentStatusOperationFilterInput {
  eq: IncidentStatus @cost(weight: "10")
  neq: IncidentStatus @cost(weight: "10")
  in: [IncidentStatus!] @cost(weight: "10")
  nin: [IncidentStatus!] @cost(weight: "10")
}

input InspectionStatusOperationFilterInput {
  eq: InspectionStatus @cost(weight: "10")
  neq: InspectionStatus @cost(weight: "10")
  in: [InspectionStatus!] @cost(weight: "10")
  nin: [InspectionStatus!] @cost(weight: "10")
}

input IntOperationFilterInput {
  eq: Int @cost(weight: "10")
  neq: Int @cost(weight: "10")
  in: [Int] @cost(weight: "10")
  nin: [Int] @cost(weight: "10")
  gt: Int @cost(weight: "10")
  ngt: Int @cost(weight: "10")
  gte: Int @cost(weight: "10")
  ngte: Int @cost(weight: "10")
  lt: Int @cost(weight: "10")
  nlt: Int @cost(weight: "10")
  lte: Int @cost(weight: "10")
  nlte: Int @cost(weight: "10")
}

input ListFilterInputTypeOfEquipmentFilterInput {
  all: EquipmentFilterInput @cost(weight: "10")
  none: EquipmentFilterInput @cost(weight: "10")
  some: EquipmentFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfIncidentFilterInput {
  all: IncidentFilterInput @cost(weight: "10")
  none: IncidentFilterInput @cost(weight: "10")
  some: IncidentFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfSkillFilterInput {
  all: SkillFilterInput @cost(weight: "10")
  none: SkillFilterInput @cost(weight: "10")
  some: SkillFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfTaskFilterInput {
  all: TaskFilterInput @cost(weight: "10")
  none: TaskFilterInput @cost(weight: "10")
  some: TaskFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfTradeFilterInput {
  all: TradeFilterInput @cost(weight: "10")
  none: TradeFilterInput @cost(weight: "10")
  some: TradeFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfTrainingFilterInput {
  all: TrainingFilterInput @cost(weight: "10")
  none: TrainingFilterInput @cost(weight: "10")
  some: TrainingFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfWorkerFilterInput {
  all: WorkerFilterInput @cost(weight: "10")
  none: WorkerFilterInput @cost(weight: "10")
  some: WorkerFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input ListFilterInputTypeOfWorkerTrainingHistoryFilterInput {
  all: WorkerTrainingHistoryFilterInput @cost(weight: "10")
  none: WorkerTrainingHistoryFilterInput @cost(weight: "10")
  some: WorkerTrainingHistoryFilterInput @cost(weight: "10")
  any: Boolean @cost(weight: "10")
}

input SkillFilterInput {
  and: [SkillFilterInput!]
  or: [SkillFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  description: StringOperationFilterInput
  workers: ListFilterInputTypeOfWorkerFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
}

input StringOperationFilterInput {
  and: [StringOperationFilterInput!]
  or: [StringOperationFilterInput!]
  eq: String @cost(weight: "10")
  neq: String @cost(weight: "10")
  contains: String @cost(weight: "20")
  ncontains: String @cost(weight: "20")
  in: [String] @cost(weight: "10")
  nin: [String] @cost(weight: "10")
  startsWith: String @cost(weight: "20")
  nstartsWith: String @cost(weight: "20")
  endsWith: String @cost(weight: "20")
  nendsWith: String @cost(weight: "20")
}

input TaskFilterInput {
  and: [TaskFilterInput!]
  or: [TaskFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  type: StringOperationFilterInput
  description: StringOperationFilterInput
  status: TaskStatusOperationFilterInput
  taskNumber: StringOperationFilterInput
  priority: TaskPriorityOperationFilterInput
  timeForCompletion: StringOperationFilterInput
  dueDate: DateTimeOperationFilterInput
  startDate: DateTimeOperationFilterInput
  category: StringOperationFilterInput
  inspectionStatus: InspectionStatusOperationFilterInput
  associatedMethodStatement: StringOperationFilterInput
  chiefEngineerId: IntOperationFilterInput
  chiefEngineer: WorkerFilterInput
  workersAssigned: ListFilterInputTypeOfWorkerFilterInput
  equipmentInvolved: ListFilterInputTypeOfEquipmentFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
  isDeleted: BooleanOperationFilterInput
  deletedAt: DateTimeOperationFilterInput
  deletedBy: StringOperationFilterInput
}

input TaskPriorityOperationFilterInput {
  eq: TaskPriority @cost(weight: "10")
  neq: TaskPriority @cost(weight: "10")
  in: [TaskPriority!] @cost(weight: "10")
  nin: [TaskPriority!] @cost(weight: "10")
}

input TaskStatusOperationFilterInput {
  eq: TaskStatus @cost(weight: "10")
  neq: TaskStatus @cost(weight: "10")
  in: [TaskStatus!] @cost(weight: "10")
  nin: [TaskStatus!] @cost(weight: "10")
}

input TradeFilterInput {
  and: [TradeFilterInput!]
  or: [TradeFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  description: StringOperationFilterInput
  workers: ListFilterInputTypeOfWorkerFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
}

input TrainingDurationFilterInput {
  and: [TrainingDurationFilterInput!]
  or: [TrainingDurationFilterInput!]
  years: IntOperationFilterInput
  months: IntOperationFilterInput
  days: IntOperationFilterInput
  hours: IntOperationFilterInput
  minutes: IntOperationFilterInput
}

input TrainingFilterInput {
  and: [TrainingFilterInput!]
  or: [TrainingFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  description: StringOperationFilterInput
  workers: ListFilterInputTypeOfWorkerFilterInput
  trainingHistory: ListFilterInputTypeOfWorkerTrainingHistoryFilterInput
  startDate: DateTimeOperationFilterInput
  endDate: DateTimeOperationFilterInput
  duration: StringOperationFilterInput
  validityPeriodMonths: IntOperationFilterInput
  trainingType: StringOperationFilterInput
  trainer: StringOperationFilterInput
  frequency: StringOperationFilterInput
  status: TrainingStatusOperationFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
  isDeleted: BooleanOperationFilterInput
  deletedAt: DateTimeOperationFilterInput
  deletedBy: StringOperationFilterInput
  parsedDuration: TrainingDurationFilterInput
}

input TrainingStatusOperationFilterInput {
  eq: TrainingStatus @cost(weight: "10")
  neq: TrainingStatus @cost(weight: "10")
  in: [TrainingStatus!] @cost(weight: "10")
  nin: [TrainingStatus!] @cost(weight: "10")
}

input WorkerFilterInput {
  and: [WorkerFilterInput!]
  or: [WorkerFilterInput!]
  id: IntOperationFilterInput
  name: StringOperationFilterInput
  company: StringOperationFilterInput
  dateOfBirth: DateOperationFilterInput
  manHours: IntOperationFilterInput
  photoUrl: StringOperationFilterInput
  inductionDate: DateTimeOperationFilterInput
  medicalCheckDate: DateTimeOperationFilterInput
  rating: FloatOperationFilterInput
  gender: StringOperationFilterInput
  nationalId: StringOperationFilterInput
  phoneNumber: StringOperationFilterInput
  email: StringOperationFilterInput
  mpesaNumber: StringOperationFilterInput
  trainings: ListFilterInputTypeOfTrainingFilterInput
  trades: ListFilterInputTypeOfTradeFilterInput
  skills: ListFilterInputTypeOfSkillFilterInput
  trainingHistory: ListFilterInputTypeOfWorkerTrainingHistoryFilterInput
  incidents: ListFilterInputTypeOfIncidentFilterInput
  age: IntOperationFilterInput
  trainingsCompleted: IntOperationFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
  isDeleted: BooleanOperationFilterInput
  deletedAt: DateTimeOperationFilterInput
  deletedBy: StringOperationFilterInput
}

input WorkerTrainingHistoryFilterInput {
  and: [WorkerTrainingHistoryFilterInput!]
  or: [WorkerTrainingHistoryFilterInput!]
  id: IntOperationFilterInput
  workerId: IntOperationFilterInput
  worker: WorkerFilterInput
  trainingId: IntOperationFilterInput
  training: TrainingFilterInput
  completionDate: DateTimeOperationFilterInput
  expiryDate: DateTimeOperationFilterInput
  status: TrainingStatusOperationFilterInput
  notes: StringOperationFilterInput
  certificateUrl: StringOperationFilterInput
  score: DecimalOperationFilterInput
  createdAt: DateTimeOperationFilterInput
  createdBy: StringOperationFilterInput
  updatedAt: DateTimeOperationFilterInput
  updatedBy: StringOperationFilterInput
  isExpired: BooleanOperationFilterInput
  isExpiringSoon: BooleanOperationFilterInput
  daysUntilExpiry: IntOperationFilterInput
}

enum IncidentStatus {
  REPORTED
  INVESTIGATING
  RESOLVED
  CLOSED
  PENDING
}

enum InspectionStatus {
  NOT_REQUIRED
  PENDING
  IN_PROGRESS
  COMPLETED
  FAILED
  REWORK_REQUIRED
}

enum TaskPriority {
  LOW
  MEDIUM
  HIGH
  CRITICAL
}

enum TaskStatus {
  TODO
  IN_PROGRESS
  COMPLETED
  CANCELLED
  ON_HOLD
}

enum TrainingStatus {
  SCHEDULED
  ACTIVE
  COMPLETED
  EXPIRED
  CANCELLED
}

"The purpose of the `cost` directive is to define a `weight` for GraphQL types, fields, and arguments. Static analysis can use these weights when calculating the overall cost of a query or response."
directive @cost(
  "The `weight` argument defines what value to add to the overall cost for every appearance, or possible appearance, of a type, field, argument, etc."
  weight: String!
) on SCALAR | OBJECT | FIELD_DEFINITION | ARGUMENT_DEFINITION | ENUM | INPUT_FIELD_DEFINITION

"The `@specifiedBy` directive is used within the type system definition language to provide a URL for specifying the behavior of custom scalar definitions."
directive @specifiedBy(
  "The specifiedBy URL points to a human-readable specification. This field will only read a result for scalar types."
  url: String!
) on SCALAR

"The `Date` scalar represents an ISO-8601 compliant date type."
scalar Date

"The `DateTime` scalar represents an ISO-8601 compliant date time type."
scalar DateTime @specifiedBy(url: "https://www.graphql-scalars.com/date-time")

"The `Decimal` scalar type represents a decimal floating-point number."
scalar Decimal
