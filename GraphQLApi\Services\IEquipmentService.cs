using Shared.GraphQL.Models;

namespace GraphQLApi.Services
{
    public interface IEquipmentService
    {
        Task<IEnumerable<Equipment>> GetAllEquipmentAsync();
        Task<Equipment?> GetEquipmentByIdAsync(int id);
        Task<Equipment> CreateEquipmentAsync(Equipment equipment);
        Task<Equipment?> UpdateEquipmentAsync(int id, Equipment equipment);
        Task<bool> DeleteEquipmentAsync(int id);
        Task<IEnumerable<Equipment>> GetEquipmentByStatusAsync(string status);
        Task<IEnumerable<Equipment>> GetEquipmentByCategoryAsync(string category);
        Task<IEnumerable<Equipment>> GetEquipmentByLocationAsync(string location);
    }
}
