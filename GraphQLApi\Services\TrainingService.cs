using GraphQLApi.Data;
using Microsoft.EntityFrameworkCore;
using Shared.GraphQL.Models;
using HotChocolate;
using Microsoft.Data.SqlClient;

namespace GraphQLApi.Services;

public class TrainingService : ITrainingService
{
    private readonly IDbContextFactory<AppDbContext> _contextFactory;
    private readonly ILogger<TrainingService> _logger;

    public TrainingService(
        IDbContextFactory<AppDbContext> contextFactory,
        ILogger<TrainingService> logger)
    {
        _contextFactory = contextFactory;
        _logger = logger;
    }



    public async Task<IEnumerable<Training>> GetAllTrainingsAsync()
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        return await context.Trainings
            .Include(t => t.Workers)
            .ToListAsync();
    }

    public async Task<Training?> GetTrainingByIdAsync(int id)
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        return await context.Trainings
            .Include(t => t.Workers)
            .FirstOrDefaultAsync(t => t.Id == id);
    }

    public async Task<Training> CreateTrainingAsync(Training training)
    {
        await using var context = await _contextFactory.CreateDbContextAsync();

        try
        {
            context.Trainings.Add(training);
            await context.SaveChangesAsync();
            return training;
        }
        catch (DbUpdateException ex) when (
            ex.InnerException is SqlException sqlEx &&
            (sqlEx.Number == 2601 || sqlEx.Number == 2627)
        )
        {
            throw new GraphQLException(new Error(
                "Validation",
                $"A training with name '{training.Name}' already exists.")
            );
        }
    }

    public async Task<Training?> UpdateTrainingAsync(int id, Training training)
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        var existingTraining = await context.Trainings
            .Include(t => t.Workers)
            .FirstOrDefaultAsync(t => t.Id == id);

        if (existingTraining == null)
        {
            return null;
        }

        try
        {
            // Update properties
            existingTraining.Name = training.Name;
            existingTraining.Description = training.Description;
            existingTraining.StartDate = training.StartDate;
            existingTraining.EndDate = training.EndDate;
            existingTraining.Duration = training.Duration;
            existingTraining.ValidityPeriodMonths = training.ValidityPeriodMonths;
            existingTraining.TrainingType = training.TrainingType;
            existingTraining.Trainer = training.Trainer;
            existingTraining.Frequency = training.Frequency;
            existingTraining.Status = training.Status;

            await context.SaveChangesAsync();
            return existingTraining;
        }
        catch (DbUpdateException ex) when (ex.InnerException?.Message.Contains("IX_Trainings_Name") == true)
        {
            throw new GraphQLException(new Error(
                "Validation",
                $"A training with name '{training.Name}' already exists.")
            );
        }
    }

    public async Task<bool> DeleteTrainingAsync(int id)
    {
        await using var context = await _contextFactory.CreateDbContextAsync();
        var training = await context.Trainings.FindAsync(id);

        if (training == null)
        {
            return false;
        }

        try
        {
            context.Trainings.Remove(training);
            await context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting training with ID {TrainingId}", id);
            return false;
        }
    }
}
