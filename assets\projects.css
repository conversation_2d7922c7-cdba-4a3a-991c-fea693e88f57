 .u-section-1 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-1 .u-sheet-1 {
  min-height: 236px;
}

.u-section-1 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: -29px;
}

.u-section-1 .u-layout-cell-1 {
  min-height: 196px;
}

.u-section-1 .u-container-layout-1 {
  padding: 0 30px 0 0;
}

.u-section-1 .u-text-1 {
  margin: 0 190px 0 0;
}

.u-section-1 .u-text-2 {
  margin: 20px 62px 0 0;
}

.u-section-1 .u-layout-cell-2 {
  min-height: 196px;
}

.u-section-1 .u-container-layout-2 {
  padding: 30px 30px 25px;
}

.u-section-1 .u-text-3 {
  font-weight: 400;
  margin: 23px 0 0;
}

@media (max-width: 1199px) {
  .u-section-1 .u-sheet-1 {
    min-height: 262px;
  }

  .u-section-1 .u-layout-wrap-1 {
    position: relative;
    margin-bottom: -68px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 163px;
  }

  .u-section-1 .u-text-1 {
    margin-right: 113px;
  }

  .u-section-1 .u-text-2 {
    margin-right: 0;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 163px;
  }
}

@media (max-width: 991px) {
  .u-section-1 .u-sheet-1 {
    min-height: 320px;
  }

  .u-section-1 .u-layout-wrap-1 {
    margin-bottom: -106px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-1 .u-text-1 {
    margin-right: 29px;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 100px;
  }
}

@media (max-width: 767px) {
  .u-section-1 .u-sheet-1 {
    min-height: 323px;
  }

  .u-section-1 .u-layout-wrap-1 {
    margin-bottom: -142px;
  }

  .u-section-1 .u-container-layout-1 {
    padding-right: 10px;
  }

  .u-section-1 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-text-3 {
    margin-top: 0;
  }
}

@media (max-width: 575px) {
  .u-section-1 .u-sheet-1 {
    min-height: 400px;
  }

  .u-section-1 .u-layout-wrap-1 {
    margin-bottom: -177px;
  }

  .u-section-1 .u-text-1 {
    margin-right: 0;
  }
} .u-section-2 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-2 .u-sheet-1 {
  min-height: 191px;
}

.u-section-2 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: 10px;
}

.u-section-2 .u-layout-cell-1 {
  min-height: 161px;
}

.u-section-2 .u-container-layout-1 {
  padding: 0 30px 0 0;
}

.u-section-2 .u-text-1 {
  margin: 0 130px 0 0;
}

.u-section-2 .u-text-2 {
  margin: 10px 62px 0 0;
}

.u-section-2 .u-layout-cell-2 {
  min-height: 161px;
}

.u-section-2 .u-container-layout-2 {
  padding: 30px;
}

.u-section-2 .u-text-3 {
  font-weight: 400;
  margin: 0;
}

@media (max-width: 1199px) {
  .u-section-2 .u-sheet-1 {
    min-height: 262px;
  }

  .u-section-2 .u-layout-wrap-1 {
    position: relative;
    margin-bottom: -68px;
  }

  .u-section-2 .u-layout-cell-1 {
    min-height: 134px;
  }

  .u-section-2 .u-text-1 {
    margin-right: 53px;
  }

  .u-section-2 .u-text-2 {
    margin-right: 0;
  }

  .u-section-2 .u-layout-cell-2 {
    min-height: 134px;
  }
}

@media (max-width: 991px) {
  .u-section-2 .u-sheet-1 {
    min-height: 320px;
  }

  .u-section-2 .u-layout-wrap-1 {
    margin-bottom: -106px;
  }

  .u-section-2 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-2 .u-text-1 {
    margin-right: 0;
  }

  .u-section-2 .u-layout-cell-2 {
    min-height: 100px;
  }
}

@media (max-width: 767px) {
  .u-section-2 .u-sheet-1 {
    min-height: 323px;
  }

  .u-section-2 .u-layout-wrap-1 {
    margin-bottom: -142px;
  }

  .u-section-2 .u-container-layout-1 {
    padding-right: 10px;
  }

  .u-section-2 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-2 .u-sheet-1 {
    min-height: 400px;
  }

  .u-section-2 .u-layout-wrap-1 {
    margin-bottom: -177px;
  }
} .u-section-3 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-3 .u-sheet-1 {
  min-height: 750px;
}

.u-section-3 .u-list-1 {
  margin-bottom: -60px;
  margin-top: 20px;
}

.u-section-3 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 710px;
  grid-gap: 10px;
}

.u-section-3 .u-container-layout-1 {
  padding: 10px;
}

.u-section-3 .u-image-1 {
  height: 235px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-3 .u-text-1 {
  font-weight: 400;
  transition-duration: 0.5s;
  text-decoration: none solid rgb(17, 17, 17) !important;
  margin: 20px 0 0;
}

.u-section-3 .u-text-2 {
  font-size: 1rem;
  font-weight: 300;
  margin: 20px 0 0;
}

.u-section-3 .u-container-layout-2 {
  padding: 10px;
}

.u-section-3 .u-image-2 {
  height: 235px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-3 .u-text-3 {
  font-weight: 400;
  transition-duration: 0.5s;
  text-decoration: none solid rgb(17, 17, 17) !important;
  margin: 20px 0 0;
}

.u-section-3 .u-text-4 {
  font-size: 1rem;
  font-weight: 300;
  margin: 20px 0 0;
}

.u-section-3 .u-container-layout-3 {
  padding: 10px;
}

.u-section-3 .u-image-3 {
  height: 235px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-3 .u-text-5 {
  font-weight: 400;
  transition-duration: 0.5s;
  text-decoration: none solid rgb(17, 17, 17) !important;
  margin: 20px 0 0;
}

.u-section-3 .u-text-6 {
  font-size: 1rem;
  font-weight: 300;
  margin: 20px 0 0;
}

.u-section-3 .u-container-layout-4 {
  padding: 10px;
}

.u-section-3 .u-image-4 {
  height: 235px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-3 .u-text-7 {
  font-weight: 400;
  transition-duration: 0.5s;
  text-decoration: none solid rgb(17, 17, 17) !important;
  margin: 20px 0 0;
}

.u-section-3 .u-text-8 {
  font-size: 1rem;
  font-weight: 300;
  margin: 20px 0 0;
}

.u-section-3 .u-container-layout-5 {
  padding: 10px;
}

.u-section-3 .u-image-5 {
  height: 235px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-3 .u-text-9 {
  font-weight: 400;
  transition-duration: 0.5s;
  text-decoration: none solid rgb(17, 17, 17) !important;
  margin: 20px 0 0;
}

.u-section-3 .u-text-10 {
  font-size: 1rem;
  font-weight: 300;
  margin: 20px 0 0;
}

.u-section-3 .u-container-layout-6 {
  padding: 10px;
}

.u-section-3 .u-image-6 {
  height: 235px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-3 .u-text-11 {
  font-weight: 400;
  transition-duration: 0.5s;
  text-decoration: none solid rgb(17, 17, 17) !important;
  margin: 20px 0 0;
}

.u-section-3 .u-text-12 {
  font-size: 1rem;
  font-weight: 300;
  margin: 20px 0 0;
}

@media (max-width: 1199px) {
  .u-section-3 .u-sheet-1 {
    min-height: 712px;
  }

  .u-section-3 .u-list-1 {
    margin-bottom: -98px;
  }

  .u-section-3 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 591px;
  }

  .u-section-3 .u-image-1 {
    height: 194px;
  }

  .u-section-3 .u-image-2 {
    height: 194px;
  }

  .u-section-3 .u-image-3 {
    height: 194px;
  }

  .u-section-3 .u-image-4 {
    height: 194px;
  }

  .u-section-3 .u-image-5 {
    height: 194px;
  }

  .u-section-3 .u-image-6 {
    height: 194px;
  }
}

@media (max-width: 991px) {
  .u-section-3 .u-sheet-1 {
    min-height: 830px;
  }

  .u-section-3 .u-list-1 {
    margin-bottom: 20px;
  }

  .u-section-3 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 1047px;
  }

  .u-section-3 .u-image-1 {
    height: 229px;
  }

  .u-section-3 .u-image-2 {
    height: 229px;
  }

  .u-section-3 .u-image-3 {
    height: 229px;
  }

  .u-section-3 .u-image-4 {
    height: 229px;
  }

  .u-section-3 .u-image-5 {
    height: 229px;
  }

  .u-section-3 .u-image-6 {
    height: 229px;
  }
}

@media (max-width: 767px) {
  .u-section-3 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-3 .u-image-1 {
    height: 322px;
  }

  .u-section-3 .u-image-2 {
    height: 322px;
  }

  .u-section-3 .u-image-3 {
    height: 322px;
  }

  .u-section-3 .u-image-4 {
    height: 322px;
  }

  .u-section-3 .u-image-5 {
    height: 322px;
  }

  .u-section-3 .u-image-6 {
    height: 322px;
  }
}

@media (max-width: 575px) {
  .u-section-3 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-3 .u-image-1 {
    height: 198px;
  }

  .u-section-3 .u-image-2 {
    height: 198px;
  }

  .u-section-3 .u-image-3 {
    height: 198px;
  }

  .u-section-3 .u-image-4 {
    height: 198px;
  }

  .u-section-3 .u-image-5 {
    height: 198px;
  }

  .u-section-3 .u-image-6 {
    height: 198px;
  }
}

.u-section-3 .u-image-6,
.u-section-3 .u-image-6:before,
.u-section-3 .u-image-6 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 :hover > .u-container-layout .u-image-6 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-3 .hover > .u-container-layout .u-image-6 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-3 .u-image-1,
.u-section-3 .u-image-1:before,
.u-section-3 .u-image-1 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 :hover > .u-container-layout .u-image-1 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-3 .hover > .u-container-layout .u-image-1 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-3 .u-image-2,
.u-section-3 .u-image-2:before,
.u-section-3 .u-image-2 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 :hover > .u-container-layout .u-image-2 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-3 .hover > .u-container-layout .u-image-2 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-3 .u-image-3,
.u-section-3 .u-image-3:before,
.u-section-3 .u-image-3 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 :hover > .u-container-layout .u-image-3 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-3 .hover > .u-container-layout .u-image-3 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-3 .u-image-4,
.u-section-3 .u-image-4:before,
.u-section-3 .u-image-4 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 :hover > .u-container-layout .u-image-4 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-3 .hover > .u-container-layout .u-image-4 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-3 .u-image-5,
.u-section-3 .u-image-5:before,
.u-section-3 .u-image-5 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 :hover > .u-container-layout .u-image-5 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-3 .hover > .u-container-layout .u-image-5 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-3 .u-text-1,
.u-section-3 .u-text-1:before,
.u-section-3 .u-text-1 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 :hover > .u-container-layout .u-text-1 {
  text-decoration: underline !important;
}

.u-section-3 .hover > .u-container-layout .u-text-1 {
  text-decoration: underline !important;
}

.u-section-3 .u-text-3,
.u-section-3 .u-text-3:before,
.u-section-3 .u-text-3 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 :hover > .u-container-layout .u-text-3 {
  text-decoration: underline !important;
}

.u-section-3 .hover > .u-container-layout .u-text-3 {
  text-decoration: underline !important;
}

.u-section-3 .u-text-5,
.u-section-3 .u-text-5:before,
.u-section-3 .u-text-5 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 :hover > .u-container-layout .u-text-5 {
  text-decoration: underline !important;
}

.u-section-3 .hover > .u-container-layout .u-text-5 {
  text-decoration: underline !important;
}

.u-section-3 .u-text-7,
.u-section-3 .u-text-7:before,
.u-section-3 .u-text-7 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 :hover > .u-container-layout .u-text-7 {
  text-decoration: underline !important;
}

.u-section-3 .hover > .u-container-layout .u-text-7 {
  text-decoration: underline !important;
}

.u-section-3 .u-text-9,
.u-section-3 .u-text-9:before,
.u-section-3 .u-text-9 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 :hover > .u-container-layout .u-text-9 {
  text-decoration: underline !important;
}

.u-section-3 .hover > .u-container-layout .u-text-9 {
  text-decoration: underline !important;
}

.u-section-3 .u-text-11,
.u-section-3 .u-text-11:before,
.u-section-3 .u-text-11 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-3 :hover > .u-container-layout .u-text-11 {
  text-decoration: underline !important;
}

.u-section-3 .hover > .u-container-layout .u-text-11 {
  text-decoration: underline !important;
} .u-section-4 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-4 .u-sheet-1 {
  min-height: 750px;
}

.u-section-4 .u-list-1 {
  margin-bottom: -60px;
  margin-top: 20px;
}

.u-section-4 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 710px;
  grid-gap: 10px;
}

.u-section-4 .u-container-layout-1 {
  padding: 10px;
}

.u-section-4 .u-image-1 {
  height: 235px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-4 .u-text-1 {
  font-weight: 400;
  transition-duration: 0.5s;
  margin: 20px 0 0;
}

.u-section-4 .u-text-2 {
  font-size: 1rem;
  font-weight: 300;
  margin: 20px 0 0;
}

.u-section-4 .u-container-layout-2 {
  padding: 10px;
}

.u-section-4 .u-image-2 {
  height: 235px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-4 .u-text-3 {
  font-weight: 400;
  transition-duration: 0.5s;
  margin: 20px 0 0;
}

.u-section-4 .u-text-4 {
  font-size: 1rem;
  font-weight: 300;
  margin: 20px 0 0;
}

.u-section-4 .u-container-layout-3 {
  padding: 10px;
}

.u-section-4 .u-image-3 {
  height: 235px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-4 .u-text-5 {
  font-weight: 400;
  transition-duration: 0.5s;
  margin: 20px 0 0;
}

.u-section-4 .u-text-6 {
  font-size: 1rem;
  font-weight: 300;
  margin: 20px 0 0;
}

.u-section-4 .u-container-layout-4 {
  padding: 10px;
}

.u-section-4 .u-image-4 {
  height: 235px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-4 .u-text-7 {
  font-weight: 400;
  transition-duration: 0.5s;
  margin: 20px 0 0;
}

.u-section-4 .u-text-8 {
  font-size: 1rem;
  font-weight: 300;
  margin: 20px 0 0;
}

.u-section-4 .u-container-layout-5 {
  padding: 10px;
}

.u-section-4 .u-image-5 {
  height: 235px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-4 .u-text-9 {
  font-weight: 400;
  transition-duration: 0.5s;
  margin: 20px 0 0;
}

.u-section-4 .u-text-10 {
  font-size: 1rem;
  font-weight: 300;
  margin: 20px 0 0;
}

.u-section-4 .u-container-layout-6 {
  padding: 10px;
}

.u-section-4 .u-image-6 {
  height: 235px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 5px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-4 .u-text-11 {
  font-weight: 400;
  transition-duration: 0.5s;
  margin: 20px 0 0;
}

.u-section-4 .u-text-12 {
  font-size: 1rem;
  font-weight: 300;
  margin: 20px 0 0;
}

@media (max-width: 1199px) {
  .u-section-4 .u-sheet-1 {
    min-height: 712px;
  }

  .u-section-4 .u-list-1 {
    margin-bottom: -98px;
  }

  .u-section-4 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 591px;
  }

  .u-section-4 .u-image-1 {
    height: 194px;
  }

  .u-section-4 .u-image-2 {
    height: 194px;
  }

  .u-section-4 .u-image-3 {
    height: 194px;
  }

  .u-section-4 .u-image-4 {
    height: 194px;
  }

  .u-section-4 .u-image-5 {
    height: 194px;
  }

  .u-section-4 .u-image-6 {
    height: 194px;
  }
}

@media (max-width: 991px) {
  .u-section-4 .u-sheet-1 {
    min-height: 830px;
  }

  .u-section-4 .u-list-1 {
    margin-bottom: 20px;
  }

  .u-section-4 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 1047px;
  }

  .u-section-4 .u-image-1 {
    height: 229px;
  }

  .u-section-4 .u-image-2 {
    height: 229px;
  }

  .u-section-4 .u-image-3 {
    height: 229px;
  }

  .u-section-4 .u-image-4 {
    height: 229px;
  }

  .u-section-4 .u-image-5 {
    height: 229px;
  }

  .u-section-4 .u-image-6 {
    height: 229px;
  }
}

@media (max-width: 767px) {
  .u-section-4 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-4 .u-image-1 {
    height: 322px;
  }

  .u-section-4 .u-image-2 {
    height: 322px;
  }

  .u-section-4 .u-image-3 {
    height: 322px;
  }

  .u-section-4 .u-image-4 {
    height: 322px;
  }

  .u-section-4 .u-image-5 {
    height: 322px;
  }

  .u-section-4 .u-image-6 {
    height: 322px;
  }
}

@media (max-width: 575px) {
  .u-section-4 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-4 .u-image-1 {
    height: 198px;
  }

  .u-section-4 .u-image-2 {
    height: 198px;
  }

  .u-section-4 .u-image-3 {
    height: 198px;
  }

  .u-section-4 .u-image-4 {
    height: 198px;
  }

  .u-section-4 .u-image-5 {
    height: 198px;
  }

  .u-section-4 .u-image-6 {
    height: 198px;
  }
}

.u-section-4 .u-image-6,
.u-section-4 .u-image-6:before,
.u-section-4 .u-image-6 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-4 :hover > .u-container-layout .u-image-6 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-4 .hover > .u-container-layout .u-image-6 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-4 .u-image-1,
.u-section-4 .u-image-1:before,
.u-section-4 .u-image-1 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-4 :hover > .u-container-layout .u-image-1 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-4 .hover > .u-container-layout .u-image-1 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-4 .u-image-2,
.u-section-4 .u-image-2:before,
.u-section-4 .u-image-2 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-4 :hover > .u-container-layout .u-image-2 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-4 .hover > .u-container-layout .u-image-2 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-4 .u-image-3,
.u-section-4 .u-image-3:before,
.u-section-4 .u-image-3 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-4 :hover > .u-container-layout .u-image-3 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-4 .hover > .u-container-layout .u-image-3 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-4 .u-image-4,
.u-section-4 .u-image-4:before,
.u-section-4 .u-image-4 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-4 :hover > .u-container-layout .u-image-4 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-4 .hover > .u-container-layout .u-image-4 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-4 .u-image-5,
.u-section-4 .u-image-5:before,
.u-section-4 .u-image-5 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-4 :hover > .u-container-layout .u-image-5 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-4 .hover > .u-container-layout .u-image-5 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-section-4 .u-text-1,
.u-section-4 .u-text-1:before,
.u-section-4 .u-text-1 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-4 :hover > .u-container-layout .u-text-1 {
  text-decoration: underline !important;
}

.u-section-4 .hover > .u-container-layout .u-text-1 {
  text-decoration: underline !important;
}

.u-section-4 .u-text-3,
.u-section-4 .u-text-3:before,
.u-section-4 .u-text-3 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-4 :hover > .u-container-layout .u-text-3 {
  text-decoration: underline !important;
}

.u-section-4 .hover > .u-container-layout .u-text-3 {
  text-decoration: underline !important;
}

.u-section-4 .u-text-5,
.u-section-4 .u-text-5:before,
.u-section-4 .u-text-5 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-4 :hover > .u-container-layout .u-text-5 {
  text-decoration: underline !important;
}

.u-section-4 .hover > .u-container-layout .u-text-5 {
  text-decoration: underline !important;
}

.u-section-4 .u-text-7,
.u-section-4 .u-text-7:before,
.u-section-4 .u-text-7 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-4 :hover > .u-container-layout .u-text-7 {
  text-decoration: underline !important;
}

.u-section-4 .hover > .u-container-layout .u-text-7 {
  text-decoration: underline !important;
}

.u-section-4 .u-text-9,
.u-section-4 .u-text-9:before,
.u-section-4 .u-text-9 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-4 :hover > .u-container-layout .u-text-9 {
  text-decoration: underline !important;
}

.u-section-4 .hover > .u-container-layout .u-text-9 {
  text-decoration: underline !important;
}

.u-section-4 .u-text-11,
.u-section-4 .u-text-11:before,
.u-section-4 .u-text-11 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-4 :hover > .u-container-layout .u-text-11 {
  text-decoration: underline !important;
}

.u-section-4 .hover > .u-container-layout .u-text-11 {
  text-decoration: underline !important;
}