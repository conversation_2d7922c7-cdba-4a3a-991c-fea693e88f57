.u-block-9b8d-2 {
  position: absolute;
  left: auto;
  width: auto;
  height: auto;
  right: calc(((100% - 1200px) / 2)  + 258px);
  bottom: 29px;
}

.u-block-9b8d-3 {
  position: absolute;
  left: auto;
  background-image: none;
  --radius: 150px;
  width: 40px;
  height: 40px;
  right: calc(((100% - 1200px) / 2)  + 499px);
  bottom: 13px;
  padding: 8px;
}

.u-block-9b8d-4 {
  position: absolute;
  background-image: none;
  --radius: 150px;
  width: 40px;
  height: 40px;
  left: auto;
  right: calc(((100% - 1200px) / 2)  + 64px);
  bottom: 13px;
  padding: 8px;
}

@media (max-width: 1199px) {
  .u-block-9b8d-2 {
    right: calc(((100% - 1000px) / 2)  + 191px);
  }

  .u-block-9b8d-3 {
    top: 463px;
    right: calc(((100% - 1000px) / 2)  + 419px);
  }

  .u-block-9b8d-4 {
    right: 0;
    top: 559px;
  }
}

@media (max-width: 991px) {
  .u-block-9b8d-2 {
    right: calc(((100% - 780px) / 2)  + 81px);
  }

  .u-block-9b8d-3 {
    right: calc(((100% - 780px) / 2)  + 327px);
  }
}

@media (max-width: 767px) {
  .u-block-9b8d-2 {
    right: calc(((100% - 540px) / 2)  + -39px);
  }

  .u-block-9b8d-3 {
    right: calc(((100% - 540px) / 2)  + 226px);
  }
}

@media (max-width: 575px) {
  .u-block-9b8d-2 {
    right: calc(((100% - 340px) / 2)  + -139px);
  }

  .u-block-9b8d-3 {
    right: calc(((100% - 340px) / 2)  + 142px);
  }
}

 .u-section-1-1 {
  background-image: url("images/hero-eni.webp"), url("images/hero-eni.webp");
  background-position: 50% 50%;
  min-height: 600px;
}

.u-section-1-1 .u-layout-wrap-1 {
  margin-top: 260px;
  margin-bottom: 0;
}

.u-section-1-1 .u-layout-cell-1 {
  min-height: 340px;
}

.u-section-1-1 .u-container-layout-1 {
  padding: 30px;
}

.u-section-1-1 .u-text-1 {
  font-weight: 400;
  font-size: 1.125rem;
  margin: 0 329px 0 0;
}

.u-section-1-1 .u-text-2 {
  --radius: 20px;
  margin: 30px 93px 0 1px;
}

.u-section-1-1 .u-text-3 {
  font-weight: 700;
  font-size: 1.5rem;
  margin: 23px -1px 0;
}

.u-section-1-1 .u-layout-cell-2 {
  min-height: 340px;
}

.u-section-1-1 .u-container-layout-2 {
  padding: 30px;
}

.u-section-1-1 .u-text-4 {
  font-weight: 400;
  font-size: 1.125rem;
  margin: 0 45px 0 0;
}

.u-section-1-1 .u-group-1 {
  --radius: 20px;
  min-height: 192px;
  height: auto;
  width: 538px;
  margin: 32px 0 0;
}

.u-section-1-1 .u-container-layout-3 {
  padding-left: 30px;
  padding-right: 30px;
}

.u-section-1-1 .u-text-5 {
  font-weight: 400;
  font-size: 1.5rem;
  margin: 30px 268px 0 0;
}

.u-section-1-1 .u-image-1 {
  width: 217px;
  height: 130px;
  --radius: 20px;
  margin: -29px 0 0 auto;
}

@media (max-width: 1199px) {
   .u-section-1-1 {
    min-height: 443px;
  }

  .u-section-1-1 .u-layout-cell-1 {
    min-height: 283px;
  }

  .u-section-1-1 .u-text-1 {
    margin-right: 229px;
  }

  .u-section-1-1 .u-text-2 {
    margin-right: 0;
    margin-left: 0;
  }

  .u-section-1-1 .u-text-3 {
    margin-right: 0;
    margin-left: 0;
  }

  .u-section-1-1 .u-layout-cell-2 {
    min-height: 283px;
  }

  .u-section-1-1 .u-text-4 {
    margin-right: 0;
  }

  .u-section-1-1 .u-group-1 {
    width: 440px;
    height: auto;
  }

  .u-section-1-1 .u-container-layout-3 {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .u-section-1-1 .u-text-5 {
    margin-right: 170px;
  }

  .u-section-1-1 .u-image-1 {
    margin-top: 20px;
  }
}

@media (max-width: 991px) {
   .u-section-1-1 {
    min-height: 260px;
  }

  .u-section-1-1 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-1-1 .u-text-1 {
    margin-right: 119px;
  }

  .u-section-1-1 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-1-1 .u-group-1 {
    width: 330px;
  }

  .u-section-1-1 .u-text-5 {
    margin-right: 60px;
  }

  .u-section-1-1 .u-image-1 {
    width: -13px;
    height: -8px;
    margin-top: -29px;
  }
}

@media (max-width: 767px) {
   .u-section-1-1 {
    min-height: 360px;
  }

  .u-section-1-1 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1-1 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1-1 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-1-1 .u-text-1 {
    margin-right: 0;
  }

  .u-section-1-1 .u-text-3 {
    font-size: 1.125rem;
  }

  .u-section-1-1 .u-group-1 {
    width: 320px;
  }

  .u-section-1-1 .u-text-5 {
    margin-right: 50px;
  }
}

 .u-section-1-2 {
  background-image: url("images/hero-small4x.webp"), url("images/hero-small4x.webp");
  background-position: 50% 50%;
  min-height: 720px;
}

.u-section-1-2 .u-layout-wrap-1 {
  margin-top: 391px;
  margin-bottom: -11px;
}

.u-section-1-2 .u-layout-cell-1 {
  min-height: 340px;
}

.u-section-1-2 .u-container-layout-1 {
  padding: 30px;
}

.u-section-1-2 .u-text-1 {
  font-weight: 400;
  font-size: 1.125rem;
  margin: 0 329px 0 0;
}

.u-section-1-2 .u-text-2 {
  --radius: 20px;
  margin: 30px 93px 0 1px;
}

.u-section-1-2 .u-text-3 {
  font-weight: 700;
  font-size: 1.5rem;
  margin: 23px -1px 0;
}

.u-section-1-2 .u-layout-cell-2 {
  min-height: 340px;
}

.u-section-1-2 .u-container-layout-2 {
  padding: 30px;
}

.u-section-1-2 .u-text-4 {
  font-weight: 400;
  font-size: 1.125rem;
  margin: 0 45px 0 0;
}

.u-section-1-2 .u-group-1 {
  --radius: 20px;
  min-height: 192px;
  height: auto;
  width: 538px;
  margin: 32px 0 0;
}

.u-section-1-2 .u-container-layout-3 {
  padding-left: 30px;
  padding-right: 30px;
}

.u-section-1-2 .u-text-5 {
  font-weight: 400;
  font-size: 1.5rem;
  margin: 30px 268px 0 0;
}

.u-section-1-2 .u-image-1 {
  width: 217px;
  height: 130px;
  --radius: 20px;
  margin: -29px 0 0 auto;
}

@media (max-width: 1199px) {
   .u-section-1-2 {
    min-height: 543px;
  }

  .u-section-1-2 .u-layout-cell-1 {
    min-height: 283px;
  }

  .u-section-1-2 .u-text-1 {
    margin-right: 229px;
  }

  .u-section-1-2 .u-text-2 {
    margin-right: 0;
    margin-left: 0;
  }

  .u-section-1-2 .u-text-3 {
    margin-right: 0;
    margin-left: 0;
  }

  .u-section-1-2 .u-layout-cell-2 {
    min-height: 283px;
  }

  .u-section-1-2 .u-text-4 {
    margin-right: 0;
  }

  .u-section-1-2 .u-group-1 {
    width: 440px;
    height: auto;
  }

  .u-section-1-2 .u-container-layout-3 {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .u-section-1-2 .u-text-5 {
    margin-right: 170px;
  }

  .u-section-1-2 .u-image-1 {
    margin-top: 20px;
  }
}

@media (max-width: 991px) {
   .u-section-1-2 {
    min-height: 360px;
  }

  .u-section-1-2 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-1-2 .u-text-1 {
    margin-right: 119px;
  }

  .u-section-1-2 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-1-2 .u-group-1 {
    width: 330px;
  }

  .u-section-1-2 .u-text-5 {
    margin-right: 60px;
  }

  .u-section-1-2 .u-image-1 {
    width: -13px;
    height: -8px;
    margin-top: -29px;
  }
}

@media (max-width: 767px) {
   .u-section-1-2 {
    min-height: 460px;
  }

  .u-section-1-2 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1-2 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1-2 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-1-2 .u-text-1 {
    margin-right: 0;
  }

  .u-section-1-2 .u-text-3 {
    font-size: 1.125rem;
  }

  .u-section-1-2 .u-group-1 {
    width: 320px;
  }

  .u-section-1-2 .u-text-5 {
    margin-right: 50px;
  }
}

 .u-section-1-3 {
  background-image: url("images/hero-ldc4x.webp"), url("images/hero-ldc4x.webp");
  min-height: 720px;
  background-position: 50% 50%;
}

.u-section-1-3 .u-layout-wrap-1 {
  margin-top: 391px;
  margin-bottom: -11px;
}

.u-section-1-3 .u-layout-cell-1 {
  min-height: 340px;
}

.u-section-1-3 .u-container-layout-1 {
  padding: 30px;
}

.u-section-1-3 .u-text-1 {
  font-weight: 400;
  font-size: 1.125rem;
  margin: 0 329px 0 0;
}

.u-section-1-3 .u-text-2 {
  --radius: 20px;
  margin: 30px 93px 0 1px;
}

.u-section-1-3 .u-text-3 {
  font-weight: 700;
  font-size: 1.5rem;
  margin: 23px -1px 0;
}

.u-section-1-3 .u-layout-cell-2 {
  min-height: 340px;
}

.u-section-1-3 .u-container-layout-2 {
  padding: 30px;
}

.u-section-1-3 .u-text-4 {
  font-weight: 400;
  font-size: 1.125rem;
  margin: 0 45px 0 0;
}

.u-section-1-3 .u-group-1 {
  --radius: 20px;
  min-height: 192px;
  height: auto;
  width: 538px;
  margin: 32px 0 0;
}

.u-section-1-3 .u-container-layout-3 {
  padding-left: 30px;
  padding-right: 30px;
}

.u-section-1-3 .u-text-5 {
  font-weight: 400;
  font-size: 1.5rem;
  margin: 30px 268px 0 0;
}

.u-section-1-3 .u-image-1 {
  width: 217px;
  height: 130px;
  --radius: 20px;
  margin: -29px 0 0 auto;
}

@media (max-width: 1199px) {
   .u-section-1-3 {
    min-height: 543px;
  }

  .u-section-1-3 .u-layout-cell-1 {
    min-height: 283px;
  }

  .u-section-1-3 .u-text-1 {
    margin-right: 229px;
  }

  .u-section-1-3 .u-text-2 {
    margin-right: 0;
    margin-left: 0;
  }

  .u-section-1-3 .u-text-3 {
    margin-right: 0;
    margin-left: 0;
  }

  .u-section-1-3 .u-layout-cell-2 {
    min-height: 283px;
  }

  .u-section-1-3 .u-text-4 {
    margin-right: 0;
  }

  .u-section-1-3 .u-group-1 {
    width: 440px;
    height: auto;
  }

  .u-section-1-3 .u-container-layout-3 {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .u-section-1-3 .u-text-5 {
    margin-right: 170px;
  }

  .u-section-1-3 .u-image-1 {
    margin-top: 20px;
  }
}

@media (max-width: 991px) {
   .u-section-1-3 {
    min-height: 360px;
  }

  .u-section-1-3 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-1-3 .u-text-1 {
    margin-right: 119px;
  }

  .u-section-1-3 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-1-3 .u-group-1 {
    width: 330px;
  }

  .u-section-1-3 .u-text-5 {
    margin-right: 60px;
  }

  .u-section-1-3 .u-image-1 {
    width: -13px;
    height: -8px;
    margin-top: -29px;
  }
}

@media (max-width: 767px) {
   .u-section-1-3 {
    min-height: 460px;
  }

  .u-section-1-3 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1-3 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1-3 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-1-3 .u-text-1 {
    margin-right: 0;
  }

  .u-section-1-3 .u-text-3 {
    font-size: 1.125rem;
  }

  .u-section-1-3 .u-group-1 {
    width: 320px;
  }

  .u-section-1-3 .u-text-5 {
    margin-right: 50px;
  }
}

 .u-section-1-4 {
  background-image: url("images/hero-roof4x.webp"), url("images/hero-roof4x.webp");
  min-height: 720px;
  background-position: 50% 50%;
}

.u-section-1-4 .u-layout-wrap-1 {
  margin-top: 391px;
  margin-bottom: -11px;
}

.u-section-1-4 .u-layout-cell-1 {
  min-height: 340px;
}

.u-section-1-4 .u-container-layout-1 {
  padding: 30px;
}

.u-section-1-4 .u-text-1 {
  font-weight: 400;
  font-size: 1.125rem;
  margin: 0 329px 0 0;
}

.u-section-1-4 .u-text-2 {
  --radius: 20px;
  margin: 30px 93px 0 1px;
}

.u-section-1-4 .u-text-3 {
  font-weight: 700;
  font-size: 1.5rem;
  margin: 23px -1px 0;
}

.u-section-1-4 .u-layout-cell-2 {
  min-height: 340px;
}

.u-section-1-4 .u-container-layout-2 {
  padding: 30px;
}

.u-section-1-4 .u-text-4 {
  font-weight: 400;
  font-size: 1.125rem;
  margin: 0 45px 0 0;
}

.u-section-1-4 .u-group-1 {
  --radius: 20px;
  min-height: 192px;
  height: auto;
  width: 538px;
  margin: 32px 0 0;
}

.u-section-1-4 .u-container-layout-3 {
  padding-left: 30px;
  padding-right: 30px;
}

.u-section-1-4 .u-text-5 {
  font-weight: 400;
  font-size: 1.5rem;
  margin: 30px 268px 0 0;
}

.u-section-1-4 .u-image-1 {
  width: 217px;
  height: 130px;
  --radius: 20px;
  margin: -29px 0 0 auto;
}

@media (max-width: 1199px) {
   .u-section-1-4 {
    min-height: 543px;
  }

  .u-section-1-4 .u-layout-cell-1 {
    min-height: 283px;
  }

  .u-section-1-4 .u-text-1 {
    margin-right: 229px;
  }

  .u-section-1-4 .u-text-2 {
    margin-right: 0;
    margin-left: 0;
  }

  .u-section-1-4 .u-text-3 {
    margin-right: 0;
    margin-left: 0;
  }

  .u-section-1-4 .u-layout-cell-2 {
    min-height: 283px;
  }

  .u-section-1-4 .u-text-4 {
    margin-right: 0;
  }

  .u-section-1-4 .u-group-1 {
    width: 440px;
    height: auto;
  }

  .u-section-1-4 .u-container-layout-3 {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .u-section-1-4 .u-text-5 {
    margin-right: 170px;
  }

  .u-section-1-4 .u-image-1 {
    margin-top: 20px;
  }
}

@media (max-width: 991px) {
   .u-section-1-4 {
    min-height: 360px;
  }

  .u-section-1-4 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-1-4 .u-text-1 {
    margin-right: 119px;
  }

  .u-section-1-4 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-1-4 .u-group-1 {
    width: 330px;
  }

  .u-section-1-4 .u-text-5 {
    margin-right: 60px;
  }

  .u-section-1-4 .u-image-1 {
    width: -13px;
    height: -8px;
    margin-top: -29px;
  }
}

@media (max-width: 767px) {
   .u-section-1-4 {
    min-height: 460px;
  }

  .u-section-1-4 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1-4 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1-4 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-1-4 .u-text-1 {
    margin-right: 0;
  }

  .u-section-1-4 .u-text-3 {
    font-size: 1.125rem;
  }

  .u-section-1-4 .u-group-1 {
    width: 320px;
  }

  .u-section-1-4 .u-text-5 {
    margin-right: 50px;
  }
}

 .u-section-1-5 {
  background-image: url("images/hero-jkuat4x.webp"), url("images/hero-jkuat4x.webp");
  min-height: 720px;
  background-position: 50% 50%;
}

.u-section-1-5 .u-layout-wrap-1 {
  margin-top: 391px;
  margin-bottom: -11px;
}

.u-section-1-5 .u-layout-cell-1 {
  min-height: 340px;
}

.u-section-1-5 .u-container-layout-1 {
  padding: 30px;
}

.u-section-1-5 .u-text-1 {
  font-weight: 400;
  font-size: 1.125rem;
  margin: 0 329px 0 0;
}

.u-section-1-5 .u-text-2 {
  --radius: 20px;
  margin: 30px 93px 0 1px;
}

.u-section-1-5 .u-text-3 {
  font-weight: 700;
  font-size: 1.5rem;
  margin: 23px -1px 0;
}

.u-section-1-5 .u-layout-cell-2 {
  min-height: 340px;
}

.u-section-1-5 .u-container-layout-2 {
  padding: 30px;
}

.u-section-1-5 .u-text-4 {
  font-weight: 400;
  font-size: 1.125rem;
  margin: 0 45px 0 0;
}

.u-section-1-5 .u-group-1 {
  --radius: 20px;
  min-height: 192px;
  height: auto;
  width: 538px;
  margin: 32px 0 0;
}

.u-section-1-5 .u-container-layout-3 {
  padding-left: 30px;
  padding-right: 30px;
}

.u-section-1-5 .u-text-5 {
  font-weight: 400;
  font-size: 1.5rem;
  margin: 30px 268px 0 0;
}

.u-section-1-5 .u-image-1 {
  width: 217px;
  height: 130px;
  --radius: 20px;
  margin: -29px 0 0 auto;
}

@media (max-width: 1199px) {
   .u-section-1-5 {
    min-height: 543px;
  }

  .u-section-1-5 .u-layout-cell-1 {
    min-height: 283px;
  }

  .u-section-1-5 .u-text-1 {
    margin-right: 229px;
  }

  .u-section-1-5 .u-text-2 {
    margin-right: 0;
    margin-left: 0;
  }

  .u-section-1-5 .u-text-3 {
    margin-right: 0;
    margin-left: 0;
  }

  .u-section-1-5 .u-layout-cell-2 {
    min-height: 283px;
  }

  .u-section-1-5 .u-text-4 {
    margin-right: 0;
  }

  .u-section-1-5 .u-group-1 {
    width: 440px;
    height: auto;
  }

  .u-section-1-5 .u-container-layout-3 {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .u-section-1-5 .u-text-5 {
    margin-right: 170px;
  }

  .u-section-1-5 .u-image-1 {
    margin-top: 20px;
  }
}

@media (max-width: 991px) {
   .u-section-1-5 {
    min-height: 360px;
  }

  .u-section-1-5 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-1-5 .u-text-1 {
    margin-right: 119px;
  }

  .u-section-1-5 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-1-5 .u-group-1 {
    width: 330px;
  }

  .u-section-1-5 .u-text-5 {
    margin-right: 60px;
  }

  .u-section-1-5 .u-image-1 {
    width: -13px;
    height: -8px;
    margin-top: -29px;
  }
}

@media (max-width: 767px) {
   .u-section-1-5 {
    min-height: 460px;
  }

  .u-section-1-5 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1-5 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1-5 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-1-5 .u-text-1 {
    margin-right: 0;
  }

  .u-section-1-5 .u-text-3 {
    font-size: 1.125rem;
  }

  .u-section-1-5 .u-group-1 {
    width: 320px;
  }

  .u-section-1-5 .u-text-5 {
    margin-right: 50px;
  }
}

 .u-section-1-6 {
  background-image: url("images/hero-gowgate2x.webp"), url("images/hero-gowgate2x.webp");
  background-position: 50% 50%;
  min-height: 720px;
}

.u-section-1-6 .u-layout-wrap-1 {
  margin-top: 238px;
  margin-bottom: 60px;
}

.u-section-1-6 .u-layout-cell-1 {
  min-height: 340px;
}

.u-section-1-6 .u-container-layout-1 {
  padding: 30px;
}

.u-section-1-6 .u-text-1 {
  font-weight: 400;
  font-size: 1.125rem;
  margin: 0 329px 0 0;
}

.u-section-1-6 .u-text-2 {
  --radius: 20px;
  margin: 30px 93px 0 1px;
}

.u-section-1-6 .u-text-3 {
  font-weight: 700;
  font-size: 1.5rem;
  margin: 23px -1px 0;
}

.u-section-1-6 .u-layout-cell-2 {
  min-height: 340px;
}

.u-section-1-6 .u-container-layout-2 {
  padding: 30px;
}

.u-section-1-6 .u-text-4 {
  font-weight: 400;
  font-size: 1.125rem;
  margin: 0 45px 0 0;
}

.u-section-1-6 .u-group-1 {
  --radius: 20px;
  min-height: 192px;
  height: auto;
  width: 538px;
  margin: 32px 0 0;
}

.u-section-1-6 .u-container-layout-3 {
  padding-left: 30px;
  padding-right: 30px;
}

.u-section-1-6 .u-text-5 {
  font-weight: 400;
  font-size: 1.5rem;
  margin: 30px 268px 0 0;
}

.u-section-1-6 .u-image-1 {
  width: 217px;
  height: 130px;
  --radius: 20px;
  margin: -29px 0 0 auto;
}

@media (max-width: 1199px) {
   .u-section-1-6 {
    min-height: 543px;
  }

  .u-section-1-6 .u-layout-cell-1 {
    min-height: 283px;
  }

  .u-section-1-6 .u-text-1 {
    margin-right: 229px;
  }

  .u-section-1-6 .u-text-2 {
    margin-right: 0;
    margin-left: 0;
  }

  .u-section-1-6 .u-text-3 {
    margin-right: 0;
    margin-left: 0;
  }

  .u-section-1-6 .u-layout-cell-2 {
    min-height: 283px;
  }

  .u-section-1-6 .u-text-4 {
    margin-right: 0;
  }

  .u-section-1-6 .u-group-1 {
    width: 440px;
    height: auto;
  }

  .u-section-1-6 .u-container-layout-3 {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .u-section-1-6 .u-text-5 {
    margin-right: 170px;
  }

  .u-section-1-6 .u-image-1 {
    margin-top: 20px;
  }
}

@media (max-width: 991px) {
   .u-section-1-6 {
    min-height: 360px;
  }

  .u-section-1-6 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-1-6 .u-text-1 {
    margin-right: 119px;
  }

  .u-section-1-6 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-1-6 .u-group-1 {
    width: 330px;
  }

  .u-section-1-6 .u-text-5 {
    margin-right: 60px;
  }

  .u-section-1-6 .u-image-1 {
    width: -13px;
    height: -8px;
    margin-top: -29px;
  }
}

@media (max-width: 767px) {
   .u-section-1-6 {
    min-height: 460px;
  }

  .u-section-1-6 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1-6 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1-6 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }
}

@media (max-width: 575px) {
  .u-section-1-6 .u-text-1 {
    margin-right: 0;
  }

  .u-section-1-6 .u-text-3 {
    font-size: 1.125rem;
  }

  .u-section-1-6 .u-group-1 {
    width: 320px;
  }

  .u-section-1-6 .u-text-5 {
    margin-right: 50px;
  }
} .u-section-2 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-2 .u-sheet-1 {
  min-height: 759px;
}

.u-section-2 .u-text-1 {
  font-weight: 500;
  font-size: 2.75rem;
  margin: 40px 0 0;
}

.u-section-2 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: -45px;
}

.u-section-2 .u-layout-cell-1 {
  min-height: 202px;
}

.u-section-2 .u-container-layout-1 {
  padding: 0;
}

.u-section-2 .u-text-2 {
  font-weight: 300;
  margin: 0;
}

.u-section-2 .u-layout-cell-2 {
  min-height: 202px;
}

.u-section-2 .u-container-layout-2 {
  padding: 0 0 30px;
}

.u-section-2 .u-text-3 {
  font-weight: 400;
  font-size: 1.5rem;
  margin: 0 0 0 30px;
}

.u-section-2 .u-list-1 {
  width: 303px;
  margin: 20px 50px 0 30px;
}

.u-section-2 .u-repeater-1 {
  grid-auto-columns: calc(50% - 5px);
  grid-template-columns: repeat(2, calc(50% - 5px));
  min-height: 120px;
  --gap: 10px;
}

.u-section-2 .u-container-layout-3 {
  padding: 0;
}

.u-section-2 .u-text-4 {
  font-size: 1rem;
  font-weight: 400;
  margin: 0 auto 0 0;
}

.u-section-2 .u-container-layout-4 {
  padding: 0;
}

.u-section-2 .u-text-5 {
  font-size: 1rem;
  font-weight: 400;
  margin: 0 auto 0 0;
}

.u-section-2 .u-layout-cell-3 {
  min-height: 409px;
}

.u-section-2 .u-container-layout-5 {
  padding: 30px;
}

.u-section-2 .u-image-1 {
  width: 570px;
  height: 327px;
  --radius: 20px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  margin: 0 auto;
}

.u-section-2 .u-layout-cell-4 {
  min-height: 611px;
}

.u-section-2 .u-container-layout-6 {
  padding: 30px;
}

.u-section-2 .u-group-1 {
  --radius: 20px;
  min-height: 509px;
  height: auto;
  margin: 0;
}

.u-section-2 .u-container-layout-7 {
  padding: 20px;
}

.u-section-2 .u-image-2 {
  height: 356px;
  margin-top: 0;
  margin-bottom: 0;
  --radius: 20px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
}

.u-section-2 .u-text-6 {
  margin-top: 20px;
  margin-bottom: 0;
  font-weight: 400;
  font-size: 1.5rem;
}

.u-section-2 .u-btn-1 {
  margin: 11px auto 0 0;
  padding: 0;
}

@media (max-width: 1199px) {
  .u-section-2 .u-sheet-1 {
    min-height: 835px;
  }

  .u-section-2 .u-layout-wrap-1 {
    margin-bottom: 40px;
    position: relative;
  }

  .u-section-2 .u-layout-cell-1 {
    min-height: 168px;
  }

  .u-section-2 .u-layout-cell-2 {
    min-height: 186px;
  }

  .u-section-2 .u-text-3 {
    margin-left: 0;
  }

  .u-section-2 .u-list-1 {
    width: 300px;
    margin-right: 16px;
    margin-left: 0;
  }

  .u-section-2 .u-repeater-1 {
    min-height: 119px;
    grid-gap: 10px;
  }

  .u-section-2 .u-layout-cell-3 {
    min-height: 374px;
  }

  .u-section-2 .u-image-1 {
    width: 557px;
    height: 320px;
  }

  .u-section-2 .u-layout-cell-4 {
    min-height: 639px;
  }

  .u-section-2 .u-group-1 {
    min-height: 458px;
    margin-right: initial;
    margin-left: initial;
    height: auto;
  }

  .u-section-2 .u-image-2 {
    filter: brightness(0.75);
    height: 291px;
  }
}

@media (max-width: 991px) {
  .u-section-2 .u-sheet-1 {
    min-height: 856px;
  }

  .u-section-2 .u-layout-cell-1 {
    min-height: 202px;
  }

  .u-section-2 .u-text-2 {
    width: auto;
    margin-right: 20px;
  }

  .u-section-2 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-2 .u-text-3 {
    width: auto;
  }

  .u-section-2 .u-list-1 {
    margin-left: auto;
    margin-right: auto;
  }

  .u-section-2 .u-layout-cell-3 {
    min-height: 450px;
  }

  .u-section-2 .u-layout-cell-4 {
    min-height: 100px;
  }

  .u-section-2 .u-image-2 {
    height: 627px;
  }
}

@media (max-width: 767px) {
  .u-section-2 .u-sheet-1 {
    min-height: 726px;
  }

  .u-section-2 .u-text-1 {
    font-size: 2.25rem;
  }

  .u-section-2 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-2 .u-text-3 {
    margin-top: 20px;
  }

  .u-section-2 .u-list-1 {
    margin-left: 0;
  }

  .u-section-2 .u-layout-cell-3 {
    min-height: 312px;
  }

  .u-section-2 .u-container-layout-5 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-2 .u-image-1 {
    width: 520px;
    height: 299px;
  }

  .u-section-2 .u-layout-cell-4 {
    min-height: 578px;
  }

  .u-section-2 .u-container-layout-6 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-2 .u-group-1 {
    min-height: 518px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-2 .u-image-2 {
    height: 441px;
  }
}

@media (max-width: 575px) {
  .u-section-2 .u-sheet-1 {
    min-height: 605px;
  }

  .u-section-2 .u-text-1 {
    font-size: 1.875rem;
  }

  .u-section-2 .u-text-2 {
    margin-right: 0;
  }

  .u-section-2 .u-layout-cell-3 {
    min-height: 196px;
  }

  .u-section-2 .u-image-1 {
    width: 320px;
    height: 184px;
  }

  .u-section-2 .u-layout-cell-4 {
    min-height: 497px;
  }

  .u-section-2 .u-container-layout-6 {
    padding-left: 0;
    padding-right: 0;
  }

  .u-section-2 .u-group-1 {
    min-height: 437px;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-2 .u-image-2 {
    height: 282px;
  }
}

.u-section-2 .u-image-2,
.u-section-2 .u-image-2:before,
.u-section-2 .u-image-2 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 :hover > .u-container-layout .u-image-2 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 20px !important;
}

.u-section-2 .hover > .u-container-layout .u-image-2 {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 20px !important;
}

.u-section-2 .u-image-1,
.u-section-2 .u-image-1:before,
.u-section-2 .u-image-1 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-2 .u-image-1.u-image-1.u-image-1:hover {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-2 .u-image-1.u-image-1.u-image-1.hover {
  transform: translateX(0px) translateY(-5px) !important;
} .u-section-3 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-3 .u-sheet-1 {
  min-height: 575px;
}

.u-section-3 .u-text-1 {
  font-weight: 300;
  margin: 20px 0 0;
}

.u-section-3 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: -100px;
}

.u-section-3 .u-layout-cell-1 {
  min-height: 255px;
  --radius: 30px;
}

.u-section-3 .u-container-layout-1 {
  box-shadow: 5px 5px 8px 0 rgba(204,211,219,0.4);
  padding: 30px;
}

.u-section-3 .u-text-2 {
  font-size: 2.25rem;
  font-weight: 800;
  margin: 50px 20px 0 0;
}

.u-section-3 .u-text-3 {
  font-size: 1.5rem;
  font-weight: 500;
  font-family: Figtree;
  margin: 10px 66px 0 0;
}

.u-section-3 .u-layout-cell-2 {
  min-height: 255px;
  --radius: 30px;
}

.u-section-3 .u-container-layout-2 {
  box-shadow: 5px 5px 8px 0 rgba(204,211,219,0.4);
  padding: 30px;
}

.u-section-3 .u-text-4 {
  font-size: 2.25rem;
  font-weight: 800;
  margin: 50px 20px 0 0;
}

.u-section-3 .u-text-5 {
  font-size: 1.5rem;
  font-weight: 500;
  font-family: Figtree;
  margin: 10px 0 0;
}

.u-section-3 .u-layout-cell-3 {
  min-height: 255px;
  --radius: 30px;
}

.u-section-3 .u-container-layout-3 {
  box-shadow: 5px 5px 8px 0 rgba(204,211,219,0.4);
  padding: 30px;
}

.u-section-3 .u-text-6 {
  font-size: 2.25rem;
  font-weight: 800;
  margin: 50px 20px 0 0;
}

.u-section-3 .u-text-7 {
  font-size: 1.5rem;
  font-weight: 500;
  font-family: Figtree;
  margin: 10px 20px 0 0;
}

.u-section-3 .u-layout-cell-4 {
  min-height: 246px;
  --radius: 30px;
}

.u-section-3 .u-container-layout-4 {
  box-shadow: 5px 5px 8px 0 rgba(204,211,219,0.4);
  padding: 20px;
}

.u-section-3 .u-list-1 {
  width: 755px;
  margin: 0 auto;
}

.u-section-3 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 13.3333px);
  grid-template-columns: repeat(3, calc(33.3333% - 13.3333px));
  min-height: 180px;
  --gap: 20px;
}

.u-section-3 .u-list-item-1 {
  background-repeat: no-repeat;
  background-size: cover;
  --radius: 10px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-3 .u-image-1 {
  background-image: url("images/site11.webp");
}

.u-section-3 .u-container-layout-5 {
  padding: 10px;
}

.u-section-3 .u-list-item-2 {
  background-repeat: no-repeat;
  background-size: cover;
  --radius: 10px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-3 .u-image-2 {
  background-image: url("images/laxmi1-1.webp");
}

.u-section-3 .u-container-layout-6 {
  padding: 10px;
}

.u-section-3 .u-list-item-3 {
  background-repeat: no-repeat;
  background-size: cover;
  --radius: 10px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-3 .u-image-3 {
  background-image: url("images/site9.webp");
}

.u-section-3 .u-container-layout-7 {
  padding: 10px;
}

.u-section-3 .u-list-item-4 {
  background-repeat: no-repeat;
  background-size: cover;
  --radius: 10px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-3 .u-image-4 {
  background-image: url("images/qc2.webp");
}

.u-section-3 .u-container-layout-8 {
  padding: 10px;
}

.u-section-3 .u-list-item-5 {
  background-repeat: no-repeat;
  background-size: cover;
  --radius: 10px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-3 .u-image-5 {
  background-image: url("images/site1.webp");
}

.u-section-3 .u-container-layout-9 {
  padding: 10px;
}

.u-section-3 .u-list-item-6 {
  background-repeat: no-repeat;
  background-size: cover;
  --radius: 10px;
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-3 .u-image-6 {
  background-image: url("images/site10.webp");
}

.u-section-3 .u-container-layout-10 {
  padding: 10px;
}

.u-section-3 .u-gallery-nav-1 {
  position: absolute;
  left: 10px;
  width: 40px;
  height: 40px;
}

.u-section-3 .u-gallery-nav-2 {
  position: absolute;
  right: 10px;
  width: 40px;
  height: 40px;
}

.u-section-3 .u-layout-cell-5 {
  min-height: 246px;
  --radius: 30px;
}

.u-section-3 .u-container-layout-11 {
  box-shadow: 5px 5px 8px 0 rgba(204,211,219,0.4);
  padding: 30px;
}

.u-section-3 .u-text-8 {
  font-size: 2.25rem;
  font-weight: 800;
  margin: 50px 0 0;
}

.u-section-3 .u-text-9 {
  font-size: 1.5rem;
  font-weight: 500;
  font-family: Figtree;
  margin: 10px 20px 0 0;
}

@media (max-width: 1199px) {
  .u-section-3 .u-sheet-1 {
    min-height: 589px;
  }

  .u-section-3 .u-layout-wrap-1 {
    position: relative;
    margin-bottom: -3px;
  }

  .u-section-3 .u-layout-cell-1 {
    min-height: 213px;
  }

  .u-section-3 .u-text-2 {
    margin-right: 0;
  }

  .u-section-3 .u-text-3 {
    margin-right: 0;
  }

  .u-section-3 .u-layout-cell-2 {
    min-height: 213px;
  }

  .u-section-3 .u-text-4 {
    margin-right: 0;
  }

  .u-section-3 .u-layout-cell-3 {
    min-height: 213px;
  }

  .u-section-3 .u-text-6 {
    margin-right: 0;
  }

  .u-section-3 .u-text-7 {
    margin-right: 0;
  }

  .u-section-3 .u-layout-cell-4 {
    min-height: 205px;
  }

  .u-section-3 .u-list-1 {
    width: 620px;
  }

  .u-section-3 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 13.3333px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 13.3333px));
    grid-gap: 20px;
  }

  .u-section-3 .u-layout-cell-5 {
    min-height: 205px;
  }

  .u-section-3 .u-text-9 {
    margin-right: 0;
  }
}

@media (max-width: 991px) {
  .u-section-3 .u-sheet-1 {
    min-height: 454px;
  }

  .u-section-3 .u-layout-wrap-1 {
    margin-bottom: 20px;
  }

  .u-section-3 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-3 .u-text-3 {
    font-size: 1.125rem;
  }

  .u-section-3 .u-layout-cell-2 {
    min-height: 100px;
  }

  .u-section-3 .u-text-5 {
    font-size: 1.125rem;
  }

  .u-section-3 .u-layout-cell-3 {
    min-height: 100px;
  }

  .u-section-3 .u-text-7 {
    font-size: 1.125rem;
  }

  .u-section-3 .u-layout-cell-4 {
    min-height: 100px;
  }

  .u-section-3 .u-list-1 {
    width: 473px;
  }

  .u-section-3 .u-repeater-1 {
    grid-auto-columns: calc(50% - 9.999975px);
    grid-template-columns: repeat(2, calc(50% - 9.999975px));
  }

  .u-section-3 .u-layout-cell-5 {
    min-height: 100px;
  }

  .u-section-3 .u-text-9 {
    font-size: 1.125rem;
  }
}

@media (max-width: 767px) {
  .u-section-3 .u-sheet-1 {
    min-height: 637px;
  }

  .u-section-3 .u-text-2 {
    margin-top: 43px;
    margin-left: 20px;
  }

  .u-section-3 .u-text-3 {
    font-size: 1rem;
    margin-top: 17px;
    margin-left: 20px;
  }

  .u-section-3 .u-text-4 {
    margin-top: 43px;
    margin-left: 20px;
  }

  .u-section-3 .u-text-5 {
    font-size: 1rem;
    margin-top: 17px;
    margin-left: 20px;
  }

  .u-section-3 .u-text-6 {
    margin-top: 43px;
    margin-left: 20px;
  }

  .u-section-3 .u-text-7 {
    font-size: 1rem;
    margin-top: 17px;
    margin-left: 20px;
  }

  .u-section-3 .u-container-layout-4 {
    padding: 30px;
  }

  .u-section-3 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-3 .u-text-8 {
    margin-top: 43px;
    margin-left: 20px;
  }

  .u-section-3 .u-text-9 {
    font-size: 1rem;
    margin-top: 17px;
    margin-left: 20px;
  }
}

@media (max-width: 575px) {
  .u-section-3 .u-sheet-1 {
    min-height: 576px;
  }

  .u-section-3 .u-text-2 {
    margin-left: 0;
  }

  .u-section-3 .u-text-3 {
    margin-left: 0;
  }

  .u-section-3 .u-text-4 {
    margin-left: 0;
  }

  .u-section-3 .u-text-5 {
    margin-left: 0;
  }

  .u-section-3 .u-text-6 {
    margin-left: 0;
  }

  .u-section-3 .u-text-7 {
    margin-left: 0;
  }

  .u-section-3 .u-layout-cell-4 {
    min-height: 260px;
  }

  .u-section-3 .u-list-1 {
    margin-right: initial;
    margin-left: initial;
    width: auto;
  }

  .u-section-3 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-3 .u-text-8 {
    margin-left: 0;
  }

  .u-section-3 .u-text-9 {
    margin-left: 0;
  }
} .u-section-4 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-4 .u-sheet-1 {
  min-height: 898px;
}

.u-section-4 .u-text-1 {
  font-weight: 300;
  margin: 20px 997px 0 0;
}

.u-section-4 .u-text-2 {
  font-size: 2.25rem;
  font-weight: 400;
  margin: 5px 747px 0 0;
}

.u-section-4 .u-btn-1 {
  margin: -22px 0 0 auto;
  padding: 0;
}

.u-section-4 .u-text-3 {
  font-weight: 300;
  margin: 20px auto 0 0;
}

.u-section-4 .u-list-1 {
  margin-top: 28px;
  margin-bottom: 22px;
  width: 100%;
}

.u-section-4 .u-repeater-1 {
  grid-auto-columns: calc(50% - 2.5px);
  grid-template-columns: repeat(2, calc(50% - 2.5px));
  min-height: 700px;
  --gap: 5px;
}

.u-section-4 .u-container-layout-1 {
  padding: 0;
}

.u-section-4 .u-image-1 {
  min-height: 345px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  background-image: url("images/safcom-ldc1.webp");
  --radius: 5px;
  background-position: 50% 50%;
  background-size: cover;
}

.u-section-4 .u-container-layout-2 {
  padding: 30px;
}

.u-section-4 .u-container-layout-3 {
  padding: 30px;
}

.u-section-4 .u-text-4 {
  font-size: 2.25rem;
  font-weight: 700;
  margin: 0;
}

.u-section-4 .u-text-5 {
  font-size: 1.5rem;
  margin: 20px auto 0;
}

.u-section-4 .u-btn-2 {
  font-size: 1.25rem;
  background-image: none;
  --radius: 10px;
  margin: 20px auto 0;
}

.u-section-4 .u-container-layout-4 {
  padding: 0;
}

.u-section-4 .u-image-2 {
  min-height: 345px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  background-image: url("images/eni-agrihub1.webp");
  --radius: 5px;
  background-position: 50% 50%;
  background-size: cover;
}

.u-section-4 .u-container-layout-5 {
  padding: 30px;
}

.u-section-4 .u-container-layout-6 {
  padding: 30px;
}

.u-section-4 .u-text-6 {
  font-size: 2.25rem;
  font-weight: 700;
  margin: 0;
}

.u-section-4 .u-text-7 {
  font-size: 1.5rem;
  margin: 20px auto 0;
}

.u-section-4 .u-btn-3 {
  font-size: 1.25rem;
  background-image: none;
  --radius: 10px;
  margin: 20px auto 0;
}

.u-section-4 .u-container-layout-7 {
  padding: 0;
}

.u-section-4 .u-image-3 {
  min-height: 345px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  background-image: url("images/dowgate-nsc1.webp");
  --radius: 5px;
  background-position: 50% 50%;
  background-size: cover;
}

.u-section-4 .u-container-layout-8 {
  padding: 30px;
}

.u-section-4 .u-container-layout-9 {
  padding: 30px;
}

.u-section-4 .u-text-8 {
  font-size: 2.25rem;
  font-weight: 700;
  margin: 0;
}

.u-section-4 .u-text-9 {
  font-size: 1.5rem;
  margin: 20px auto 0;
}

.u-section-4 .u-btn-4 {
  font-size: 1.25rem;
  background-image: none;
  --radius: 10px;
  margin: 20px auto 0;
}

.u-section-4 .u-container-layout-10 {
  padding: 0;
}

.u-section-4 .u-image-4 {
  min-height: 345px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  background-image: url("images/safcom-bbs1.webp");
  --radius: 5px;
  background-position: 50% 50%;
  background-size: cover;
}

.u-section-4 .u-container-layout-11 {
  padding: 30px;
}

.u-section-4 .u-container-layout-12 {
  padding: 30px;
}

.u-section-4 .u-text-10 {
  font-size: 2.25rem;
  font-weight: 700;
  margin: 0;
}

.u-section-4 .u-text-11 {
  font-size: 1.5rem;
  margin: 20px auto 0;
}

.u-section-4 .u-btn-5 {
  font-size: 1.25rem;
  background-image: none;
  --radius: 10px;
  margin: 20px auto 0;
}

@media (max-width: 1199px) {
  .u-section-4 .u-sheet-1 {
    min-height: 896px;
  }

  .u-section-4 .u-text-1 {
    margin-right: 797px;
  }

  .u-section-4 .u-text-2 {
    margin-right: 547px;
  }

  .u-section-4 .u-list-1 {
    margin-bottom: 20px;
    width: auto;
  }

  .u-section-4 .u-repeater-1 {
    grid-gap: 5px;
  }

  .u-section-4 .u-image-1 {
    transition-duration: 0.5s;
    height: auto;
  }

  .u-section-4 .u-btn-2 {
    padding: 16px 43px;
  }

  .u-section-4 .u-image-2 {
    transition-duration: 0.5s;
    height: auto;
  }

  .u-section-4 .u-btn-3 {
    padding: 16px 43px;
  }

  .u-section-4 .u-image-3 {
    transition-duration: 0.5s;
    height: auto;
  }

  .u-section-4 .u-btn-4 {
    padding: 16px 43px;
  }

  .u-section-4 .u-image-4 {
    transition-duration: 0.5s;
    height: auto;
  }

  .u-section-4 .u-btn-5 {
    padding: 16px 43px;
  }
}

@media (max-width: 991px) {
  .u-section-4 .u-text-1 {
    margin-right: 577px;
  }

  .u-section-4 .u-text-2 {
    margin-right: 327px;
  }

  .u-section-4 .u-btn-2 {
    padding: 10px 24px 10px 23px;
  }

  .u-section-4 .u-btn-3 {
    padding: 10px 24px 10px 23px;
  }

  .u-section-4 .u-btn-4 {
    padding: 10px 24px 10px 23px;
  }

  .u-section-4 .u-btn-5 {
    padding: 10px 24px 10px 23px;
  }
}

@media (max-width: 767px) {
  .u-section-4 .u-text-1 {
    margin-right: 337px;
  }

  .u-section-4 .u-text-2 {
    margin-right: 87px;
  }

  .u-section-4 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-4 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-text-5 {
    font-size: 1.3333333333333333rem;
  }

  .u-section-4 .u-container-layout-5 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-container-layout-6 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-text-7 {
    font-size: 1.3333333333333333rem;
  }

  .u-section-4 .u-container-layout-8 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-container-layout-9 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-text-9 {
    font-size: 1.3333333333333333rem;
  }

  .u-section-4 .u-container-layout-11 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-container-layout-12 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-4 .u-text-11 {
    font-size: 1.3333333333333333rem;
  }
}

@media (max-width: 575px) {
  .u-section-4 .u-text-1 {
    margin-right: 137px;
  }

  .u-section-4 .u-text-2 {
    margin-right: 0;
  }

  .u-section-4 .u-repeater-1 {
    grid-auto-columns: 100%;
  }
}

.u-section-4 .u-list-1:not([data-block-selected]):not([data-cell-selected]),
.u-section-4 .u-list-1:not([data-block-selected]):not([data-cell-selected]):before,
.u-section-4 .u-list-1:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-4 .u-list-1:not([data-block-selected]):not([data-cell-selected]).u-section-4 .u-list-1:not([data-block-selected]):not([data-cell-selected]).u-section-4 .u-list-1:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-section-4 .u-list-1:not([data-block-selected]):not([data-cell-selected]).u-section-4 .u-list-1:not([data-block-selected]):not([data-cell-selected]).u-section-4 .u-list-1:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-section-4 .u-text-4:not([data-block-selected]):not([data-cell-selected]),
.u-section-4 .u-text-4:not([data-block-selected]):not([data-cell-selected]):before,
.u-section-4 .u-text-4:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-4 .u-text-4:not([data-block-selected]):not([data-cell-selected]).u-section-4 .u-text-4:not([data-block-selected]):not([data-cell-selected]).u-section-4 .u-text-4:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-section-4 .u-text-4:not([data-block-selected]):not([data-cell-selected]).u-section-4 .u-text-4:not([data-block-selected]):not([data-cell-selected]).u-section-4 .u-text-4:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-f71c-96:not([data-block-selected]):not([data-cell-selected]),
.u-block-f71c-96:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-f71c-96:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-f71c-96:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-96:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-96:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-f71c-96:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-96:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-96:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-f71c-102:not([data-block-selected]):not([data-cell-selected]),
.u-block-f71c-102:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-f71c-102:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-f71c-102:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-102:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-102:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-f71c-102:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-102:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-102:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-f71c-108:not([data-block-selected]):not([data-cell-selected]),
.u-block-f71c-108:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-f71c-108:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-f71c-108:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-108:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-108:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-f71c-108:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-108:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-108:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-f71c-114:not([data-block-selected]):not([data-cell-selected]),
.u-block-f71c-114:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-f71c-114:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-f71c-114:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-114:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-114:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-f71c-114:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-114:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-114:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-f71c-133:not([data-block-selected]):not([data-cell-selected]),
.u-block-f71c-133:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-f71c-133:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-f71c-133:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-133:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-133:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-f71c-133:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-133:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-133:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-f71c-138:not([data-block-selected]):not([data-cell-selected]),
.u-block-f71c-138:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-f71c-138:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-f71c-138:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-138:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-138:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-f71c-138:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-138:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-138:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-f71c-143:not([data-block-selected]):not([data-cell-selected]),
.u-block-f71c-143:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-f71c-143:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-f71c-143:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-143:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-143:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-f71c-143:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-143:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-143:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-f71c-148:not([data-block-selected]):not([data-cell-selected]),
.u-block-f71c-148:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-f71c-148:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-block-f71c-148:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-148:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-148:not([data-block-selected]):not([data-cell-selected]):hover {
  border-radius: 30px !important;
}

.u-block-f71c-148:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-148:not([data-block-selected]):not([data-cell-selected]).u-block-f71c-148:not([data-block-selected]):not([data-cell-selected]).hover {
  border-radius: 30px !important;
}

.u-block-f71c-84:not([data-block-selected]):not([data-cell-selected]),
.u-block-f71c-84:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-f71c-84:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-container-layout:hover > .u-block-f71c-84:not([data-block-selected]):not([data-cell-selected]) {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-container-layout.hover > .u-block-f71c-84:not([data-block-selected]):not([data-cell-selected]) {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-block-f71c-90:not([data-block-selected]):not([data-cell-selected]),
.u-block-f71c-90:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-f71c-90:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-container-layout:hover > .u-block-f71c-90:not([data-block-selected]):not([data-cell-selected]) {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
}

.u-container-layout.hover > .u-block-f71c-90:not([data-block-selected]):not([data-cell-selected]) {
  transform: translateX(0px) translateY(-5px) !important;
  border-radius: 10px !important;
} .u-section-5 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-5 .u-sheet-1 {
  min-height: 781px;
}

.u-section-5 .u-text-1 {
  font-weight: 300;
  margin: 20px 933px 0 0;
}

.u-section-5 .u-text-2 {
  font-size: 2.25rem;
  font-weight: 400;
  margin: 5px 696px 0 0;
}

.u-section-5 .u-tabs-1 {
  min-height: 577px;
  height: auto;
  margin-top: 20px;
  margin-bottom: -72px;
}

.u-section-5 .u-tab-link-1 {
  font-size: 1.125rem;
  background-image: none;
  font-weight: 400;
  border-style: solid;
  --radius: 10px;
  padding: 10px 25px;
}

.u-section-5 .u-tab-link-2 {
  font-size: 1.125rem;
  background-image: none;
  font-weight: 400;
  border-style: solid;
  --radius: 10px;
  padding: 10px 25px;
}

.u-section-5 .u-container-layout-1 {
  padding: 20px 0;
}

.u-section-5 .u-list-1 {
  margin-top: 0;
  margin-bottom: 0;
}

.u-section-5 .u-repeater-1 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 511px;
  --gap: 10px;
}

.u-section-5 .u-container-layout-2 {
  padding: 0;
}

.u-section-5 .u-group-1 {
  --radius: 20px;
  min-height: 250px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  width: 100%;
}

.u-section-5 .u-container-layout-3 {
  padding: 30px;
}

.u-section-5 .u-image-1 {
  width: 190px;
  height: 190px;
  margin: 0 auto;
}

.u-section-5 .u-container-layout-4 {
  padding: 22px 30px;
}

.u-section-5 .u-image-2 {
  width: 60px;
  height: 60px;
  --radius: 200px;
  margin: 8px auto 0 0;
}

.u-section-5 .u-image-3 {
  width: 96px;
  height: 16px;
  font-size: 1rem;
  margin: -60px auto 0 80px;
}

.u-section-5 .u-text-3 {
  margin: 10px 0 0 80px;
}

.u-section-5 .u-text-4 {
  font-size: 1rem;
  margin: 14px 3px -6px 0;
}

.u-section-5 .u-container-layout-5 {
  padding: 0;
}

.u-section-5 .u-group-2 {
  --radius: 20px;
  min-height: 250px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  width: 100%;
}

.u-section-5 .u-container-layout-6 {
  padding: 30px;
}

.u-section-5 .u-image-4 {
  width: 190px;
  height: 190px;
  margin: 0 auto;
}

.u-section-5 .u-container-layout-7 {
  padding: 22px 30px;
}

.u-section-5 .u-image-5 {
  width: 60px;
  height: 60px;
  --radius: 200px;
  margin: 8px auto 0 0;
}

.u-section-5 .u-image-6 {
  width: 96px;
  height: 16px;
  font-size: 1rem;
  margin: -60px auto 0 80px;
}

.u-section-5 .u-text-5 {
  margin: 10px 0 0 80px;
}

.u-section-5 .u-text-6 {
  font-size: 1rem;
  margin: 14px 3px -6px 0;
}

.u-section-5 .u-container-layout-8 {
  padding: 0;
}

.u-section-5 .u-group-3 {
  --radius: 20px;
  min-height: 250px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  width: 100%;
}

.u-section-5 .u-container-layout-9 {
  padding: 30px;
}

.u-section-5 .u-image-7 {
  width: 190px;
  height: 190px;
  margin: 0 auto;
}

.u-section-5 .u-container-layout-10 {
  padding: 22px 30px;
}

.u-section-5 .u-image-8 {
  width: 60px;
  height: 60px;
  --radius: 200px;
  margin: 8px auto 0 0;
}

.u-section-5 .u-image-9 {
  width: 96px;
  height: 16px;
  font-size: 1rem;
  margin: -60px auto 0 80px;
}

.u-section-5 .u-text-7 {
  margin: 10px 0 0 80px;
}

.u-section-5 .u-text-8 {
  font-size: 1rem;
  margin: 14px 3px -6px 0;
}

.u-section-5 .u-container-layout-11 {
  padding: 0;
}

.u-section-5 .u-group-4 {
  --radius: 20px;
  min-height: 250px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  width: 100%;
}

.u-section-5 .u-container-layout-12 {
  padding: 30px;
}

.u-section-5 .u-image-10 {
  width: 190px;
  height: 190px;
  margin: 0 auto;
}

.u-section-5 .u-container-layout-13 {
  padding: 22px 30px;
}

.u-section-5 .u-image-11 {
  width: 60px;
  height: 60px;
  --radius: 200px;
  margin: 8px auto 0 0;
}

.u-section-5 .u-image-12 {
  width: 96px;
  height: 16px;
  font-size: 1rem;
  margin: -60px auto 0 80px;
}

.u-section-5 .u-text-9 {
  margin: 10px 0 0 80px;
}

.u-section-5 .u-text-10 {
  font-size: 1rem;
  margin: 14px 3px -6px 0;
}

.u-section-5 .u-container-layout-14 {
  padding: 0;
}

.u-section-5 .u-group-5 {
  --radius: 20px;
  min-height: 250px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  width: 100%;
}

.u-section-5 .u-container-layout-15 {
  padding: 30px;
}

.u-section-5 .u-image-13 {
  width: 190px;
  height: 190px;
  margin: 0 auto;
}

.u-section-5 .u-container-layout-16 {
  padding: 22px 30px;
}

.u-section-5 .u-image-14 {
  width: 60px;
  height: 60px;
  --radius: 200px;
  margin: 8px auto 0 0;
}

.u-section-5 .u-image-15 {
  width: 96px;
  height: 16px;
  font-size: 1rem;
  margin: -60px auto 0 80px;
}

.u-section-5 .u-text-11 {
  margin: 10px 0 0 80px;
}

.u-section-5 .u-text-12 {
  font-size: 1rem;
  margin: 14px 3px -6px 0;
}

.u-section-5 .u-container-layout-17 {
  padding: 0;
}

.u-section-5 .u-group-6 {
  --radius: 20px;
  min-height: 250px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  width: 100%;
}

.u-section-5 .u-container-layout-18 {
  padding: 30px;
}

.u-section-5 .u-image-16 {
  width: 190px;
  height: 190px;
  margin: 0 auto;
}

.u-section-5 .u-container-layout-19 {
  padding: 22px 30px;
}

.u-section-5 .u-image-17 {
  width: 60px;
  height: 60px;
  --radius: 200px;
  margin: 8px auto 0 0;
}

.u-section-5 .u-image-18 {
  width: 96px;
  height: 16px;
  font-size: 1rem;
  margin: -60px auto 0 80px;
}

.u-section-5 .u-text-13 {
  margin: 10px 0 0 80px;
}

.u-section-5 .u-text-14 {
  font-size: 1rem;
  margin: 14px 3px -6px 0;
}

.u-section-5 .u-container-layout-20 {
  padding: 20px 0;
}

.u-section-5 .u-list-2 {
  margin: 0 auto 0 20px;
}

.u-section-5 .u-repeater-2 {
  grid-auto-columns: calc(33.3333% - 6.66667px);
  grid-template-columns: repeat(3, calc(33.3333% - 6.66667px));
  min-height: 511px;
  --gap: 10px;
}

.u-section-5 .u-container-layout-21 {
  padding: 0;
}

.u-section-5 .u-group-7 {
  --radius: 20px;
  min-height: 250px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  width: 100%;
}

.u-section-5 .u-container-layout-22 {
  padding: 30px;
}

.u-section-5 .u-image-19 {
  width: 190px;
  height: 190px;
  margin: 0 auto;
}

.u-section-5 .u-container-layout-23 {
  padding: 22px 30px;
}

.u-section-5 .u-image-20 {
  width: 60px;
  height: 60px;
  --radius: 200px;
  font-size: 1rem;
  margin: 8px auto 0 0;
}

.u-section-5 .u-image-21 {
  width: 96px;
  height: 16px;
  font-size: 1rem;
  margin: -60px auto 0 80px;
}

.u-section-5 .u-text-15 {
  margin: 10px 0 0 80px;
}

.u-section-5 .u-text-16 {
  font-size: 1rem;
  margin: 15px 3px -6px 0;
}

.u-section-5 .u-container-layout-24 {
  padding: 0;
}

.u-section-5 .u-group-8 {
  --radius: 20px;
  min-height: 250px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  width: 100%;
}

.u-section-5 .u-container-layout-25 {
  padding: 30px;
}

.u-section-5 .u-image-22 {
  width: 190px;
  height: 190px;
  margin: 0 auto;
}

.u-section-5 .u-container-layout-26 {
  padding: 22px 30px;
}

.u-section-5 .u-image-23 {
  width: 60px;
  height: 60px;
  --radius: 200px;
  font-size: 1rem;
  margin: 8px auto 0 0;
}

.u-section-5 .u-image-24 {
  width: 96px;
  height: 16px;
  font-size: 1rem;
  margin: -60px auto 0 80px;
}

.u-section-5 .u-text-17 {
  margin: 10px 0 0 80px;
}

.u-section-5 .u-text-18 {
  font-size: 1rem;
  margin: 15px 3px -6px 0;
}

.u-section-5 .u-container-layout-27 {
  padding: 0;
}

.u-section-5 .u-group-9 {
  --radius: 20px;
  min-height: 250px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  width: 100%;
}

.u-section-5 .u-container-layout-28 {
  padding: 30px;
}

.u-section-5 .u-image-25 {
  width: 190px;
  height: 190px;
  margin: 0 auto;
}

.u-section-5 .u-container-layout-29 {
  padding: 22px 30px;
}

.u-section-5 .u-image-26 {
  width: 60px;
  height: 60px;
  --radius: 200px;
  font-size: 1rem;
  margin: 8px auto 0 0;
}

.u-section-5 .u-image-27 {
  width: 96px;
  height: 16px;
  font-size: 1rem;
  margin: -60px auto 0 80px;
}

.u-section-5 .u-text-19 {
  margin: 10px 0 0 80px;
}

.u-section-5 .u-text-20 {
  font-size: 1rem;
  margin: 15px 3px -6px 0;
}

.u-section-5 .u-container-layout-30 {
  padding: 0;
}

.u-section-5 .u-group-10 {
  --radius: 20px;
  min-height: 250px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  width: 100%;
}

.u-section-5 .u-container-layout-31 {
  padding: 30px;
}

.u-section-5 .u-image-28 {
  width: 190px;
  height: 190px;
  margin: 0 auto;
}

.u-section-5 .u-container-layout-32 {
  padding: 22px 30px;
}

.u-section-5 .u-image-29 {
  width: 60px;
  height: 60px;
  --radius: 200px;
  font-size: 1rem;
  margin: 8px auto 0 0;
}

.u-section-5 .u-image-30 {
  width: 96px;
  height: 16px;
  font-size: 1rem;
  margin: -60px auto 0 80px;
}

.u-section-5 .u-text-21 {
  margin: 10px 0 0 80px;
}

.u-section-5 .u-text-22 {
  font-size: 1rem;
  margin: 15px 3px -6px 0;
}

.u-section-5 .u-container-layout-33 {
  padding: 0;
}

.u-section-5 .u-group-11 {
  --radius: 20px;
  min-height: 250px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  width: 100%;
}

.u-section-5 .u-container-layout-34 {
  padding: 30px;
}

.u-section-5 .u-image-31 {
  width: 190px;
  height: 190px;
  margin: 0 auto;
}

.u-section-5 .u-container-layout-35 {
  padding: 22px 30px;
}

.u-section-5 .u-image-32 {
  width: 60px;
  height: 60px;
  --radius: 200px;
  font-size: 1rem;
  margin: 8px auto 0 0;
}

.u-section-5 .u-image-33 {
  width: 96px;
  height: 16px;
  font-size: 1rem;
  margin: -60px auto 0 80px;
}

.u-section-5 .u-text-23 {
  margin: 10px 0 0 80px;
}

.u-section-5 .u-text-24 {
  font-size: 1rem;
  margin: 15px 3px -6px 0;
}

.u-section-5 .u-container-layout-36 {
  padding: 0;
}

.u-section-5 .u-group-12 {
  --radius: 20px;
  min-height: 250px;
  margin-top: 0;
  margin-bottom: 0;
  height: auto;
  width: 100%;
}

.u-section-5 .u-container-layout-37 {
  padding: 30px;
}

.u-section-5 .u-image-34 {
  width: 190px;
  height: 190px;
  margin: 0 auto;
}

.u-section-5 .u-container-layout-38 {
  padding: 22px 30px;
}

.u-section-5 .u-image-35 {
  width: 60px;
  height: 60px;
  --radius: 200px;
  font-size: 1rem;
  margin: 8px auto 0 0;
}

.u-section-5 .u-image-36 {
  width: 96px;
  height: 16px;
  font-size: 1rem;
  margin: -60px auto 0 80px;
}

.u-section-5 .u-text-25 {
  margin: 10px 0 0 80px;
}

.u-section-5 .u-text-26 {
  font-size: 1rem;
  margin: 15px 3px -6px 0;
}

@media (max-width: 1199px) {
  .u-section-5 .u-sheet-1 {
    min-height: 780px;
  }

  .u-section-5 .u-text-1 {
    margin-top: -99px;
    margin-right: 733px;
  }

  .u-section-5 .u-text-2 {
    margin-right: 496px;
  }

  .u-section-5 .u-tabs-1 {
    margin-bottom: -59px;
  }

  .u-section-5 .u-repeater-1 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 426px;
    grid-gap: 10px;
  }

  .u-section-5 .u-group-1 {
    height: auto;
  }

  .u-section-5 .u-image-1 {
    width: 182px;
    height: 182px;
  }

  .u-section-5 .u-image-2 {
    width: 40px;
    height: 40px;
  }

  .u-section-5 .u-image-3 {
    width: 63px;
    height: 10px;
    margin-top: -40px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-3 {
    width: auto;
    margin-right: 30px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-4 {
    margin-top: 10px;
    margin-bottom: 0;
  }

  .u-section-5 .u-group-2 {
    height: auto;
  }

  .u-section-5 .u-image-4 {
    width: 182px;
    height: 182px;
  }

  .u-section-5 .u-image-5 {
    width: 40px;
    height: 40px;
  }

  .u-section-5 .u-image-6 {
    width: 63px;
    height: 10px;
    margin-top: -40px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-5 {
    width: auto;
    margin-right: 30px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-6 {
    margin-top: 10px;
    margin-bottom: 0;
  }

  .u-section-5 .u-group-3 {
    height: auto;
  }

  .u-section-5 .u-image-7 {
    width: 182px;
    height: 182px;
  }

  .u-section-5 .u-image-8 {
    width: 40px;
    height: 40px;
  }

  .u-section-5 .u-image-9 {
    width: 63px;
    height: 10px;
    margin-top: -40px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-7 {
    width: auto;
    margin-right: 30px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-8 {
    margin-top: 10px;
    margin-bottom: 0;
  }

  .u-section-5 .u-group-4 {
    height: auto;
  }

  .u-section-5 .u-image-10 {
    width: 182px;
    height: 182px;
  }

  .u-section-5 .u-image-11 {
    width: 40px;
    height: 40px;
  }

  .u-section-5 .u-image-12 {
    width: 63px;
    height: 10px;
    margin-top: -40px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-9 {
    width: auto;
    margin-right: 30px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-10 {
    margin-top: 10px;
    margin-bottom: 0;
  }

  .u-section-5 .u-group-5 {
    height: auto;
  }

  .u-section-5 .u-image-13 {
    width: 182px;
    height: 182px;
  }

  .u-section-5 .u-image-14 {
    width: 40px;
    height: 40px;
  }

  .u-section-5 .u-image-15 {
    width: 63px;
    height: 10px;
    margin-top: -40px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-11 {
    width: auto;
    margin-right: 30px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-12 {
    margin-top: 10px;
    margin-bottom: 0;
  }

  .u-section-5 .u-group-6 {
    height: auto;
  }

  .u-section-5 .u-image-16 {
    width: 182px;
    height: 182px;
  }

  .u-section-5 .u-image-17 {
    width: 40px;
    height: 40px;
  }

  .u-section-5 .u-image-18 {
    width: 63px;
    height: 10px;
    margin-top: -40px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-13 {
    width: auto;
    margin-right: 30px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-14 {
    margin-top: 10px;
    margin-bottom: 0;
  }

  .u-section-5 .u-list-2 {
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-5 .u-repeater-2 {
    grid-auto-columns: calc(33.333333333333336% - 6.66667px);
    grid-template-columns: repeat(3, calc(33.333333333333336% - 6.66667px));
    min-height: 433px;
    grid-gap: 10px;
  }

  .u-section-5 .u-group-7 {
    height: auto;
  }

  .u-section-5 .u-container-layout-23 {
    padding-bottom: 30px;
  }

  .u-section-5 .u-image-20 {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
    margin-top: 0;
  }

  .u-section-5 .u-image-21 {
    width: 50px;
    height: 9px;
    margin-top: -32px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-15 {
    width: auto;
    margin-top: 5px;
    margin-right: 17px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-16 {
    margin-top: 10px;
    margin-bottom: -2px;
  }

  .u-section-5 .u-group-8 {
    height: auto;
  }

  .u-section-5 .u-container-layout-26 {
    padding-bottom: 30px;
  }

  .u-section-5 .u-image-23 {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
    margin-top: 0;
  }

  .u-section-5 .u-image-24 {
    width: 50px;
    height: 9px;
    margin-top: -32px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-17 {
    width: auto;
    margin-top: 5px;
    margin-right: 17px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-18 {
    margin-top: 10px;
    margin-bottom: -2px;
  }

  .u-section-5 .u-group-9 {
    height: auto;
  }

  .u-section-5 .u-container-layout-29 {
    padding-bottom: 30px;
  }

  .u-section-5 .u-image-26 {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
    margin-top: 0;
  }

  .u-section-5 .u-image-27 {
    width: 50px;
    height: 9px;
    margin-top: -32px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-19 {
    width: auto;
    margin-top: 5px;
    margin-right: 17px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-20 {
    margin-top: 10px;
    margin-bottom: -2px;
  }

  .u-section-5 .u-group-10 {
    height: auto;
  }

  .u-section-5 .u-container-layout-32 {
    padding-bottom: 30px;
  }

  .u-section-5 .u-image-29 {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
    margin-top: 0;
  }

  .u-section-5 .u-image-30 {
    width: 50px;
    height: 9px;
    margin-top: -32px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-21 {
    width: auto;
    margin-top: 5px;
    margin-right: 17px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-22 {
    margin-top: 10px;
    margin-bottom: -2px;
  }

  .u-section-5 .u-group-11 {
    height: auto;
  }

  .u-section-5 .u-container-layout-35 {
    padding-bottom: 30px;
  }

  .u-section-5 .u-image-32 {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
    margin-top: 0;
  }

  .u-section-5 .u-image-33 {
    width: 50px;
    height: 9px;
    margin-top: -32px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-23 {
    width: auto;
    margin-top: 5px;
    margin-right: 17px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-24 {
    margin-top: 10px;
    margin-bottom: -2px;
  }

  .u-section-5 .u-group-12 {
    height: auto;
  }

  .u-section-5 .u-container-layout-38 {
    padding-bottom: 30px;
  }

  .u-section-5 .u-image-35 {
    width: 40px;
    height: 40px;
    font-size: 1.25rem;
    margin-top: 0;
  }

  .u-section-5 .u-image-36 {
    width: 50px;
    height: 9px;
    margin-top: -32px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-25 {
    width: auto;
    margin-top: 5px;
    margin-right: 17px;
    margin-left: 50px;
  }

  .u-section-5 .u-text-26 {
    margin-top: 10px;
    margin-bottom: -2px;
  }
}

@media (max-width: 991px) {
  .u-section-5 .u-sheet-1 {
    min-height: 1009px;
  }

  .u-section-5 .u-text-1 {
    margin-top: -66px;
    margin-right: 513px;
  }

  .u-section-5 .u-text-2 {
    margin-right: 276px;
  }

  .u-section-5 .u-tabs-1 {
    margin-bottom: -56px;
  }

  .u-section-5 .u-repeater-1 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 748px;
  }

  .u-section-5 .u-image-1 {
    width: 189px;
    height: 189px;
    margin-top: 1px;
  }

  .u-section-5 .u-text-3 {
    margin-top: 5px;
  }

  .u-section-5 .u-image-4 {
    width: 189px;
    height: 189px;
    margin-top: 1px;
  }

  .u-section-5 .u-text-5 {
    margin-top: 5px;
  }

  .u-section-5 .u-image-7 {
    width: 189px;
    height: 189px;
    margin-top: 1px;
  }

  .u-section-5 .u-image-9 {
    width: 96px;
    height: 16px;
    margin-top: -60px;
    margin-left: 80px;
  }

  .u-section-5 .u-text-7 {
    margin-top: 5px;
  }

  .u-section-5 .u-image-10 {
    width: 189px;
    height: 189px;
    margin-top: 1px;
  }

  .u-section-5 .u-image-12 {
    width: 96px;
    height: 16px;
    margin-top: -60px;
    margin-left: 80px;
  }

  .u-section-5 .u-text-9 {
    margin-top: 5px;
  }

  .u-section-5 .u-image-13 {
    width: 189px;
    height: 189px;
    margin-top: 1px;
  }

  .u-section-5 .u-image-15 {
    width: 96px;
    height: 16px;
    margin-top: -60px;
    margin-left: 80px;
  }

  .u-section-5 .u-text-11 {
    margin-top: 5px;
  }

  .u-section-5 .u-image-16 {
    width: 189px;
    height: 189px;
    margin-top: 1px;
  }

  .u-section-5 .u-image-18 {
    width: 96px;
    height: 16px;
    margin-top: -60px;
    margin-left: 80px;
  }

  .u-section-5 .u-text-13 {
    margin-top: 5px;
  }

  .u-section-5 .u-repeater-2 {
    grid-auto-columns: calc(50% - 5.0000025px);
    grid-template-columns: repeat(2, calc(50% - 5.0000025px));
    min-height: 760px;
  }

  .u-section-5 .u-image-21 {
    width: 57px;
    height: 10px;
    margin-top: -40px;
  }

  .u-section-5 .u-image-24 {
    width: 57px;
    height: 10px;
    margin-top: -40px;
  }

  .u-section-5 .u-image-27 {
    width: 57px;
    height: 10px;
    margin-top: -40px;
  }

  .u-section-5 .u-image-30 {
    width: 57px;
    height: 10px;
    margin-top: -40px;
  }

  .u-section-5 .u-image-33 {
    width: 57px;
    height: 10px;
    margin-top: -40px;
  }

  .u-section-5 .u-image-36 {
    width: 57px;
    height: 10px;
    margin-top: -40px;
  }
}

@media (max-width: 767px) {
  .u-section-5 .u-sheet-1 {
    min-height: 1181px;
  }

  .u-section-5 .u-text-1 {
    margin-top: 20px;
    margin-right: 273px;
  }

  .u-section-5 .u-text-2 {
    margin-right: 36px;
  }

  .u-section-5 .u-tabs-1 {
    margin-bottom: 30px;
  }

  .u-section-5 .u-tab-link-1 {
    font-size: 1rem;
  }

  .u-section-5 .u-tab-link-2 {
    font-size: 1rem;
  }

  .u-section-5 .u-repeater-1 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-5 .u-image-1 {
    margin-top: 0;
  }

  .u-section-5 .u-container-layout-4 {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .u-section-5 .u-image-3 {
    width: 57px;
  }

  .u-section-5 .u-image-4 {
    margin-top: 0;
  }

  .u-section-5 .u-container-layout-7 {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .u-section-5 .u-image-6 {
    width: 57px;
  }

  .u-section-5 .u-image-7 {
    margin-top: 0;
  }

  .u-section-5 .u-container-layout-10 {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .u-section-5 .u-image-9 {
    width: 57px;
    height: 10px;
    margin-top: -40px;
    margin-left: 50px;
  }

  .u-section-5 .u-image-10 {
    margin-top: 0;
  }

  .u-section-5 .u-container-layout-13 {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .u-section-5 .u-image-12 {
    width: 57px;
    height: 10px;
    margin-top: -40px;
    margin-left: 50px;
  }

  .u-section-5 .u-image-13 {
    margin-top: 0;
  }

  .u-section-5 .u-container-layout-16 {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .u-section-5 .u-image-15 {
    width: 57px;
    height: 10px;
    margin-top: -40px;
    margin-left: 50px;
  }

  .u-section-5 .u-image-16 {
    margin-top: 0;
  }

  .u-section-5 .u-container-layout-19 {
    padding-top: 30px;
    padding-bottom: 30px;
  }

  .u-section-5 .u-image-18 {
    width: 57px;
    height: 10px;
    margin-top: -40px;
    margin-left: 50px;
  }

  .u-section-5 .u-repeater-2 {
    grid-auto-columns: calc(100% - 0px);
    grid-template-columns: 100%;
  }

  .u-section-5 .u-container-layout-23 {
    padding-top: 30px;
  }

  .u-section-5 .u-image-21 {
    width: 63px;
    height: 11px;
  }

  .u-section-5 .u-container-layout-26 {
    padding-top: 30px;
  }

  .u-section-5 .u-image-24 {
    width: 63px;
    height: 11px;
  }

  .u-section-5 .u-container-layout-29 {
    padding-top: 30px;
  }

  .u-section-5 .u-image-27 {
    width: 63px;
    height: 11px;
  }

  .u-section-5 .u-container-layout-32 {
    padding-top: 30px;
  }

  .u-section-5 .u-image-30 {
    width: 63px;
    height: 11px;
  }

  .u-section-5 .u-container-layout-35 {
    padding-top: 30px;
  }

  .u-section-5 .u-image-33 {
    width: 63px;
    height: 11px;
  }

  .u-section-5 .u-container-layout-38 {
    padding-top: 30px;
  }

  .u-section-5 .u-image-36 {
    width: 63px;
    height: 11px;
  }
}

@media (max-width: 575px) {
  .u-section-5 .u-text-1 {
    margin-right: 73px;
  }

  .u-section-5 .u-text-2 {
    margin-right: 0;
  }

  .u-section-5 .u-repeater-1 {
    grid-auto-columns: 100%;
  }

  .u-section-5 .u-image-1 {
    width: 190px;
    height: 190px;
  }

  .u-section-5 .u-image-3 {
    width: 63px;
    height: 11px;
  }

  .u-section-5 .u-image-4 {
    width: 190px;
    height: 190px;
  }

  .u-section-5 .u-image-6 {
    width: 63px;
    height: 11px;
  }

  .u-section-5 .u-image-7 {
    width: 190px;
    height: 190px;
  }

  .u-section-5 .u-image-9 {
    width: 63px;
    height: 11px;
  }

  .u-section-5 .u-image-10 {
    width: 190px;
    height: 190px;
  }

  .u-section-5 .u-image-12 {
    width: 63px;
    height: 11px;
  }

  .u-section-5 .u-image-13 {
    width: 190px;
    height: 190px;
  }

  .u-section-5 .u-image-15 {
    width: 63px;
    height: 11px;
  }

  .u-section-5 .u-image-16 {
    width: 190px;
    height: 190px;
  }

  .u-section-5 .u-image-18 {
    width: 63px;
    height: 11px;
  }

  .u-section-5 .u-repeater-2 {
    grid-auto-columns: 100%;
  }

  .u-section-5 .u-image-21 {
    width: 57px;
    height: 10px;
  }

  .u-section-5 .u-image-24 {
    width: 57px;
    height: 10px;
  }

  .u-section-5 .u-image-27 {
    width: 57px;
    height: 10px;
  }

  .u-section-5 .u-image-30 {
    width: 57px;
    height: 10px;
  }

  .u-section-5 .u-image-33 {
    width: 57px;
    height: 10px;
  }

  .u-section-5 .u-image-36 {
    width: 57px;
    height: 10px;
  }
}