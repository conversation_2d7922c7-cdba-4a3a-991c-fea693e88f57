# Site Engineer Mobile Application - Implementation Guide

## Overview

This document provides a comprehensive guide for creating a separate React application specifically designed for Site Engineers. This mobile-first application will complement the existing safety-focused workforce management system by providing field engineers with essential tools for daily operations.

## Why This Application Exists

### Business Justification
- **Field-First Design**: Site engineers work primarily on-site and need mobile-optimized tools
- **Role-Specific Workflow**: Engineers have different priorities than safety officers (productivity vs. compliance)
- **Simplified Interface**: Focused on essential daily tasks without safety management complexity
- **Offline Capability**: Field work often has poor connectivity, requiring offline-first design

### Key Differentiators from Main Application
- **Mobile-First**: Optimized for phones and tablets used in the field
- **Task-Focused**: Emphasis on work planning and execution rather than safety compliance
- **Simplified UI**: Streamlined interface for quick task completion
- **Engineer Perspective**: Views data from productivity and progress standpoint

## Application Architecture

### Technology Stack
```
Frontend: React 18 + TypeScript
Styling: Tailwind CSS (consistent with main app)
State Management: React Context + useReducer
Routing: React Router DOM
HTTP Client: Axios or Fetch API
PWA: Service Workers for offline capability
UI Components: Custom components following design system
Icons: Lucide React (consistent with main app)
```

### Project Structure
```
site-engineer-app/
├── public/
│   ├── manifest.json          # PWA manifest
│   └── sw.js                  # Service worker
├── src/
│   ├── components/
│   │   ├── common/            # Shared UI components
│   │   ├── workers/           # Worker management components
│   │   ├── tasks/             # Task planning components
│   │   ├── permits/           # Permit request components
│   │   ├── reports/           # Progress reporting components
│   │   └── layout/            # Layout components
│   ├── pages/
│   │   ├── Dashboard.tsx
│   │   ├── MyTeam.tsx
│   │   ├── TaskPlanning.tsx
│   │   ├── PermitRequests.tsx
│   │   └── Reports.tsx
│   ├── hooks/
│   │   ├── useAuth.ts
│   │   ├── useWorkers.ts
│   │   ├── useTasks.ts
│   │   └── useOfflineSync.ts
│   ├── services/
│   │   ├── api.ts
│   │   ├── auth.ts
│   │   └── offline.ts
│   ├── types/
│   │   ├── index.ts
│   │   ├── workers.ts
│   │   ├── tasks.ts
│   │   └── permits.ts
│   ├── utils/
│   │   ├── storage.ts
│   │   └── sync.ts
│   └── styles/
│       └── globals.css
```

## Design System Alignment

### Color Palette (Consistent with Main App)
```css
/* Primary Colors */
--primary-50: #f0f9ff;
--primary-500: #3b82f6;
--primary-600: #2563eb;
--primary-700: #1d4ed8;

/* Success Colors */
--success-50: #f0fdf4;
--success-500: #22c55e;
--success-600: #16a34a;

/* Warning Colors */
--warning-50: #fffbeb;
--warning-500: #f59e0b;
--warning-600: #d97706;

/* Error Colors */
--error-50: #fef2f2;
--error-500: #ef4444;
--error-600: #dc2626;

/* Neutral Colors */
--gray-50: #f9fafb;
--gray-100: #f3f4f6;
--gray-500: #6b7280;
--gray-900: #111827;
```

### Typography Scale
```css
/* Mobile-Optimized Typography */
.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
```

### Component Design Principles
1. **Touch-Friendly**: Minimum 44px touch targets
2. **High Contrast**: Ensure readability in outdoor conditions
3. **Consistent Spacing**: Use 4px grid system (4, 8, 12, 16, 24, 32px)
4. **Loading States**: Clear feedback for all async operations
5. **Error Handling**: User-friendly error messages with retry options

### Mobile-First Responsive Breakpoints
```css
/* Mobile First Approach */
sm: '640px',   /* Small tablets */
md: '768px',   /* Tablets */
lg: '1024px',  /* Small laptops */
xl: '1280px'   /* Desktops */
```

## Core Features Implementation

### 1. Worker Management

#### Purpose
- View team members assigned to the engineer
- Submit overtime requests for workers
- Track basic worker information and availability

#### Key Components
```typescript
// MyTeam.tsx - Main worker management page
interface WorkerSummary {
  id: string;
  name: string;
  trade: string;
  photo?: string;
  isOnSite: boolean;
  hoursWorked: number;
  overtimeHours: number;
  lastSeen: Date;
}

// OvertimeRequest.tsx - Overtime submission form
interface OvertimeRequest {
  workerId: string;
  date: Date;
  hours: number;
  reason: string;
  approvalStatus: 'pending' | 'approved' | 'rejected';
}
```

#### Mobile Considerations
- Swipe gestures for quick actions
- Large, touch-friendly buttons
- Minimal form fields with smart defaults
- Photo thumbnails for easy worker identification

### 2. Task Planning & Management

#### Purpose
- Create and assign tasks for the next day
- Submit task plans for safety approval
- Update task progress throughout the day

#### Key Components
```typescript
// TaskPlanning.tsx - Next day task creation
interface TaskPlan {
  id: string;
  title: string;
  description: string;
  assignedWorkers: string[];
  location: string;
  estimatedHours: number;
  priority: 'low' | 'medium' | 'high';
  requiresPermit: boolean;
  status: 'draft' | 'submitted' | 'approved' | 'rejected';
  submittedAt?: Date;
  approvedBy?: string;
}

// TaskProgress.tsx - Update task status
interface TaskProgress {
  taskId: string;
  status: 'not-started' | 'in-progress' | 'completed' | 'blocked';
  progressPercentage: number;
  notes?: string;
  updatedAt: Date;
}
```

#### Mobile Features
- Quick task templates for common work
- Drag-and-drop worker assignment
- Voice-to-text for descriptions
- Photo capture for progress updates

### 3. Permit Requests

#### Purpose
- Submit permit requests for planned work activities
- Track approval status of submitted permits
- View active permits for work areas

#### Key Components
```typescript
// PermitRequest.tsx - Submit new permit requests
interface PermitRequest {
  id: string;
  workActivity: string; // From predefined templates
  location: string;
  plannedDate: Date;
  estimatedDuration: number;
  assignedWorkers: string[];
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  status: 'draft' | 'submitted' | 'under-review' | 'approved' | 'rejected';
  submittedAt?: Date;
  reviewedBy?: string;
  reviewNotes?: string;
}

// PermitStatus.tsx - Track permit approvals
interface PermitStatus {
  permitId: string;
  currentStep: string;
  approvalSteps: ApprovalStep[];
  estimatedApprovalTime: number;
  canProceed: boolean;
}
```

#### Mobile Optimizations
- Pre-filled forms based on work activity templates
- Location picker with GPS integration
- Camera integration for site photos
- Push notifications for status updates

### 4. Reporting & Documentation

#### Purpose
- Submit daily progress reports
- Report issues, delays, or safety concerns
- Document work completion with photos

#### Key Components
```typescript
// DailyReport.tsx - End-of-day progress reporting
interface DailyReport {
  id: string;
  date: Date;
  completedTasks: TaskSummary[];
  issuesEncountered: Issue[];
  tomorrowsPlans: string;
  weatherConditions: string;
  workersPresent: number;
  hoursWorked: number;
  photos: string[];
  submittedBy: string;
}

// IssueReport.tsx - Report problems or delays
interface IssueReport {
  id: string;
  type: 'delay' | 'safety' | 'quality' | 'equipment' | 'weather' | 'other';
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  location: string;
  affectedTasks: string[];
  photos: string[];
  reportedAt: Date;
  status: 'open' | 'investigating' | 'resolved';
}
```

#### Mobile Features
- Quick report templates
- Offline report creation with sync
- Multiple photo attachments
- Voice notes for detailed descriptions

## Mock Data Structure

### Authentication & User Context
```typescript
interface SiteEngineer {
  id: string;
  name: string;
  email: string;
  phone: string;
  employeeId: string;
  siteId: string;
  siteName: string;
  role: 'site-engineer';
  permissions: string[];
  supervisedWorkers: string[];
  createdAt: Date;
}
```

### Worker Data (Aligned with Main System)
```typescript
interface Worker {
  id: string;
  name: string;
  employeeId: string;
  trade: string;
  photo?: string;
  phone: string;
  email?: string;
  siteId: string;
  supervisorId: string; // Site engineer ID
  isActive: boolean;
  isOnSite: boolean;
  lastCheckIn?: Date;
  certifications: Certification[];
  trainings: Training[];
  hoursWorked: number;
  overtimeHours: number;
}
```

### Task Data (Simplified from Main System)
```typescript
interface EngineerTask {
  id: string;
  title: string;
  description: string;
  category: string;
  location: string;
  siteId: string;
  createdBy: string; // Engineer ID
  assignedWorkers: string[];
  plannedDate: Date;
  estimatedHours: number;
  priority: 'low' | 'medium' | 'high';
  status: 'draft' | 'submitted' | 'approved' | 'in-progress' | 'completed';
  requiresPermit: boolean;
  permitId?: string;
  progressPercentage: number;
  createdAt: Date;
  updatedAt: Date;
}
```

### Permit Data (Simplified Request View)
```typescript
interface EngineerPermitRequest {
  id: string;
  permitNumber?: string;
  workActivity: string;
  description: string;
  location: string;
  siteId: string;
  requestedBy: string; // Engineer ID
  assignedWorkers: string[];
  plannedStartDate: Date;
  plannedEndDate: Date;
  riskLevel: 'low' | 'medium' | 'high' | 'critical';
  status: 'draft' | 'submitted' | 'under-review' | 'approved' | 'rejected' | 'active' | 'closed';
  submittedAt?: Date;
  approvedAt?: Date;
  reviewNotes?: string;
  attachments: string[];
}
```

## API Integration Points

### Endpoints to Implement
```typescript
// Authentication
POST /api/auth/login
POST /api/auth/refresh
POST /api/auth/logout

// Workers
GET /api/engineers/{engineerId}/workers
POST /api/workers/{workerId}/overtime

// Tasks
GET /api/engineers/{engineerId}/tasks
POST /api/tasks
PUT /api/tasks/{taskId}
PUT /api/tasks/{taskId}/progress

// Permits
GET /api/engineers/{engineerId}/permits
POST /api/permits/request
GET /api/permits/{permitId}/status

// Reports
POST /api/reports/daily
POST /api/reports/issues
GET /api/reports/my-reports
```

### Data Synchronization Strategy
1. **Real-time Updates**: WebSocket connection for permit approvals and task updates
2. **Offline Support**: Local storage with sync queue for offline operations
3. **Conflict Resolution**: Last-write-wins with user notification for conflicts
4. **Background Sync**: Service worker for automatic sync when connection restored

## Getting Started

### Initial Setup Commands
```bash
# Create new React app
npx create-react-app site-engineer-app --template typescript

# Install dependencies
npm install react-router-dom axios lucide-react
npm install -D tailwindcss postcss autoprefixer
npm install -D @types/react @types/react-dom

# Initialize Tailwind CSS
npx tailwindcss init -p

# Install PWA dependencies
npm install workbox-webpack-plugin workbox-window
```

### Environment Configuration
```env
REACT_APP_API_BASE_URL=https://api.workforce.com
REACT_APP_WS_URL=wss://api.workforce.com/ws
REACT_APP_ENVIRONMENT=development
REACT_APP_VERSION=1.0.0
```

## Mobile-First Design Patterns

### Navigation Design
```typescript
// Bottom Tab Navigation (Mobile Standard)
interface TabNavigation {
  tabs: [
    { id: 'dashboard', label: 'Home', icon: 'Home' },
    { id: 'team', label: 'My Team', icon: 'Users' },
    { id: 'tasks', label: 'Tasks', icon: 'CheckSquare' },
    { id: 'permits', label: 'Permits', icon: 'Shield' },
    { id: 'reports', label: 'Reports', icon: 'FileText' }
  ];
}
```

### Touch Interactions
- **Swipe Actions**: Left swipe to edit, right swipe to complete
- **Pull to Refresh**: Standard mobile refresh pattern
- **Long Press**: Context menus for additional actions
- **Haptic Feedback**: Confirm actions with device vibration

### Offline-First Architecture
```typescript
// Service Worker Strategy
interface OfflineStrategy {
  cacheFirst: ['static-assets', 'app-shell'];
  networkFirst: ['api-calls', 'real-time-data'];
  staleWhileRevalidate: ['worker-photos', 'task-templates'];
}

// Local Storage Schema
interface LocalStorage {
  user: SiteEngineer;
  workers: Worker[];
  tasks: EngineerTask[];
  permits: EngineerPermitRequest[];
  syncQueue: PendingAction[];
  lastSync: Date;
}
```

## Implementation Phases

### Phase 1: Core Infrastructure (Week 1-2)
- [ ] Project setup with TypeScript and Tailwind
- [ ] Authentication system
- [ ] Basic navigation and layout
- [ ] API service layer
- [ ] Offline storage setup

### Phase 2: Worker Management (Week 3)
- [ ] My Team dashboard
- [ ] Worker list with photos and status
- [ ] Overtime request form
- [ ] Worker detail view

### Phase 3: Task Planning (Week 4-5)
- [ ] Task creation form
- [ ] Task assignment interface
- [ ] Task progress tracking
- [ ] Task approval workflow

### Phase 4: Permit Requests (Week 6)
- [ ] Permit request form
- [ ] Work activity templates
- [ ] Permit status tracking
- [ ] Photo attachments

### Phase 5: Reporting (Week 7)
- [ ] Daily progress reports
- [ ] Issue reporting
- [ ] Photo documentation
- [ ] Report history

### Phase 6: Polish & PWA (Week 8)
- [ ] Offline functionality
- [ ] Push notifications
- [ ] Performance optimization
- [ ] User testing and refinement

## Security Considerations

### Authentication & Authorization
```typescript
// JWT Token Management
interface AuthToken {
  accessToken: string;
  refreshToken: string;
  expiresAt: Date;
  permissions: string[];
}

// Role-Based Access Control
interface Permissions {
  canViewWorkers: boolean;
  canSubmitOvertime: boolean;
  canCreateTasks: boolean;
  canRequestPermits: boolean;
  canSubmitReports: boolean;
}
```

### Data Protection
- Encrypt sensitive data in local storage
- Implement certificate pinning for API calls
- Use secure HTTP headers
- Validate all user inputs
- Implement rate limiting on API calls

## Performance Optimization

### Bundle Size Management
```javascript
// Code Splitting Strategy
const TaskPlanning = lazy(() => import('./pages/TaskPlanning'));
const PermitRequests = lazy(() => import('./pages/PermitRequests'));
const Reports = lazy(() => import('./pages/Reports'));

// Image Optimization
const optimizeImage = (file: File) => {
  // Compress images before upload
  // Resize to maximum 1920x1080
  // Convert to WebP format when supported
};
```

### Caching Strategy
- Cache worker photos and frequently accessed data
- Implement intelligent prefetching
- Use compression for API responses
- Minimize re-renders with React.memo and useMemo

## Testing Strategy

### Unit Testing
```typescript
// Example test for task creation
describe('TaskCreation', () => {
  it('should create task with required fields', () => {
    const task = createTask({
      title: 'Concrete Pouring',
      workers: ['worker-1', 'worker-2'],
      location: 'Zone A'
    });
    expect(task.status).toBe('draft');
    expect(task.assignedWorkers).toHaveLength(2);
  });
});
```

### Integration Testing
- Test API integration with mock server
- Test offline functionality
- Test data synchronization
- Test push notification handling

### User Acceptance Testing
- Field testing with actual site engineers
- Usability testing on various devices
- Performance testing on slow networks
- Accessibility testing for outdoor use

## Deployment & DevOps

### Build Configuration
```javascript
// webpack.config.js optimizations
module.exports = {
  optimization: {
    splitChunks: {
      chunks: 'all',
      cacheGroups: {
        vendor: {
          test: /[\\/]node_modules[\\/]/,
          name: 'vendors',
          chunks: 'all',
        },
      },
    },
  },
  plugins: [
    new WorkboxPlugin.GenerateSW({
      clientsClaim: true,
      skipWaiting: true,
      runtimeCaching: [
        {
          urlPattern: /^https:\/\/api\.workforce\.com/,
          handler: 'NetworkFirst',
          options: {
            cacheName: 'api-cache',
            expiration: {
              maxEntries: 50,
              maxAgeSeconds: 300, // 5 minutes
            },
          },
        },
      ],
    }),
  ],
};
```

### CI/CD Pipeline
```yaml
# .github/workflows/deploy.yml
name: Deploy Site Engineer App
on:
  push:
    branches: [main]
jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - name: Setup Node.js
        uses: actions/setup-node@v2
        with:
          node-version: '18'
      - name: Install dependencies
        run: npm ci
      - name: Run tests
        run: npm test -- --coverage --watchAll=false
      - name: Build application
        run: npm run build
      - name: Deploy to staging
        run: npm run deploy:staging
```

## Monitoring & Analytics

### Error Tracking
```typescript
// Error boundary for crash reporting
class ErrorBoundary extends Component {
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Send error to monitoring service
    analytics.track('app_error', {
      error: error.message,
      stack: error.stack,
      component: errorInfo.componentStack
    });
  }
}
```

### Usage Analytics
- Track feature usage patterns
- Monitor performance metrics
- Analyze user journey flows
- Measure task completion rates

## Maintenance & Updates

### Version Management
- Semantic versioning (1.0.0)
- Feature flags for gradual rollouts
- Backward compatibility for API changes
- Automated dependency updates

### User Feedback Loop
- In-app feedback mechanism
- Regular user surveys
- Feature request tracking
- Performance monitoring alerts

This comprehensive implementation guide provides everything needed to create a successful mobile-first Site Engineer application that integrates seamlessly with the existing workforce management system while providing a tailored, efficient experience for field engineers.
