// Core types for Site Engineer Mobile Application

// User Authentication Types
export interface SiteEngineer {
  id: string;
  name: string;
  email: string;
  phone: string;
  employeeId: string;
  siteId: string;
  siteName: string;
  role: 'site-engineer';
  permissions: string[];
  supervisedWorkers: string[];
  createdAt: Date;
}

// Authentication Context
export interface AuthContextType {
  user: SiteEngineer | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<void>;
  logout: () => void;
  loading: boolean;
}

// API Response Types
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  error?: string;
}

// Navigation Types
export interface TabItem {
  id: string;
  label: string;
  icon: React.ReactNode;
  badge?: number;
  path: string;
}

// Common UI Types
export interface SelectOption {
  value: string;
  label: string;
  disabled?: boolean;
}

export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'number' | 'tel' | 'textarea' | 'select' | 'date' | 'time';
  placeholder?: string;
  required?: boolean;
  options?: SelectOption[];
  validation?: {
    min?: number;
    max?: number;
    pattern?: string;
    message?: string;
  };
}

// Status Types
export type TaskStatus = 'draft' | 'submitted' | 'approved' | 'in-progress' | 'completed' | 'blocked';
export type PermitStatus = 'draft' | 'submitted' | 'under-review' | 'approved' | 'rejected' | 'active' | 'closed';
export type Priority = 'low' | 'medium' | 'high' | 'critical';
export type RiskLevel = 'low' | 'medium' | 'high' | 'critical';

// Offline Sync Types
export interface PendingAction {
  id: string;
  type: 'create' | 'update' | 'delete';
  entity: 'task' | 'permit' | 'report' | 'overtime';
  data: any;
  timestamp: Date;
  retryCount: number;
}

export interface SyncStatus {
  isOnline: boolean;
  lastSync: Date | null;
  pendingActions: PendingAction[];
  syncInProgress: boolean;
}
