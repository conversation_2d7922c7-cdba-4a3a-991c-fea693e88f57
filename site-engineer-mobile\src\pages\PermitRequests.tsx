import React, { useState } from 'react';
import { Shield, Plus, Clock, AlertTriangle, CheckCircle, XCircle, Eye } from 'lucide-react';
import MobileLayout from '../components/layout/MobileLayout';
import Card from '../components/common/Card';
import Button from '../components/common/Button';
import StatusBadge from '../components/common/StatusBadge';

// Local type definitions to avoid module resolution issues
type PermitStatus = 'draft' | 'submitted' | 'under-review' | 'approved' | 'rejected' | 'active' | 'closed';
type Priority = 'low' | 'medium' | 'high' | 'critical';
type RiskLevel = 'low' | 'medium' | 'high' | 'critical';

interface EngineerPermitRequest {
  id: string;
  permitNumber?: string;
  workActivity: string;
  description: string;
  location: string;
  siteId: string;
  requestedBy: string;
  assignedWorkers: string[];
  plannedStartDate: Date;
  plannedEndDate: Date;
  estimatedDuration: number;
  riskLevel: RiskLevel;
  priority: Priority;
  status: PermitStatus;
  submittedAt?: Date;
  approvedAt?: Date;
  reviewedBy?: string;
  reviewNotes?: string;
  rejectionReason?: string;
  attachments: string[];
  requiredPPE: string[];
  safetyRequirements: string[];
  hazards: string[];
  controlMeasures: string[];
  emergencyProcedures: string[];
  createdAt: Date;
  updatedAt: Date;
}

interface WorkActivityTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  riskLevel: RiskLevel;
  estimatedDuration: number;
  requiredPermitTypes: string[];
  requiredPPE: string[];
  standardHazards: string[];
  standardControlMeasures: string[];
  safetyRequirements: string[];
  isActive: boolean;
}

interface Worker {
  id: string;
  name: string;
  trade: string;
}

// Mock data
const mockPermitRequests: EngineerPermitRequest[] = [
  {
    id: 'permit-1',
    permitNumber: 'HWP-2024-001',
    workActivity: 'Hot Work - Welding',
    description: 'Structural steel welding for second floor framework',
    location: 'Block A Second Floor',
    siteId: 'site-1',
    requestedBy: 'engineer-1',
    assignedWorkers: ['worker-1'],
    plannedStartDate: new Date(),
    plannedEndDate: new Date(Date.now() + 4 * 60 * 60 * 1000),
    estimatedDuration: 4,
    riskLevel: 'high',
    priority: 'critical',
    status: 'approved',
    submittedAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    approvedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    reviewedBy: 'safety-officer-1',
    reviewNotes: 'All safety requirements met. Fire watch assigned.',
    attachments: ['risk-assessment.pdf', 'method-statement.pdf'],
    requiredPPE: ['Welding helmet', 'Fire-resistant clothing', 'Safety boots', 'Gloves'],
    safetyRequirements: ['Fire watch', 'Fire extinguisher nearby', 'Clear combustibles'],
    hazards: ['Fire risk', 'Hot surfaces', 'Fumes', 'Arc flash'],
    controlMeasures: ['Fire watch present', 'Ventilation provided', 'PPE worn', 'Fire extinguisher ready'],
    emergencyProcedures: ['Stop work immediately', 'Alert fire watch', 'Use fire extinguisher if safe'],
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
  },
  {
    id: 'permit-2',
    permitNumber: 'EWP-2024-002',
    workActivity: 'Electrical Installation',
    description: 'Installation of main electrical distribution panel',
    location: 'Block B Electrical Room',
    siteId: 'site-1',
    requestedBy: 'engineer-1',
    assignedWorkers: ['worker-2'],
    plannedStartDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
    plannedEndDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000 + 6 * 60 * 60 * 1000),
    estimatedDuration: 6,
    riskLevel: 'high',
    priority: 'medium',
    status: 'under-review',
    submittedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    attachments: ['electrical-drawings.pdf'],
    requiredPPE: ['Insulated gloves', 'Arc flash suit', 'Safety boots', 'Hard hat'],
    safetyRequirements: ['Lockout/Tagout', 'Voltage testing', 'Isolation verification'],
    hazards: ['Electrical shock', 'Arc flash', 'Burns', 'Falls'],
    controlMeasures: ['LOTO procedures', 'Voltage testing', 'Insulated tools', 'Proper PPE'],
    emergencyProcedures: ['De-energize immediately', 'Call emergency services', 'Provide first aid'],
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
  }
];

const mockWorkActivityTemplates: WorkActivityTemplate[] = [
  {
    id: 'activity-1',
    name: 'Hot Work - Welding',
    description: 'Welding operations requiring hot work permit',
    category: 'Hot Work',
    riskLevel: 'high',
    estimatedDuration: 4,
    requiredPermitTypes: ['Hot Work Permit'],
    requiredPPE: ['Welding helmet', 'Fire-resistant clothing', 'Safety boots', 'Gloves'],
    standardHazards: ['Fire risk', 'Hot surfaces', 'Fumes', 'Arc flash'],
    standardControlMeasures: ['Fire watch', 'Ventilation', 'PPE', 'Fire extinguisher'],
    safetyRequirements: ['Fire watch', 'Fire extinguisher nearby', 'Clear combustibles'],
    isActive: true
  },
  {
    id: 'activity-2',
    name: 'Electrical Installation',
    description: 'Electrical wiring and equipment installation',
    category: 'Electrical',
    riskLevel: 'high',
    estimatedDuration: 6,
    requiredPermitTypes: ['Electrical Work Permit'],
    requiredPPE: ['Insulated gloves', 'Arc flash suit', 'Safety boots', 'Hard hat'],
    standardHazards: ['Electrical shock', 'Arc flash', 'Burns', 'Falls'],
    standardControlMeasures: ['LOTO procedures', 'Voltage testing', 'Insulated tools', 'PPE'],
    safetyRequirements: ['Lockout/Tagout', 'Voltage testing', 'Isolation verification'],
    isActive: true
  }
];

const mockWorkers: Worker[] = [
  { id: 'worker-1', name: 'David Kamau', trade: 'Welder' },
  { id: 'worker-2', name: 'Mary Wanjiku', trade: 'Electrician' },
  { id: 'worker-3', name: 'Peter Ochieng', trade: 'Carpenter' },
  { id: 'worker-4', name: 'Grace Muthoni', trade: 'Mason' }
];

interface PermitRequestsProps {
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

const PermitRequests: React.FC<PermitRequestsProps> = ({ activeTab, onTabChange }) => {
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [selectedActivity, setSelectedActivity] = useState<WorkActivityTemplate | null>(null);
  const [workActivity, setWorkActivity] = useState('');
  const [description, setDescription] = useState('');
  const [location, setLocation] = useState('');
  const [plannedStartDate, setPlannedStartDate] = useState('');
  const [plannedEndDate, setPlannedEndDate] = useState('');
  const [selectedWorkers, setSelectedWorkers] = useState<string[]>([]);
  const [additionalHazards, setAdditionalHazards] = useState('');
  const [controlMeasures, setControlMeasures] = useState('');

  const activePermits = mockPermitRequests.filter(p => p.status === 'approved' || p.status === 'active');
  const pendingPermits = mockPermitRequests.filter(p => p.status === 'submitted' || p.status === 'under-review');
  const draftPermits = mockPermitRequests.filter(p => p.status === 'draft');

  const handleActivitySelect = (activity: WorkActivityTemplate) => {
    setSelectedActivity(activity);
    setWorkActivity(activity.name);
    setDescription(activity.description);
  };

  const handleWorkerToggle = (workerId: string) => {
    setSelectedWorkers(prev =>
      prev.includes(workerId)
        ? prev.filter(id => id !== workerId)
        : [...prev, workerId]
    );
  };

  const handleSubmitPermit = () => {
    if (!workActivity || !location || !plannedStartDate || !plannedEndDate) return;

    const newPermit: Partial<EngineerPermitRequest> = {
      workActivity,
      description,
      location,
      plannedStartDate: new Date(plannedStartDate),
      plannedEndDate: new Date(plannedEndDate),
      assignedWorkers: selectedWorkers,
      riskLevel: selectedActivity?.riskLevel || 'medium',
      priority: 'medium',
      status: 'draft',
      requiredPPE: selectedActivity?.requiredPPE || [],
      safetyRequirements: selectedActivity?.safetyRequirements || [],
      hazards: selectedActivity?.standardHazards || [],
      controlMeasures: selectedActivity?.standardControlMeasures || [],
      attachments: []
    };

    console.log('Creating permit request:', newPermit);

    // Reset form
    setShowCreateModal(false);
    setSelectedActivity(null);
    setWorkActivity('');
    setDescription('');
    setLocation('');
    setPlannedStartDate('');
    setPlannedEndDate('');
    setSelectedWorkers([]);
    setAdditionalHazards('');
    setControlMeasures('');
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'approved':
      case 'active':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'rejected':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'under-review':
      case 'submitted':
        return <Clock className="h-4 w-4 text-yellow-600" />;
      default:
        return <Shield className="h-4 w-4 text-gray-600" />;
    }
  };

  const getRiskLevelColor = (riskLevel: string) => {
    switch (riskLevel) {
      case 'critical': return 'text-red-600 bg-red-50';
      case 'high': return 'text-orange-600 bg-orange-50';
      case 'medium': return 'text-yellow-600 bg-yellow-50';
      case 'low': return 'text-green-600 bg-green-50';
      default: return 'text-gray-600 bg-gray-50';
    }
  };

  return (
    <MobileLayout
      title="Permit Requests"
      activeTab={activeTab}
      onTabChange={onTabChange}
      headerActions={
        <Button
          variant="primary"
          size="sm"
          onClick={() => setShowCreateModal(true)}
        >
          <Plus className="h-4 w-4" />
        </Button>
      }
    >
      <div className="p-4 space-y-6">
        {/* Quick Stats */}
        <div className="grid grid-cols-3 gap-4">
          <Card padding="md">
            <div className="text-center">
              <p className="text-2xl font-semibold text-green-600">{activePermits.length}</p>
              <p className="text-sm text-gray-500">Active</p>
            </div>
          </Card>
          <Card padding="md">
            <div className="text-center">
              <p className="text-2xl font-semibold text-yellow-600">{pendingPermits.length}</p>
              <p className="text-sm text-gray-500">Pending</p>
            </div>
          </Card>
          <Card padding="md">
            <div className="text-center">
              <p className="text-2xl font-semibold text-gray-600">{draftPermits.length}</p>
              <p className="text-sm text-gray-500">Drafts</p>
            </div>
          </Card>
        </div>

        {/* Active Permits */}
        {activePermits.length > 0 && (
          <Card title="Active Permits" padding="md">
            <div className="space-y-3">
              {activePermits.map((permit) => (
                <div key={permit.id} className="p-3 bg-green-50 rounded-lg border border-green-200">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        {getStatusIcon(permit.status)}
                        <h4 className="font-medium text-gray-900">{permit.workActivity}</h4>
                      </div>
                      {permit.permitNumber && (
                        <p className="text-xs text-gray-500 mb-1">#{permit.permitNumber}</p>
                      )}
                      <p className="text-sm text-gray-600">{permit.location}</p>
                    </div>
                    <StatusBadge status={permit.status} size="sm" type="permit" />
                  </div>
                  
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center space-x-3">
                      <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskLevelColor(permit.riskLevel)}`}>
                        {permit.riskLevel.toUpperCase()}
                      </span>
                      <span>{permit.assignedWorkers.length} workers</span>
                    </div>
                    <button className="text-blue-600 hover:text-blue-800">
                      <Eye className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        )}

        {/* Pending Permits */}
        {pendingPermits.length > 0 && (
          <Card title="Pending Approval" padding="md">
            <div className="space-y-3">
              {pendingPermits.map((permit) => (
                <div key={permit.id} className="p-3 bg-yellow-50 rounded-lg border border-yellow-200">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <div className="flex items-center space-x-2 mb-1">
                        {getStatusIcon(permit.status)}
                        <h4 className="font-medium text-gray-900">{permit.workActivity}</h4>
                      </div>
                      <p className="text-sm text-gray-600">{permit.location}</p>
                      {permit.submittedAt && (
                        <p className="text-xs text-gray-500 mt-1">
                          Submitted {permit.submittedAt.toLocaleDateString()}
                        </p>
                      )}
                    </div>
                    <StatusBadge status={permit.status} size="sm" type="permit" />
                  </div>
                  
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskLevelColor(permit.riskLevel)}`}>
                      {permit.riskLevel.toUpperCase()}
                    </span>
                    <button className="text-blue-600 hover:text-blue-800">
                      <Eye className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        )}

        {/* Draft Permits */}
        {draftPermits.length > 0 && (
          <Card title="Draft Permits" padding="md">
            <div className="space-y-3">
              {draftPermits.map((permit) => (
                <div key={permit.id} className="p-3 bg-gray-50 rounded-lg border border-gray-200">
                  <div className="flex items-start justify-between mb-2">
                    <div className="flex-1">
                      <h4 className="font-medium text-gray-900">{permit.workActivity}</h4>
                      <p className="text-sm text-gray-600">{permit.location}</p>
                    </div>
                    <StatusBadge status={permit.status} size="sm" type="permit" />
                  </div>
                  
                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span className={`px-2 py-1 rounded-full text-xs font-medium ${getRiskLevelColor(permit.riskLevel)}`}>
                      {permit.riskLevel.toUpperCase()}
                    </span>
                    <button className="text-blue-600 hover:text-blue-800">
                      Continue Editing
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        )}

        {/* Quick Actions */}
        <Card title="Quick Actions" padding="md">
          <div className="grid grid-cols-2 gap-3">
            <Button
              variant="primary"
              fullWidth
              onClick={() => setShowCreateModal(true)}
            >
              <Plus className="h-4 w-4 mr-2" />
              New Permit
            </Button>
            <Button
              variant="secondary"
              fullWidth
              onClick={() => onTabChange('tasks')}
            >
              <Shield className="h-4 w-4 mr-2" />
              View Tasks
            </Button>
          </div>
        </Card>
      </div>

      {/* Create Permit Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-end justify-center z-50">
          <div className="bg-white rounded-t-lg w-full max-w-md max-h-[90vh] overflow-y-auto">
            <div className="sticky top-0 bg-white border-b border-gray-200 p-4">
              <div className="flex items-center justify-between">
                <h3 className="text-lg font-semibold">Request Work Permit</h3>
                <button
                  onClick={() => setShowCreateModal(false)}
                  className="text-gray-400 hover:text-gray-600"
                >
                  ×
                </button>
              </div>
            </div>

            <div className="p-4 space-y-4">
              {/* Work Activity Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Work Activity *
                </label>
                <select
                  value={selectedActivity?.id || ''}
                  onChange={(e) => {
                    const activity = mockWorkActivityTemplates.find(a => a.id === e.target.value);
                    if (activity) handleActivitySelect(activity);
                  }}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                >
                  <option value="">Select work activity...</option>
                  {mockWorkActivityTemplates.map((activity) => (
                    <option key={activity.id} value={activity.id}>
                      {activity.name}
                    </option>
                  ))}
                </select>
              </div>

              {/* Custom Work Activity */}
              {!selectedActivity && (
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Custom Work Activity
                  </label>
                  <input
                    type="text"
                    value={workActivity}
                    onChange={(e) => setWorkActivity(e.target.value)}
                    placeholder="Describe the work activity..."
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              )}

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Work Description *
                </label>
                <textarea
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  placeholder="Detailed description of the work..."
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Location */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Work Location *
                </label>
                <input
                  type="text"
                  value={location}
                  onChange={(e) => setLocation(e.target.value)}
                  placeholder="Specific work location..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Dates */}
              <div className="grid grid-cols-2 gap-3">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Start Date *
                  </label>
                  <input
                    type="date"
                    value={plannedStartDate}
                    onChange={(e) => setPlannedStartDate(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    End Date *
                  </label>
                  <input
                    type="date"
                    value={plannedEndDate}
                    onChange={(e) => setPlannedEndDate(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
              </div>

              {/* Worker Assignment */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Assign Workers
                </label>
                <div className="space-y-2 max-h-32 overflow-y-auto border border-gray-300 rounded-lg p-2">
                  {mockWorkers.map((worker) => (
                    <label key={worker.id} className="flex items-center space-x-3 cursor-pointer">
                      <input
                        type="checkbox"
                        checked={selectedWorkers.includes(worker.id)}
                        onChange={() => handleWorkerToggle(worker.id)}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                      />
                      <span className="text-sm text-gray-900">{worker.name} - {worker.trade}</span>
                    </label>
                  ))}
                </div>
              </div>

              {/* Risk Information */}
              {selectedActivity && (
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
                  <div className="flex items-center mb-2">
                    <AlertTriangle className="h-4 w-4 text-yellow-600 mr-2" />
                    <span className="text-sm font-medium text-yellow-800">
                      Risk Level: {selectedActivity.riskLevel.toUpperCase()}
                    </span>
                  </div>
                  <div className="text-xs text-yellow-700">
                    <p className="mb-1"><strong>Required PPE:</strong> {selectedActivity.requiredPPE.join(', ')}</p>
                    <p><strong>Safety Requirements:</strong> {selectedActivity.safetyRequirements.join(', ')}</p>
                  </div>
                </div>
              )}

              {/* Additional Hazards */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Additional Hazards
                </label>
                <textarea
                  value={additionalHazards}
                  onChange={(e) => setAdditionalHazards(e.target.value)}
                  placeholder="Any additional hazards specific to this work..."
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Control Measures */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Additional Control Measures
                </label>
                <textarea
                  value={controlMeasures}
                  onChange={(e) => setControlMeasures(e.target.value)}
                  placeholder="Additional safety measures to be implemented..."
                  rows={2}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Actions */}
              <div className="flex space-x-3 pt-4">
                <Button
                  variant="secondary"
                  fullWidth
                  onClick={() => setShowCreateModal(false)}
                >
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  fullWidth
                  onClick={handleSubmitPermit}
                  disabled={!workActivity || !location || !plannedStartDate || !plannedEndDate}
                >
                  Submit Request
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </MobileLayout>
  );
};

export default PermitRequests;
