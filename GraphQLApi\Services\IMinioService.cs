using Shared.Enums;
using Shared.GraphQL.Models;

namespace GraphQLApi.Services
{
    /// <summary>
    /// Interface for MinIO file storage operations
    /// </summary>
    public interface IMinioService
    {
        /// <summary>
        /// Upload a file to MinIO and create metadata record
        /// </summary>
        /// <param name="stream">File stream</param>
        /// <param name="fileName">Original filename</param>
        /// <param name="bucketName">Target bucket</param>
        /// <param name="contentType">MIME content type</param>
        /// <param name="description">Optional file description</param>
        /// <param name="folderPath">Optional folder path within bucket</param>
        /// <param name="isPublic">Whether file should be publicly accessible</param>
        /// <param name="expiresAt">Optional expiration date for temporary files</param>
        /// <returns>FileMetadata record with MinIO details</returns>
        Task<FileMetadata> UploadFileAsync(
            Stream stream,
            string fileName,
            string bucketName,
            string contentType,
            string? description = null,
            string? folderPath = null,
            bool isPublic = false,
            DateTime? expiresAt = null);

        /// <summary>
        /// Download a file from MinIO
        /// </summary>
        /// <param name="fileMetadata">File metadata record</param>
        /// <returns>File stream</returns>
        Task<Stream> DownloadFileAsync(FileMetadata fileMetadata);

        /// <summary>
        /// Download a file by object key and bucket
        /// </summary>
        /// <param name="bucketName">Bucket name</param>
        /// <param name="objectKey">Object key</param>
        /// <returns>File stream</returns>
        Task<Stream> DownloadFileAsync(string bucketName, string objectKey);

        /// <summary>
        /// Delete a file from MinIO and mark metadata as deleted
        /// </summary>
        /// <param name="fileMetadata">File metadata record</param>
        /// <returns>Success status</returns>
        Task<bool> DeleteFileAsync(FileMetadata fileMetadata);

        /// <summary>
        /// Delete a file by object key and bucket
        /// </summary>
        /// <param name="bucketName">Bucket name</param>
        /// <param name="objectKey">Object key</param>
        /// <returns>Success status</returns>
        Task<bool> DeleteFileAsync(string bucketName, string objectKey);

        /// <summary>
        /// Create a folder (prefix) in a bucket
        /// </summary>
        /// <param name="bucketName">Bucket name</param>
        /// <param name="folderPath">Folder path</param>
        /// <returns>Success status</returns>
        Task<bool> CreateFolderAsync(string bucketName, string folderPath);

        /// <summary>
        /// List files in a bucket with optional prefix
        /// </summary>
        /// <param name="bucketName">Bucket name</param>
        /// <param name="prefix">Optional prefix filter</param>
        /// <returns>List of object information</returns>
        Task<IEnumerable<MinioObjectInfo>> ListFilesAsync(string bucketName, string? prefix = null);

        /// <summary>
        /// Get a presigned URL for file access
        /// </summary>
        /// <param name="fileMetadata">File metadata record</param>
        /// <param name="expiryInSeconds">URL expiry time in seconds</param>
        /// <returns>Presigned URL</returns>
        Task<string> GetPresignedUrlAsync(FileMetadata fileMetadata, int expiryInSeconds = 3600);

        /// <summary>
        /// Get a presigned URL for file upload
        /// </summary>
        /// <param name="bucketName">Bucket name</param>
        /// <param name="objectKey">Object key</param>
        /// <param name="expiryInSeconds">URL expiry time in seconds</param>
        /// <returns>Presigned URL</returns>
        Task<string> GetPresignedUploadUrlAsync(string bucketName, string objectKey, int expiryInSeconds = 3600);

        /// <summary>
        /// Check if a file exists in MinIO
        /// </summary>
        /// <param name="bucketName">Bucket name</param>
        /// <param name="objectKey">Object key</param>
        /// <returns>True if file exists</returns>
        Task<bool> FileExistsAsync(string bucketName, string objectKey);

        /// <summary>
        /// Get file information from MinIO
        /// </summary>
        /// <param name="bucketName">Bucket name</param>
        /// <param name="objectKey">Object key</param>
        /// <returns>Object stat information</returns>
        Task<MinioObjectInfo?> GetFileInfoAsync(string bucketName, string objectKey);

        /// <summary>
        /// Validate file type against allowed types
        /// </summary>
        /// <param name="fileName">File name</param>
        /// <param name="contentType">Content type</param>
        /// <returns>Validation result</returns>
        Task<(bool IsValid, AllowedFileType? FileType, string? ErrorMessage)> ValidateFileTypeAsync(string fileName, string contentType);

        /// <summary>
        /// Initialize required buckets
        /// </summary>
        /// <returns>Success status</returns>
        Task<bool> InitializeBucketsAsync();
    }

    /// <summary>
    /// MinIO object information
    /// </summary>
    public class MinioObjectInfo
    {
        public string ObjectName { get; set; } = string.Empty;
        public long Size { get; set; }
        public DateTime LastModified { get; set; }
        public string ETag { get; set; } = string.Empty;
        public string ContentType { get; set; } = string.Empty;
        public Dictionary<string, string> Metadata { get; set; } = new();
    }
}
