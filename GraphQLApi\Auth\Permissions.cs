﻿namespace GraphQLApi.Auth
{
    /*this class consists of 
     * 1. the admin who has R/W over everything in the system
     * 2. the line manager who has same permissions as admin but readonly
     * 3. Accountant who has same permisions as admin but only write in money
     * 4. Main HSE who has R/W over everything related to safety. 
     * 5. Site HSE who has R/W over everything hse but site level
     * 6. Site engineer who has minimal R/W limited to site level. 
     */
    public class Permissions
    {
        public enum permissions
        {
            None = 0,
            ViewWorkers = 1,
        }
    }
}
