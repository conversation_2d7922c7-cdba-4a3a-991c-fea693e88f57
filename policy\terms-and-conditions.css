 .u-section-1 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-1 .u-sheet-1 {
  min-height: 908px;
}

.u-section-1 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: -36px;
}

.u-section-1 .u-layout-cell-1 {
  min-height: 868px;
}

.u-section-1 .u-container-layout-1 {
  padding: 30px 29px;
}

.u-section-1 .u-text-1 {
  font-weight: 400;
  margin: 0;
}

.u-section-1 .u-text-2 {
  font-size: 1.25rem;
  margin: 10px auto 0 1px;
}

.u-section-1 .u-text-3 {
  font-size: 1.25rem;
  margin: 10px auto 0 1px;
}

.u-section-1 .u-text-4 {
  font-size: 1rem;
  margin: 5px auto 0 1px;
}

.u-section-1 .u-text-5 {
  font-size: 1rem;
  margin: 20px auto 0 1px;
}

.u-section-1 .u-layout-cell-2 {
  min-height: 868px;
}

.u-section-1 .u-container-layout-2 {
  padding: 30px;
}

.u-section-1 .u-list-1 {
  margin: 0;
}

.u-section-1 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 807px;
  grid-gap: 10px;
}

.u-section-1 .u-container-layout-3 {
  padding: 10px;
}

.u-section-1 .u-text-6 {
  font-size: 1.25rem;
  margin: 0 auto 0 0;
}

.u-section-1 .u-text-7 {
  font-size: 1rem;
  margin: 10px 0 0;
}

.u-section-1 .u-btn-1 {
  background-image: none;
  padding: 0;
}

.u-section-1 .u-container-layout-4 {
  padding: 10px;
}

.u-section-1 .u-text-8 {
  font-size: 1.25rem;
  margin: 0 auto 0 0;
}

.u-section-1 .u-text-9 {
  font-size: 1rem;
  margin: 10px 0 0;
}

.u-section-1 .u-container-layout-5 {
  padding: 10px;
}

.u-section-1 .u-text-10 {
  font-size: 1.25rem;
  margin: 0 auto 0 0;
}

.u-section-1 .u-text-11 {
  font-size: 1rem;
  margin: 10px 0 0;
}

.u-section-1 .u-container-layout-6 {
  padding: 10px;
}

.u-section-1 .u-text-12 {
  font-size: 1.25rem;
  margin: 0 auto 0 0;
}

.u-section-1 .u-text-13 {
  font-size: 1rem;
  margin: 10px 0 0;
}

.u-section-1 .u-container-layout-7 {
  padding: 10px;
}

.u-section-1 .u-text-14 {
  font-size: 1.25rem;
  margin: 0 auto 0 0;
}

.u-section-1 .u-text-15 {
  font-size: 1rem;
  margin: 10px 0 0;
}

.u-section-1 .u-container-layout-8 {
  padding: 10px;
}

.u-section-1 .u-text-16 {
  font-size: 1.25rem;
  margin: 0 auto 0 0;
}

.u-section-1 .u-text-17 {
  font-size: 1rem;
  margin: 10px 0 0;
}

.u-section-1 .u-container-layout-9 {
  padding: 10px;
}

.u-section-1 .u-text-18 {
  font-size: 1.25rem;
  margin: 0 auto 0 0;
}

.u-section-1 .u-text-19 {
  font-size: 1rem;
  margin: 10px 0 0;
}

@media (max-width: 1199px) {
  .u-section-1 .u-sheet-1 {
    min-height: 253px;
  }

  .u-section-1 .u-layout-wrap-1 {
    position: relative;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 723px;
  }

  .u-section-1 .u-text-1 {
    font-size: 1.5rem;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 723px;
  }

  .u-section-1 .u-list-1 {
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-1 .u-repeater-1 {
    grid-template-columns: 100%;
  }
}

@media (max-width: 991px) {
  .u-section-1 .u-sheet-1 {
    min-height: 20px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 100px;
  }
}

@media (max-width: 767px) {
  .u-section-1 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }
} .u-section-2 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-2 .u-sheet-1 {
  min-height: 798px;
}

.u-section-2 .u-layout-wrap-1 {
  margin-top: 20px;
  margin-bottom: -9px;
}

.u-section-2 .u-layout-cell-1 {
  min-height: 758px;
}

.u-section-2 .u-container-layout-1 {
  padding: 30px 29px;
}

.u-section-2 .u-text-1 {
  font-weight: 400;
  margin: 0 1px;
}

.u-section-2 .u-text-2 {
  font-size: 1.25rem;
  margin: 10px auto 0 1px;
}

.u-section-2 .u-text-3 {
  font-size: 1.25rem;
  margin: 10px auto 0 1px;
}

.u-section-2 .u-text-4 {
  font-size: 1rem;
  margin: 5px auto 0 1px;
}

.u-section-2 .u-text-5 {
  font-size: 1rem;
  margin: 20px auto 0 1px;
}

.u-section-2 .u-layout-cell-2 {
  min-height: 758px;
}

.u-section-2 .u-container-layout-2 {
  padding: 30px 30px 29px;
}

.u-section-2 .u-list-1 {
  margin: 0;
}

.u-section-2 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 698px;
  grid-gap: 10px;
}

.u-section-2 .u-container-layout-3 {
  padding: 10px;
}

.u-section-2 .u-text-6 {
  font-size: 1.25rem;
  margin: 0 auto 0 0;
}

.u-section-2 .u-text-7 {
  font-size: 1rem;
  margin: 10px 0 0;
}

.u-section-2 .u-container-layout-4 {
  padding: 10px;
}

.u-section-2 .u-text-8 {
  font-size: 1.25rem;
  margin: 0 auto 0 0;
}

.u-section-2 .u-text-9 {
  font-size: 1rem;
  margin: 10px 0 0;
}

.u-section-2 .u-container-layout-5 {
  padding: 10px;
}

.u-section-2 .u-text-10 {
  font-size: 1.25rem;
  margin: 0 auto 0 0;
}

.u-section-2 .u-text-11 {
  font-size: 1rem;
  margin: 10px 0 0;
}

.u-section-2 .u-container-layout-6 {
  padding: 10px;
}

.u-section-2 .u-text-12 {
  font-size: 1.25rem;
  margin: 0 auto 0 0;
}

.u-section-2 .u-text-13 {
  font-size: 1rem;
  margin: 10px 0 0;
}

.u-section-2 .u-container-layout-7 {
  padding: 10px;
}

.u-section-2 .u-text-14 {
  font-size: 1.25rem;
  margin: 0 auto 0 0;
}

.u-section-2 .u-text-15 {
  font-size: 1rem;
  margin: 10px 0 0;
}

.u-section-2 .u-container-layout-8 {
  padding: 10px;
}

.u-section-2 .u-text-16 {
  font-size: 1.25rem;
  margin: 0 auto 0 0;
}

.u-section-2 .u-text-17 {
  font-size: 1rem;
  margin: 10px 0 0;
}

@media (max-width: 1199px) {
  .u-section-2 .u-sheet-1 {
    min-height: 253px;
  }

  .u-section-2 .u-layout-wrap-1 {
    position: relative;
  }

  .u-section-2 .u-layout-cell-1 {
    min-height: 632px;
  }

  .u-section-2 .u-text-1 {
    font-size: 1.5rem;
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-2 .u-layout-cell-2 {
    min-height: 632px;
  }

  .u-section-2 .u-list-1 {
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-2 .u-repeater-1 {
    grid-template-columns: 100%;
  }
}

@media (max-width: 991px) {
  .u-section-2 .u-sheet-1 {
    min-height: 20px;
  }

  .u-section-2 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-2 .u-layout-cell-2 {
    min-height: 100px;
  }
}

@media (max-width: 767px) {
  .u-section-2 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-2 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }
}