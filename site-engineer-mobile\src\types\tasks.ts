// Task-related types for Site Engineer Mobile Application

// Status Types
export type TaskStatus = 'draft' | 'submitted' | 'approved' | 'in-progress' | 'completed' | 'blocked';
export type Priority = 'low' | 'medium' | 'high' | 'critical';
export type RiskLevel = 'low' | 'medium' | 'high' | 'critical';

export interface EngineerTask {
  id: string;
  title: string;
  description: string;
  category: string;
  location: string;
  siteId: string;
  createdBy: string; // Engineer ID
  assignedWorkers: string[];
  plannedDate: Date;
  estimatedHours: number;
  priority: Priority;
  status: TaskStatus;
  requiresPermit: boolean;
  permitId?: string;
  progressPercentage: number;
  actualStartTime?: Date;
  actualEndTime?: Date;
  actualHours?: number;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface TaskProgress {
  taskId: string;
  status: TaskStatus;
  progressPercentage: number;
  notes?: string;
  updatedAt: Date;
  updatedBy: string;
  photos?: string[];
}

export interface TaskTemplate {
  id: string;
  name: string;
  description: string;
  category: string;
  estimatedHours: number;
  requiredWorkers: number;
  requiredSkills: string[];
  requiresPermit: boolean;
  riskLevel: RiskLevel;
  safetyRequirements: string[];
  isActive: boolean;
}

// Mock data for development
export const mockTasks: EngineerTask[] = [
  {
    id: 'task-1',
    title: 'Concrete Foundation Pour - Block A',
    description: 'Pour concrete for foundation of Block A residential building',
    category: 'Construction',
    location: 'Block A Foundation',
    siteId: 'site-1',
    createdBy: 'engineer-1',
    assignedWorkers: ['worker-1', 'worker-3', 'worker-4'],
    plannedDate: new Date(Date.now() + 24 * 60 * 60 * 1000), // Tomorrow
    estimatedHours: 8,
    priority: 'high',
    status: 'approved',
    requiresPermit: false,
    progressPercentage: 0,
    createdAt: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 1 * 60 * 60 * 1000)
  },
  {
    id: 'task-2',
    title: 'Electrical Panel Installation',
    description: 'Install main electrical distribution panel for Block B',
    category: 'Electrical',
    location: 'Block B Electrical Room',
    siteId: 'site-1',
    createdBy: 'engineer-1',
    assignedWorkers: ['worker-2'],
    plannedDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // Day after tomorrow
    estimatedHours: 6,
    priority: 'medium',
    status: 'submitted',
    requiresPermit: true,
    progressPercentage: 0,
    createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
  },
  {
    id: 'task-3',
    title: 'Structural Steel Welding',
    description: 'Weld structural steel beams for second floor framework',
    category: 'Welding',
    location: 'Block A Second Floor',
    siteId: 'site-1',
    createdBy: 'engineer-1',
    assignedWorkers: ['worker-1'],
    plannedDate: new Date(), // Today
    estimatedHours: 4,
    priority: 'critical',
    status: 'in-progress',
    requiresPermit: true,
    permitId: 'permit-1',
    progressPercentage: 65,
    actualStartTime: new Date(Date.now() - 3 * 60 * 60 * 1000),
    notes: 'Weather conditions good, proceeding as planned',
    createdAt: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 30 * 60 * 1000)
  },
  {
    id: 'task-4',
    title: 'Formwork Installation',
    description: 'Install formwork for concrete columns on ground floor',
    category: 'Construction',
    location: 'Block B Ground Floor',
    siteId: 'site-1',
    createdBy: 'engineer-1',
    assignedWorkers: ['worker-3', 'worker-4'],
    plannedDate: new Date(Date.now() - 24 * 60 * 60 * 1000), // Yesterday
    estimatedHours: 6,
    priority: 'medium',
    status: 'completed',
    requiresPermit: false,
    progressPercentage: 100,
    actualStartTime: new Date(Date.now() - 30 * 60 * 60 * 1000),
    actualEndTime: new Date(Date.now() - 24 * 60 * 60 * 1000),
    actualHours: 5.5,
    notes: 'Completed ahead of schedule, good weather conditions',
    createdAt: new Date(Date.now() - 4 * 24 * 60 * 60 * 1000),
    updatedAt: new Date(Date.now() - 24 * 60 * 60 * 1000)
  },
  {
    id: 'task-5',
    title: 'Safety Inspection - Scaffolding',
    description: 'Weekly safety inspection of scaffolding structures',
    category: 'Safety',
    location: 'All Blocks',
    siteId: 'site-1',
    createdBy: 'engineer-1',
    assignedWorkers: [],
    plannedDate: new Date(Date.now() + 3 * 24 * 60 * 60 * 1000),
    estimatedHours: 2,
    priority: 'high',
    status: 'draft',
    requiresPermit: false,
    progressPercentage: 0,
    createdAt: new Date(),
    updatedAt: new Date()
  }
];

export const mockTaskTemplates: TaskTemplate[] = [
  {
    id: 'template-1',
    name: 'Concrete Pour',
    description: 'Standard concrete pouring operation',
    category: 'Construction',
    estimatedHours: 8,
    requiredWorkers: 4,
    requiredSkills: ['Concrete work', 'Heavy machinery operation'],
    requiresPermit: false,
    riskLevel: 'medium',
    safetyRequirements: ['Hard hat', 'Safety boots', 'High-vis vest'],
    isActive: true
  },
  {
    id: 'template-2',
    name: 'Electrical Installation',
    description: 'Electrical wiring and equipment installation',
    category: 'Electrical',
    estimatedHours: 6,
    requiredWorkers: 2,
    requiredSkills: ['Electrical certification', 'LOTO procedures'],
    requiresPermit: true,
    riskLevel: 'high',
    safetyRequirements: ['Insulated gloves', 'Arc flash suit', 'Voltage tester'],
    isActive: true
  },
  {
    id: 'template-3',
    name: 'Hot Work - Welding',
    description: 'Welding operations requiring hot work permit',
    category: 'Welding',
    estimatedHours: 4,
    requiredWorkers: 2,
    requiredSkills: ['Welding certification', 'Hot work training'],
    requiresPermit: true,
    riskLevel: 'high',
    safetyRequirements: ['Welding helmet', 'Fire-resistant clothing', 'Fire extinguisher'],
    isActive: true
  },
  {
    id: 'template-4',
    name: 'Formwork Installation',
    description: 'Installation of concrete formwork',
    category: 'Construction',
    estimatedHours: 6,
    requiredWorkers: 3,
    requiredSkills: ['Carpentry', 'Construction experience'],
    requiresPermit: false,
    riskLevel: 'medium',
    safetyRequirements: ['Hard hat', 'Safety harness', 'Tool belt'],
    isActive: true
  },
  {
    id: 'template-5',
    name: 'Working at Height',
    description: 'Work activities above 2 meters',
    category: 'Construction',
    estimatedHours: 4,
    requiredWorkers: 2,
    requiredSkills: ['Height work certification', 'Fall protection'],
    requiresPermit: true,
    riskLevel: 'high',
    safetyRequirements: ['Safety harness', 'Hard hat', 'Fall arrest system'],
    isActive: true
  }
];
