 .u-section-1 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-1 .u-sheet-1 {
  min-height: 1066px;
}

.u-section-1 .u-text-1 {
  margin: 20px 0 0;
}

.u-section-1 .u-text-2 {
  font-weight: 500;
  margin: 20px 0 0;
}

.u-section-1 .u-layout-wrap-1 {
  margin-top: 50px;
  margin-bottom: 20px;
}

.u-section-1 .u-layout-cell-1 {
  min-height: 746px;
  --radius: 20px;
}

.u-section-1 .u-container-layout-1 {
  padding: 30px;
}

.u-section-1 .u-image-1 {
  width: 752px;
  height: 408px;
  --radius: 20px;
  transition-duration: 0.5s;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  margin: 0;
}

.u-section-1 .u-text-3 {
  font-weight: 500;
  margin: 30px 35px 0 0;
}

.u-section-1 .u-text-4 {
  font-weight: 300;
  margin: 17px 0 0;
}

.u-section-1 .u-layout-cell-2 {
  min-height: 335px;
  --radius: 30px;
}

.u-section-1 .u-container-layout-2 {
  padding: 20px;
}

.u-section-1 .u-image-2 {
  height: 343px;
  --radius: 20px;
  transition-duration: 0.5s;
  width: 324px;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  margin: 0 auto;
}

.u-section-1 .u-layout-cell-3 {
  min-height: 341px;
  --radius: 20px;
}

.u-section-1 .u-container-layout-3 {
  padding: 20px;
}

.u-section-1 .u-image-3 {
  height: 198px;
  --radius: 20px;
  transition-duration: 0.5s;
  width: 324px;
  transform: rotate(0deg) scale(1) translateX(0px) translateY(0px);
  margin: 0 auto;
}

.u-section-1 .u-text-5 {
  font-size: 1rem;
  margin: 30px auto 0 0;
}

@media (max-width: 1199px) {
  .u-section-1 .u-sheet-1 {
    min-height: 1016px;
  }

  .u-section-1 .u-layout-wrap-1 {
    margin-bottom: 14px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 732px;
    background-position: 50% 50%;
  }

  .u-section-1 .u-image-1 {
    width: 615px;
    height: 386px;
  }

  .u-section-1 .u-text-3 {
    margin-top: 20px;
    margin-right: 0;
  }

  .u-section-1 .u-text-4 {
    font-size: 1.125rem;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 396px;
  }

  .u-section-1 .u-image-2 {
    margin-right: initial;
    margin-left: initial;
    width: auto;
  }

  .u-section-1 .u-layout-cell-3 {
    min-height: 336px;
  }

  .u-section-1 .u-image-3 {
    margin-right: initial;
    margin-left: initial;
    width: auto;
  }

  .u-section-1 .u-text-5 {
    margin-top: 20px;
  }
}

@media (max-width: 991px) {
  .u-section-1 .u-sheet-1 {
    min-height: 537px;
  }

  .u-section-1 .u-layout-wrap-1 {
    margin-bottom: 20px;
    position: relative;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 679px;
  }

  .u-section-1 .u-image-1 {
    width: 465px;
    height: 246px;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 365px;
  }

  .u-section-1 .u-image-2 {
    height: 305px;
    width: auto;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-1 .u-layout-cell-3 {
    min-height: 314px;
  }

  .u-section-1 .u-image-3 {
    height: 161px;
    width: 191px;
    margin-left: auto;
    margin-right: auto;
  }
}

@media (max-width: 767px) {
  .u-section-1 .u-sheet-1 {
    min-height: 774px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 650px;
  }

  .u-section-1 .u-container-layout-1 {
    padding: 20px;
  }

  .u-section-1 .u-image-1 {
    height: 300px;
    margin-right: initial;
    margin-left: initial;
    width: auto;
  }

  .u-section-1 .u-text-4 {
    font-size: 1rem;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 529px;
  }

  .u-section-1 .u-image-2 {
    height: 484px;
    width: auto;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-1 .u-layout-cell-3 {
    min-height: 498px;
  }

  .u-section-1 .u-image-3 {
    height: 350px;
    width: 100%;
  }
}

@media (max-width: 575px) {
  .u-section-1 .u-sheet-1 {
    min-height: 583px;
  }

  .u-section-1 .u-layout-cell-1 {
    min-height: 100px;
  }

  .u-section-1 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-image-1 {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-1 .u-layout-cell-2 {
    min-height: 333px;
  }

  .u-section-1 .u-container-layout-2 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-image-2 {
    height: 310px;
    width: auto;
    margin-right: initial;
    margin-left: initial;
  }

  .u-section-1 .u-layout-cell-3 {
    min-height: 383px;
  }

  .u-section-1 .u-container-layout-3 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-1 .u-image-3 {
    height: 259px;
    width: 318px;
  }
}

.u-section-1 .u-image-3,
.u-section-1 .u-image-3:before,
.u-section-1 .u-image-3 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-1 :hover > .u-container-layout .u-image-3 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-1 .hover > .u-container-layout .u-image-3 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-block-398b-49:not([data-block-selected]):not([data-cell-selected]),
.u-block-398b-49:not([data-block-selected]):not([data-cell-selected]):before,
.u-block-398b-49:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

:hover:not([data-block-selected]):not([data-cell-selected]) > .u-container-layout > .u-block-398b-49 {
  transform: translateX(0px) translateY(-5px) !important;
}

.hover > .u-container-layout > .u-block-398b-49 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-1 .u-image-1,
.u-section-1 .u-image-1:before,
.u-section-1 .u-image-1 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-1 :hover > .u-container-layout .u-image-1 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-1 .hover > .u-container-layout .u-image-1 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-1 .u-image-2,
.u-section-1 .u-image-2:before,
.u-section-1 .u-image-2 > .u-container-layout:before {
  transition-property: fill, color, background-color, stroke-width, border-style, border-width, border-top-width, border-left-width, border-right-width, border-bottom-width, custom-border, borders, box-shadow, text-shadow, opacity, border-radius, stroke, border-color, font-size, font-style, font-weight, text-decoration, letter-spacing, transform, background-image, image-zoom, background-size, background-position;
}

.u-section-1 :hover > .u-container-layout .u-image-2 {
  transform: translateX(0px) translateY(-5px) !important;
}

.u-section-1 .hover > .u-container-layout .u-image-2 {
  transform: translateX(0px) translateY(-5px) !important;
} .u-section-2 {
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.5), rgba(249, 240, 248, 0.5));
  background-size: cover;
}

.u-section-2 .u-sheet-1 {
  min-height: 829px;
}

.u-section-2 .u-group-1 {
  --radius: 20px;
  min-height: 671px;
  height: auto;
  margin-top: 39px;
  margin-bottom: 60px;
  background-position: 50% 50%;
  background-repeat: no-repeat;
  background-image: linear-gradient(to bottom, rgba(249, 240, 248, 0.2), rgba(249, 240, 248, 0.2));
  background-size: cover;
}

.u-section-2 .u-container-layout-1 {
  padding: 30px;
}

.u-section-2 .u-text-1 {
  font-size: 2.25rem;
  margin: 0 auto 0 1px;
}

.u-section-2 .u-list-1 {
  margin-top: 21px;
  margin-right: 0;
  margin-bottom: 0;
}

.u-section-2 .u-repeater-1 {
  grid-auto-columns: 100%;
  grid-template-columns: repeat(1, 100%);
  min-height: 493px;
  --gap: 30px;
}

.u-section-2 .u-list-item-1 {
  --radius: 20px;
  box-shadow: 5px 5px 8px 0 rgba(224,229,235,0.4);
}

.u-section-2 .u-container-layout-2 {
  padding: 20px;
}

.u-section-2 .u-image-1 {
  width: 219px;
  --radius: 20px;
  height: 125px;
  margin: 0 auto 0 0;
}

.u-section-2 .u-text-2 {
  font-size: 1.5rem;
  margin: -106px auto 0 240px;
}

.u-section-2 .u-text-3 {
  font-weight: 300;
  margin: 19px 173px 0 239px;
}

.u-section-2 .u-list-item-2 {
  --radius: 20px;
  box-shadow: 5px 5px 8px 0 rgba(224,229,235,0.4);
}

.u-section-2 .u-container-layout-3 {
  padding: 20px;
}

.u-section-2 .u-image-2 {
  width: 219px;
  --radius: 20px;
  height: 125px;
  margin: 0 auto 0 0;
}

.u-section-2 .u-text-4 {
  font-size: 1.5rem;
  margin: -106px auto 0 240px;
}

.u-section-2 .u-text-5 {
  font-weight: 300;
  margin: 19px 173px 0 239px;
}

.u-section-2 .u-list-item-3 {
  --radius: 20px;
  box-shadow: 5px 5px 8px 0 rgba(224,229,235,0.4);
}

.u-section-2 .u-container-layout-4 {
  padding: 20px;
}

.u-section-2 .u-image-3 {
  width: 219px;
  --radius: 20px;
  height: 125px;
  margin: 0 auto 0 0;
}

.u-section-2 .u-text-6 {
  font-size: 1.5rem;
  margin: -106px auto 0 240px;
}

.u-section-2 .u-text-7 {
  font-weight: 300;
  margin: 19px 173px 0 239px;
}

@media (max-width: 1199px) {
  .u-section-2 .u-sheet-1 {
    min-height: 772px;
  }

  .u-section-2 .u-group-1 {
    margin-top: 30px;
    margin-bottom: 12px;
    height: auto;
  }

  .u-section-2 .u-list-1 {
    margin-right: initial;
  }

  .u-section-2 .u-repeater-1 {
    grid-template-columns: 100%;
    grid-gap: 30px;
  }

  .u-section-2 .u-container-layout-2 {
    padding-top: 19px;
    padding-bottom: 19px;
  }

  .u-section-2 .u-image-1 {
    margin-top: 1px;
  }

  .u-section-2 .u-text-2 {
    width: auto;
    margin-top: -115px;
    margin-left: 239px;
  }

  .u-section-2 .u-text-3 {
    width: auto;
    margin-top: 28px;
    margin-right: 3px;
  }

  .u-section-2 .u-container-layout-3 {
    padding-top: 19px;
    padding-bottom: 19px;
  }

  .u-section-2 .u-image-2 {
    margin-top: 1px;
  }

  .u-section-2 .u-text-4 {
    width: auto;
    margin-top: -115px;
    margin-left: 239px;
  }

  .u-section-2 .u-text-5 {
    width: auto;
    margin-top: 28px;
    margin-right: 3px;
  }

  .u-section-2 .u-container-layout-4 {
    padding-top: 19px;
    padding-bottom: 19px;
  }

  .u-section-2 .u-image-3 {
    margin-top: 1px;
  }

  .u-section-2 .u-text-6 {
    width: auto;
    margin-top: -115px;
    margin-left: 239px;
  }

  .u-section-2 .u-text-7 {
    width: auto;
    margin-top: 28px;
    margin-right: 3px;
  }
}

@media (max-width: 991px) {
  .u-section-2 .u-sheet-1 {
    min-height: 714px;
  }

  .u-section-2 .u-group-1 {
    margin-top: 20px;
    margin-bottom: 60px;
  }

  .u-section-2 .u-text-2 {
    margin-top: -125px;
    margin-right: 3px;
  }

  .u-section-2 .u-text-3 {
    margin-top: 21px;
  }

  .u-section-2 .u-text-4 {
    margin-top: -125px;
    margin-right: 3px;
  }

  .u-section-2 .u-text-5 {
    margin-top: 21px;
  }

  .u-section-2 .u-text-6 {
    margin-top: -125px;
    margin-right: 3px;
  }

  .u-section-2 .u-text-7 {
    margin-top: 21px;
  }
}

@media (max-width: 767px) {
  .u-section-2 .u-group-1 {
    min-height: 884px;
  }

  .u-section-2 .u-container-layout-1 {
    padding: 20px;
  }

  .u-section-2 .u-text-1 {
    font-size: 1.875rem;
  }

  .u-section-2 .u-repeater-1 {
    min-height: 735px;
  }

  .u-section-2 .u-container-layout-2 {
    padding: 10px;
  }

  .u-section-2 .u-image-1 {
    height: 94px;
    margin-top: 0;
  }

  .u-section-2 .u-text-2 {
    margin-top: -74px;
    margin-right: 0;
  }

  .u-section-2 .u-text-3 {
    margin-top: 36px;
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-2 .u-container-layout-3 {
    padding: 10px;
  }

  .u-section-2 .u-image-2 {
    height: 94px;
    margin-top: 0;
  }

  .u-section-2 .u-text-4 {
    margin-top: -74px;
    margin-right: 0;
  }

  .u-section-2 .u-text-5 {
    margin-top: 36px;
    margin-left: 0;
    margin-right: 0;
  }

  .u-section-2 .u-container-layout-4 {
    padding: 10px;
  }

  .u-section-2 .u-image-3 {
    height: 94px;
    margin-top: 0;
  }

  .u-section-2 .u-text-6 {
    margin-top: -74px;
    margin-right: 0;
  }

  .u-section-2 .u-text-7 {
    margin-top: 36px;
    margin-left: 0;
    margin-right: 0;
  }
}

@media (max-width: 575px) {
  .u-section-2 .u-container-layout-1 {
    padding-left: 10px;
    padding-right: 10px;
  }

  .u-section-2 .u-image-1 {
    width: 100%;
    height: 200px;
  }

  .u-section-2 .u-text-2 {
    font-size: 1.25rem;
    margin-top: 20px;
    margin-left: 1px;
    margin-right: 1px;
  }

  .u-section-2 .u-text-3 {
    margin-top: 20px;
    margin-left: 1px;
    margin-right: 1px;
  }

  .u-section-2 .u-image-2 {
    width: 100%;
    height: 200px;
  }

  .u-section-2 .u-text-4 {
    font-size: 1.25rem;
    margin-top: 20px;
    margin-left: 1px;
    margin-right: 1px;
  }

  .u-section-2 .u-text-5 {
    margin-top: 20px;
    margin-left: 1px;
    margin-right: 1px;
  }

  .u-section-2 .u-image-3 {
    width: 100%;
    height: 200px;
  }

  .u-section-2 .u-text-6 {
    font-size: 1.25rem;
    margin-top: 20px;
    margin-left: 1px;
    margin-right: 1px;
  }

  .u-section-2 .u-text-7 {
    margin-top: 20px;
    margin-left: 1px;
    margin-right: 1px;
  }
}