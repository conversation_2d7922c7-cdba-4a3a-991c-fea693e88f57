// Modal functionality
let modal = document.querySelector('.modal');
let currentProjectIndex = 0;

// Open project modal with details
function openProjectModal(projectId) {
  console.log('Opening modal for project ID:', projectId);
  
  // Find the project in the filtered projects array
  const projectIndex = filteredProjects.findIndex(p => p.id === parseInt(projectId));
  if (projectIndex === -1) {
    console.error('Project not found in filtered projects');
    return;
  }
  
  currentProjectIndex = projectIndex;
  const project = filteredProjects[currentProjectIndex];
  console.log('Found project:', project.name);
  
  // Populate modal content
  document.getElementById('modal-project-title').textContent = project.name;
  document.getElementById('modal-client').textContent = project.client;
  document.getElementById('modal-sector').textContent = project.sector;
  document.getElementById('modal-location').textContent = project.location;
  document.getElementById('modal-description').textContent = project.description;
  document.getElementById('modal-main-image').src = project.mainImage;
  document.getElementById('modal-main-image').alt = project.name;
  
  // Populate delivered items list
  const deliveredList = document.getElementById('modal-delivered-list');
  deliveredList.innerHTML = '';
  
  if (project.deliveredItems && project.deliveredItems.length > 0) {
    console.log('Adding', project.deliveredItems.length, 'delivered items');
    project.deliveredItems.forEach(item => {
      const li = document.createElement('li');
      li.textContent = item;
      deliveredList.appendChild(li);
    });
  } else {
    console.log('No delivered items found');
    const li = document.createElement('li');
    li.textContent = 'Information not available';
    deliveredList.appendChild(li);
  }
  
  // Populate gallery
  const galleryContainer = document.getElementById('modal-gallery');
  galleryContainer.innerHTML = '';
  
  if (project.additionalImages && project.additionalImages.length > 0) {
    // Limit to 6 images as per requirements
    const imagesToShow = project.additionalImages.slice(0, 6);
    console.log('Adding', imagesToShow.length, 'gallery images');
    
    imagesToShow.forEach(imageUrl => {
      const imgContainer = document.createElement('div');
      imgContainer.className = 'gallery-item';
      
      const img = document.createElement('img');
      img.src = imageUrl;
      img.alt = `${project.name} - Additional Image`;
      img.className = 'gallery-image';
      
      // Add click event to show full-size image
      img.addEventListener('click', () => {
        document.getElementById('modal-main-image').src = imageUrl;
      });
      
      imgContainer.appendChild(img);
      galleryContainer.appendChild(imgContainer);
    });
    
    galleryContainer.style.display = 'grid';
  } else {
    console.log('No additional images found');
    galleryContainer.style.display = 'none';
  }
  
  // Update navigation buttons
  updateNavigationButtons();
  
  // Store current scroll position
  const scrollY = window.scrollY;
  modal.dataset.scrollY = scrollY;
  console.log('Stored scroll position:', scrollY);
  
  // Show modal
  modal.classList.add('open');
  document.body.classList.add('modal-open');
  console.log('Modal opened');
  
  // Set focus to modal content for keyboard navigation
  setTimeout(() => {
    const modalContent = document.querySelector('.modal-content');
    if (modalContent) {
      modalContent.setAttribute('tabindex', '-1');
      modalContent.focus();
      console.log('Focus set to modal content');
    }
  }, 100);
}

// Close the modal
function closeProjectModal() {
  console.log('Closing modal');
  
  // Get stored scroll position
  const scrollY = modal.dataset.scrollY ? parseInt(modal.dataset.scrollY) : 0;
  
  // Hide modal
  modal.classList.remove('open');
  document.body.classList.remove('modal-open');
  
  // Restore scroll position
  window.scrollTo(0, scrollY);
  console.log('Restored scroll position to:', scrollY);
}

// Update navigation buttons in modal
function updateNavigationButtons() {
  const prevProject = getPrevProject();
  const nextProject = getNextProject();
  
  // Update previous button
  const prevBtn = document.getElementById('prev-project');
  if (prevProject) {
    prevBtn.style.visibility = 'visible';
    document.getElementById('prev-title').textContent = truncateTitle(prevProject.name);
  } else {
    prevBtn.style.visibility = 'hidden';
  }
  
  // Update next button
  const nextBtn = document.getElementById('next-project');
  if (nextProject) {
    nextBtn.style.visibility = 'visible';
    document.getElementById('next-title').textContent = truncateTitle(nextProject.name);
  } else {
    nextBtn.style.visibility = 'hidden';
  }
}

// Get previous project in filtered list
function getPrevProject() {
  if (currentProjectIndex > 0) {
    return filteredProjects[currentProjectIndex - 1];
  }
  return null;
}

// Get next project in filtered list
function getNextProject() {
  if (currentProjectIndex < filteredProjects.length - 1) {
    return filteredProjects[currentProjectIndex + 1];
  }
  return null;
}

// Navigate to previous project
function navigateToPrevProject() {
  if (currentProjectIndex > 0) {
    currentProjectIndex--;
    openProjectModal(filteredProjects[currentProjectIndex].id);
  }
}

// Navigate to next project
function navigateToNextProject() {
  if (currentProjectIndex < filteredProjects.length - 1) {
    currentProjectIndex++;
    openProjectModal(filteredProjects[currentProjectIndex].id);
  }
}

// Truncate title to first 3 words
function truncateTitle(title) {
  const words = title.split(' ');
  if (words.length <= 3) return title;
  return words.slice(0, 3).join(' ') + '...';
}

// Set up modal event listeners
function setupModalEventListeners() {
  // Close modal when clicking the close button
  const closeBtn = document.querySelector('.close-modal');
  if (closeBtn) {
    closeBtn.addEventListener('click', closeProjectModal);
  }
  
  // Close modal when clicking outside the modal content
  modal.addEventListener('click', (event) => {
    if (event.target === modal) {
      closeProjectModal();
    }
  });
  
  // Handle keyboard navigation
  document.addEventListener('keydown', (event) => {
    if (!modal.classList.contains('open')) return;
    
    if (event.key === 'Escape') {
      closeProjectModal();
    } else if (event.key === 'ArrowLeft') {
      navigateToPrevProject();
    } else if (event.key === 'ArrowRight') {
      navigateToNextProject();
    }
  });
  
  // Navigation buttons
  const prevBtn = document.getElementById('prev-project');
  const nextBtn = document.getElementById('next-project');
  
  if (prevBtn) prevBtn.addEventListener('click', navigateToPrevProject);
  if (nextBtn) nextBtn.addEventListener('click', navigateToNextProject);
}

// Initialize modal functionality
document.addEventListener('DOMContentLoaded', () => {
  modal = document.querySelector('.modal');
  if (modal) {
    setupModalEventListeners();
  }
});
