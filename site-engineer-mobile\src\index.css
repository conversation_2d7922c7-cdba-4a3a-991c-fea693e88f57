@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom CSS for Site Engineer Mobile App */
:root {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  line-height: 1.5;
  font-weight: 400;

  /* Mobile-optimized font smoothing */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* Reset default styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background-color: #f9fafb;
  color: #111827;
}

/* Mobile-specific optimizations */
@media (max-width: 768px) {
  /* Prevent zoom on input focus on iOS */
  input[type="text"],
  input[type="email"],
  input[type="password"],
  input[type="number"],
  input[type="tel"],
  textarea,
  select {
    font-size: 16px;
  }

  /* Improve touch targets */
  button,
  a,
  input,
  select,
  textarea {
    min-height: 44px;
  }
}

/* Custom utility classes */
.touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.safe-area-top {
  padding-top: env(safe-area-inset-top);
}

.safe-area-bottom {
  padding-bottom: env(safe-area-inset-bottom);
}
